"""
Integration tests for the audio agent.
"""

import pytest
import ray
import asyncio
from unittest.mock import patch, MagicMock, AsyncMock

from audio_agent.config import AudioAgentConfig
from audio_agent.core.session_manager import SessionManager


@pytest.fixture(scope="module")
def ray_context():
    """Initialize Ray for testing."""
    if not ray.is_initialized():
        ray.init(
            ignore_reinit_error=True,
            runtime_env={"working_dir": None}
        )
    yield


@pytest.fixture
def agent_config():
    """Create agent configuration for testing."""
    config = AudioAgentConfig()
    # Set test values
    config.websocket.app_id = "test_app_id"
    config.websocket.access_key = "test_access_key"
    return config


@pytest.mark.asyncio
async def test_session_manager_initialization(ray_context, agent_config):
    """Test SessionManager initialization."""
    session_manager = SessionManager(agent_config)
    
    assert session_manager.config == agent_config
    assert session_manager.running is False
    assert session_manager.session_id is not None
    assert session_manager.audio_input_actor is None
    assert session_manager.audio_output_actor is None
    assert session_manager.dialog_client_actor is None


@pytest.mark.asyncio
async def test_session_manager_start_stop_mock(ray_context, agent_config):
    """Test SessionManager start and stop with mocked components."""
    
    with patch('pyaudio.PyAudio') as mock_pyaudio, \
         patch('websockets.connect') as mock_websockets_connect:
        
        # Mock PyAudio
        mock_audio = MagicMock()
        mock_stream = MagicMock()
        mock_pyaudio.return_value = mock_audio
        mock_audio.open.return_value = mock_stream
        mock_stream.read.return_value = b'test_audio_data'
        
        # Mock WebSocket
        mock_websocket = AsyncMock()
        mock_websockets_connect.return_value = mock_websocket
        mock_websocket.recv.return_value = b'test_response'
        
        session_manager = SessionManager(agent_config)
        
        # Test start
        await session_manager.start()
        assert session_manager.running is True
        assert session_manager.audio_input_actor is not None
        assert session_manager.audio_output_actor is not None
        assert session_manager.dialog_client_actor is not None
        
        # Give some time for the processing loop to run
        await asyncio.sleep(0.1)
        
        # Test stop
        await session_manager.stop()
        assert session_manager.running is False


@pytest.mark.asyncio
async def test_session_manager_error_handling(ray_context, agent_config):
    """Test SessionManager error handling."""
    
    with patch('pyaudio.PyAudio') as mock_pyaudio:
        # Mock PyAudio to raise an exception
        mock_pyaudio.side_effect = Exception("Audio device not available")
        
        session_manager = SessionManager(agent_config)
        
        # Test that start handles errors gracefully
        with pytest.raises(Exception):
            await session_manager.start()
        
        # Ensure the session manager is not running after error
        assert session_manager.running is False


def test_session_manager_is_running(ray_context, agent_config):
    """Test SessionManager is_running method."""
    session_manager = SessionManager(agent_config)
    
    assert session_manager.is_running() is False
    
    session_manager.running = True
    assert session_manager.is_running() is True
    
    session_manager.running = False
    assert session_manager.is_running() is False


@pytest.mark.asyncio
async def test_full_pipeline_mock(ray_context, agent_config):
    """Test the full audio processing pipeline with mocks."""
    
    with patch('pyaudio.PyAudio') as mock_pyaudio, \
         patch('websockets.connect') as mock_websockets_connect:
        
        # Mock PyAudio
        mock_audio = MagicMock()
        mock_input_stream = MagicMock()
        mock_output_stream = MagicMock()
        mock_pyaudio.return_value = mock_audio
        
        # Configure different streams for input and output
        def mock_open(**kwargs):
            if kwargs.get('input'):
                return mock_input_stream
            else:
                return mock_output_stream
        
        mock_audio.open.side_effect = mock_open
        mock_input_stream.read.return_value = b'input_audio_data'
        
        # Mock WebSocket
        mock_websocket = AsyncMock()
        mock_websockets_connect.return_value = mock_websocket
        
        # Mock WebSocket responses
        mock_websocket.recv.side_effect = [
            b'start_connection_response',  # StartConnection response
            b'start_session_response',     # StartSession response
        ]
        
        session_manager = SessionManager(agent_config)
        
        try:
            # Start the session manager
            await session_manager.start()
            
            # Let it run for a short time
            await asyncio.sleep(0.2)
            
            # Verify that the components are working
            assert session_manager.running is True
            
            # Verify that audio input actor is recording
            is_recording = await session_manager.audio_input_actor.is_recording_active.remote()
            assert is_recording is True
            
            # Verify that audio output actor is playing
            is_playing = await session_manager.audio_output_actor.is_playback_active.remote()
            assert is_playing is True
            
            # Verify that dialog client is connected
            is_connected = await session_manager.dialog_client_actor.is_connection_active.remote()
            assert is_connected is True
            
        finally:
            # Always stop the session manager
            await session_manager.stop()
            
            # Verify everything is stopped
            assert session_manager.running is False
