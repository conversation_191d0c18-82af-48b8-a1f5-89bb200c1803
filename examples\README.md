# Audio Agent 使用示例

这个目录包含了Audio Agent的各种使用示例，从简单的基础用法到复杂的高级功能。

## 📁 示例文件

### [simple_usage.py](simple_usage.py)
**最简单的使用示例**

展示如何用最少的代码启动语音对话：
- 基本配置
- 简单的会话管理
- 错误处理

**运行方式**：
```bash
# 1. 修改文件中的API密钥
# 2. 运行示例
uv run python examples/simple_usage.py
```

**适合人群**：
- 初次使用者
- 快速原型开发
- 学习基本概念

### [advanced_usage.py](advanced_usage.py)
**高级功能演示**

展示完整的生产级使用方式：
- 自定义配置
- 状态监控
- 错误处理和重试
- 日志记录
- 性能优化

**运行方式**：
```bash
# 1. 修改文件中的API密钥
# 2. 运行示例
uv run python examples/advanced_usage.py
```

**适合人群**：
- 有经验的开发者
- 生产环境部署
- 需要监控和调试

## 🚀 快速开始

### 1. 准备API密钥

首先获取火山引擎的API密钥：
1. 访问[火山引擎控制台](https://console.volcengine.com/)
2. 开通语音技术服务
3. 获取App ID和Access Key

### 2. 选择示例

**新手推荐**：从`simple_usage.py`开始
```bash
uv run python examples/simple_usage.py
```

**进阶用户**：直接使用`advanced_usage.py`
```bash
uv run python examples/advanced_usage.py
```

### 3. 配置API密钥

有两种方式配置：

**方式一：修改代码**
```python
update_config(
    websocket={
        "app_id": "your_real_app_id",
        "access_key": "your_real_access_key"
    }
)
```

**方式二：环境变量**
```bash
export AUDIO_AGENT_WEBSOCKET__APP_ID="your_app_id"
export AUDIO_AGENT_WEBSOCKET__ACCESS_KEY="your_access_key"
```

## 📖 示例详解

### Simple Usage 示例

```python
# 最简配置
update_config(
    websocket={
        "app_id": "your_app_id",
        "access_key": "your_access_key"
    }
)

# 创建会话管理器
session_manager = SessionManager(get_config())

# 启动对话
await session_manager.start()
```

**特点**：
- 代码简洁，易于理解
- 使用默认配置
- 基本的错误处理
- 适合快速验证功能

### Advanced Usage 示例

```python
class AdvancedAudioAgent:
    def __init__(self, app_id, access_key, bot_name):
        # 详细配置
        self.configure_system()
        # 日志设置
        self.setup_logging()
    
    async def start(self, max_retries=3):
        # 支持重试的启动
        for attempt in range(max_retries):
            try:
                await self.session_manager.start()
                return True
            except Exception as e:
                # 指数退避重试
                await asyncio.sleep(2 ** attempt)
    
    async def monitor_status(self):
        # 实时状态监控
        while self.is_running():
            # 监控CPU、内存、Ray状态
            pass
```

**特点**：
- 完整的错误处理和重试机制
- 实时状态监控
- 详细的日志记录
- 生产级配置
- 性能优化

## 🎯 使用场景

### 场景1：快速原型验证
```bash
# 使用简单示例快速验证想法
uv run python examples/simple_usage.py
```

### 场景2：集成到现有项目
```python
# 参考advanced_usage.py的AdvancedAudioAgent类
from examples.advanced_usage import AdvancedAudioAgent

agent = AdvancedAudioAgent(app_id, access_key, "项目助手")
await agent.start()
```

### 场景3：自定义机器人角色
```python
# 技术助手
update_config(
    session={
        "dialog": {
            "bot_name": "技术专家",
            "system_role": "你是一个资深的软件工程师。",
            "speaking_style": "回答准确专业，提供具体解决方案。"
        }
    }
)

# 英语老师
update_config(
    session={
        "dialog": {
            "bot_name": "英语老师",
            "system_role": "你是一个专业的英语老师。",
            "speaking_style": "说话清晰缓慢，纠正发音错误。"
        }
    }
)
```

### 场景4：性能调优
```python
# 低延迟配置
update_config(
    input_audio={
        "chunk": 800,  # 更小的块大小
        "sample_rate": 16000
    },
    output_audio={
        "chunk": 1200,
        "sample_rate": 24000
    }
)

# 高并发配置
update_config(
    ray={
        "address": "auto",      # 自动启动本地集群
        "num_cpus": 8,         # 使用更多CPU
        "dashboard_port": 8265
    }
)

# 分布式配置
update_config(
    ray={
        "address": "ray://head-node:10001",  # 连接远程集群
        "dashboard_host": "0.0.0.0",        # 允许外部访问
        "dashboard_port": 8265
    }
)
```

## 🔧 自定义开发

### 基于示例创建自己的应用

1. **复制示例文件**
```bash
cp examples/advanced_usage.py my_voice_app.py
```

2. **修改配置**
```python
# 自定义您的配置
update_config(
    session={
        "dialog": {
            "bot_name": "我的专属助手",
            "system_role": "根据您的需求定制角色",
            "speaking_style": "定制说话风格"
        }
    }
)
```

3. **添加自定义功能**
```python
class MyVoiceApp(AdvancedAudioAgent):
    def __init__(self, app_id, access_key):
        super().__init__(app_id, access_key, "我的助手")
    
    async def custom_feature(self):
        # 添加您的自定义功能
        pass
```

### 集成到Web应用

```python
from fastapi import FastAPI
from examples.advanced_usage import AdvancedAudioAgent

app = FastAPI()
voice_agent = None

@app.on_event("startup")
async def startup():
    global voice_agent
    voice_agent = AdvancedAudioAgent(app_id, access_key)
    await voice_agent.start()

@app.on_event("shutdown")
async def shutdown():
    if voice_agent:
        await voice_agent.stop()
```

## 🐛 故障排除

### 常见问题

1. **API密钥错误**
```
❌ WebSocket连接失败: HTTP 401
```
**解决**：检查App ID和Access Key是否正确

2. **音频设备问题**
```
❌ 无法打开音频设备
```
**解决**：检查麦克风和扬声器权限

3. **Ray初始化失败**
```
❌ Ray初始化失败
```
**解决**：运行`ray stop`清理进程

### 调试技巧

1. **启用详细日志**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

2. **检查音频设备**
```python
import pyaudio
p = pyaudio.PyAudio()
for i in range(p.get_device_count()):
    print(p.get_device_info_by_index(i))
```

3. **监控Ray状态**
- 访问：http://localhost:8265
- 查看Actor状态和资源使用

## 📚 进一步学习

- **项目文档**：[../README.md](../README.md)
- **开发指南**：[../docs/DEVELOPMENT.md](../docs/DEVELOPMENT.md)
- **API文档**：[../docs/API.md](../docs/API.md)
- **常见问题**：[../docs/FAQ.md](../docs/FAQ.md)

## 🤝 贡献示例

欢迎贡献更多示例！如果您有有趣的使用场景，请：

1. 创建新的示例文件
2. 添加详细的注释和说明
3. 更新此README
4. 提交Pull Request

---

**祝您使用愉快！🎉**
