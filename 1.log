2025-07-27 10:10:43,060 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-27 10:10:43,060 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,060 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,060 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,060 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,060 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,060 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,061 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,061 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,061 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,061 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,061 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,061 - audio_agent.events.event_handler - INFO - TTS audio response: 38412 bytes
2025-07-27 10:10:43,061 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,067 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '？'}}        
2025-07-27 10:10:43,067 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:43,067 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:43,067 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:43,071 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38364bytes>', 'payload_msg': '<binary_data:38364bytes>'}
2025-07-27 10:10:43,071 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:43,072 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:43,072 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38364 bytes
2025-07-27 10:10:43,073 - audio_agent.events.event_handler - INFO - TTS audio response: 38364 bytes
2025-07-27 10:10:43,073 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38364 bytes)
2025-07-27 10:10:43,089 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38120bytes>', 'payload_msg': '<binary_data:38120bytes>'}
2025-07-27 10:10:43,089 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:43,089 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:43,089 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38120 bytes
2025-07-27 10:10:43,089 - audio_agent.events.event_handler - INFO - TTS audio response: 38120 bytes
2025-07-27 10:10:43,090 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38120 bytes)
2025-07-27 10:10:43,176 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:39000bytes>', 'payload_msg': '<binary_data:39000bytes>'}
2025-07-27 10:10:43,176 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:43,176 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:43,176 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 39000 bytes
2025-07-27 10:10:43,176 - audio_agent.events.event_handler - INFO - TTS audio response: 39000 bytes
2025-07-27 10:10:43,177 - audio_agent.core.session_manager - INFO - Audio response queued for playback (39000 bytes)
2025-07-27 10:10:43,247 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38380bytes>', 'payload_msg': '<binary_data:38380bytes>'}
2025-07-27 10:10:43,247 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:43,247 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:43,247 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38380 bytes
2025-07-27 10:10:43,248 - audio_agent.events.event_handler - INFO - TTS audio response: 38380 bytes
2025-07-27 10:10:43,249 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38380 bytes)
2025-07-27 10:10:43,338 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38716bytes>', 'payload_msg': '<binary_data:38716bytes>'}
2025-07-27 10:10:43,338 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:43,338 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:43,338 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38716 bytes
2025-07-27 10:10:43,339 - audio_agent.events.event_handler - INFO - TTS audio response: 38716 bytes
2025-07-27 10:10:43,340 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38716 bytes)
2025-07-27 10:10:43,408 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:28396bytes>', 'payload_msg': '<binary_data:28396bytes>'}
2025-07-27 10:10:43,408 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:43,408 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:43,408 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 28396 bytes
2025-07-27 10:10:43,408 - audio_agent.events.event_handler - INFO - TTS audio response: 28396 bytes
2025-07-27 10:10:43,409 - audio_agent.core.session_manager - INFO - Audio response queued for playback (28396 bytes)
2025-07-27 10:10:43,427 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {}}
2025-07-27 10:10:43,427 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:43,427 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:43,428 - audio_agent.events.event_handler - INFO - TTS sentence ended
2025-07-27 10:10:46,294 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 10:10:48,609 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 450, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'asr_task_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae_6_3', 'question_id': '13700027810365186', 'round_id': 8}}
2025-07-27 10:10:48,609 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 450 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:48,609 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:48,609 - audio_agent.events.event_handler - INFO - ASR first word detected - can interrupt playback
2025-07-27 10:10:48,613 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9100622534751892, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 5.692004, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在'}], 'end_time': 5.692004, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在'}]}}
2025-07-27 10:10:48,613 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:48,613 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:48,613 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:48,613 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:48,613 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:48,613 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在', interim: True        
2025-07-27 10:10:48,613 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在'       
2025-07-27 10:10:48,650 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9100622534751892, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 5.762006, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在'}], 'end_time': 5.762006, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在'}]}}
2025-07-27 10:10:48,650 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:48,650 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:48,650 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:48,650 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:48,650 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:48,650 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在', interim: True        
2025-07-27 10:10:48,650 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在'       
2025-07-27 10:10:48,720 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9100622534751892, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 5.852008, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在'}], 'end_time': 5.852008, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在'}]}}
2025-07-27 10:10:48,720 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:48,720 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:48,720 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:48,720 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:48,720 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:48,720 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在', interim: True        
2025-07-27 10:10:48,720 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在'       
2025-07-27 10:10:48,782 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9355105757713318, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 5.922009, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的'}], 'end_time': 5.922009, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的'}]}}
2025-07-27 10:10:48,782 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:48,782 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:48,782 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:48,783 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:48,783 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:48,783 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的', interim: True      
2025-07-27 10:10:48,783 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的'     
2025-07-27 10:10:48,860 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9355105757713318, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.012012, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的'}], 'end_time': 6.012012, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的'}]}}
2025-07-27 10:10:48,860 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:48,860 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:48,860 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:48,860 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:48,860 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:48,860 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的', interim: True      
2025-07-27 10:10:48,860 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的'     
2025-07-27 10:10:48,919 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9355105757713318, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.082013, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的'}], 'end_time': 6.082013, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的'}]}}
2025-07-27 10:10:48,919 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:48,919 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:48,919 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:48,919 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:48,919 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:48,919 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的', interim: True      
2025-07-27 10:10:48,919 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的'     
2025-07-27 10:10:49,009 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9355105757713318, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.172015, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的'}], 'end_time': 6.172015, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的'}]}}
2025-07-27 10:10:49,009 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,009 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,009 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,009 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,009 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,009 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的', interim: True      
2025-07-27 10:10:49,009 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的'     
2025-07-27 10:10:49,086 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.909774661064148, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在的时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.242017, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间'}], 'end_time': 6.242017, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间'}]}}
2025-07-27 10:10:49,087 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,087 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,087 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,087 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,087 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,087 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间', interim: True  
2025-07-27 10:10:49,087 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间' 
2025-07-27 10:10:49,169 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.909774661064148, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在的时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.332019, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间'}], 'end_time': 6.332019, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间'}]}}
2025-07-27 10:10:49,169 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,169 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,169 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,169 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,169 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,170 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间', interim: True  
2025-07-27 10:10:49,170 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间' 
2025-07-27 10:10:49,209 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9209082722663879, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.40202, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是'}], 'end_time': 6.40202, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是'}]}}
2025-07-27 10:10:49,209 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,209 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,209 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,209 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,209 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,210 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是', interim: True
2025-07-27 10:10:49,210 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是'
2025-07-27 10:10:49,357 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9209082722663879, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.492023, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是'}], 'end_time': 6.492023, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是'}]}}
2025-07-27 10:10:49,357 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,357 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,357 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,357 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,357 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,357 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是', interim: True
2025-07-27 10:10:49,357 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是'
2025-07-27 10:10:49,388 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9209082722663879, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.562024, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是'}], 'end_time': 6.562024, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是'}]}}
2025-07-27 10:10:49,389 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,389 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,389 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,389 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,389 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,389 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是', interim: True
2025-07-27 10:10:49,389 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是'
2025-07-27 10:10:49,476 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.9209082722663879, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.652026, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是'}], 'end_time': 6.652026, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是'}]}}
2025-07-27 10:10:49,477 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,477 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,477 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,477 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,477 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,477 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是', interim: True
2025-07-27 10:10:49,477 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是'
2025-07-27 10:10:49,547 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8758888244628906, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.722028, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少'}], 'end_time': 6.722028, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是多少'}]}}
2025-07-27 10:10:49,547 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,547 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,547 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,547 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,547 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,547 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少', interim: True
2025-07-27 10:10:49,547 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少'
2025-07-27 10:10:49,633 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8758888244628906, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.81203, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少'}], 'end_time': 6.81203, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是多少'}]}}
2025-07-27 10:10:49,633 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,633 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,634 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,634 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,634 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,634 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少', interim: True
2025-07-27 10:10:49,635 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少'
2025-07-27 10:10:49,726 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 59.99896389770498}, 'results': [{'alternatives': [{'end_time': 6.882031, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 6.882031, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是 多少呀'}]}}
2025-07-27 10:10:49,727 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,727 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,727 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,727 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,727 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,727 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:49,727 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:49,727 - audio_agent.events.event_processor - INFO - 🔧 Early tool detection: '现在的时间是多少呀' likely needs tools
2025-07-27 10:10:49,729 - audio_agent.events.event_processor - INFO - ✅ Early placeholder ChatTTSText sent during ASR interim
2025-07-27 10:10:49,890 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'prefetch': True, 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 150.00197750854483}, 'results': [{'alternatives': [{'end_time': 6.972034, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀？'}], 'end_time': 6.972034, 'index': 0, 'is_interim': True, 'is_soft_finished': True, 'is_vad_timeout': False, 'soft_vad_type': 999, 'start_time': 5.012, 'text': '现在的时间是多少呀？'}]}}
2025-07-27 10:10:49,890 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,890 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,890 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,890 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,890 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,890 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀？', interim: True
2025-07-27 10:10:49,890 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀？'
2025-07-27 10:10:49,928 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 220.00310284423819}, 'results': [{'alternatives': [{'end_time': 7.042035, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.042035, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是多少呀'}]}}
2025-07-27 10:10:49,928 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,928 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,928 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,928 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,928 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,928 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:49,928 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:49,963 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 300.00116278076126}, 'results': [{'alternatives': [{'end_time': 7.132037, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.132037, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是多少呀'}]}}
2025-07-27 10:10:49,963 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:49,963 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:49,963 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:49,964 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:49,964 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:49,964 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:49,964 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:50,048 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 359.9997649536135}, 'results': [{'alternatives': [{'end_time': 7.202039, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.202039, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是 多少呀'}]}}
2025-07-27 10:10:50,048 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,048 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,048 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:50,048 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:50,048 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:50,048 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:50,048 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:50,146 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 450.00182489013696}, 'results': [{'alternatives': [{'end_time': 7.292041, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.292041, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是多少呀'}]}}
2025-07-27 10:10:50,146 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,146 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,146 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:50,146 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:50,146 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:50,146 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:50,146 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:50,191 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 520.0029502258303}, 'results': [{'alternatives': [{'end_time': 7.362042, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.362042, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是 多少呀'}]}}
2025-07-27 10:10:50,191 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,191 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,191 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:50,191 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:50,191 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:50,191 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:50,191 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:50,312 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 610.0050101623538}, 'results': [{'alternatives': [{'end_time': 7.452044, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.452044, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是 多少呀'}]}}
2025-07-27 10:10:50,312 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,312 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,312 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:50,312 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:50,312 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:50,312 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:50,312 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:50,345 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 680.0070891723635}, 'results': [{'alternatives': [{'end_time': 7.522046, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.522046, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是 多少呀'}]}}
2025-07-27 10:10:50,345 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,345 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,345 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:50,345 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:50,345 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:50,345 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:50,345 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:50,479 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 770.009149108887}, 'results': [{'alternatives': [{'end_time': 7.612048, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.612048, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是多少呀'}]}}
2025-07-27 10:10:50,479 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,479 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,479 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:50,479 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:50,479 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:50,479 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:50,479 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:50,509 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'interrupt_score': 0.8712310194969177, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现 在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 840.0112281188967}, 'results': [{'alternatives': [{'end_time': 7.68205, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀'}], 'end_time': 7.68205, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是多 少呀'}]}}
2025-07-27 10:10:50,509 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,509 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,509 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:50,509 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:50,509 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:50,509 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀', interim: True
2025-07-27 10:10:50,509 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在的时间是多少呀'
2025-07-27 10:10:50,646 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.052671559154987335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在的时间是多少呀', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '现在的时间是多少呀？', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 919.998811218262}, 'results': [{'alternatives': [{'end_time': 7.772052, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 5.012, 'text': '现在的时间是多少呀？'}], 'end_time': 7.772052, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 5.012, 'text': '现在的时间是多少呀？'}]}}
2025-07-27 10:10:50,646 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,646 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,646 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:10:50,646 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:10:50,646 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:10:50,646 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在的时间是多少呀？', interim: False
2025-07-27 10:10:50,646 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '现在的时间是多少呀？'
2025-07-27 10:10:50,651 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 1014, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 6853}}
2025-07-27 10:10:50,651 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,651 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,652 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-27 10:10:50,652 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-27 10:10:50,652 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '现在的时间是多少呀？'
2025-07-27 10:10:50,652 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '现在的时间是多少呀？'
2025-07-27 10:10:50,654 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
2025-07-27 10:10:50,654 - audio_agent.events.llm_enhancer - INFO - ✅ Detected actual time query: '现在的时间是 多少呀？'
2025-07-27 10:10:50,654 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'datetime_query', 'confidence': 0.9, 'requires_tools': True, 'tools': ['datetime'], 'parameters': {}}
2025-07-27 10:10:50,654 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'datetime_query', 'confidence': 0.9, 'requires_tools': True, 'tools': ['datetime'], 'parameters': {}}
2025-07-27 10:10:50,654 - audio_agent.events.event_processor - INFO - 🔧 Tools needed, sending placeholder first: ['datetime']
2025-07-27 10:10:50,654 - audio_agent.events.event_processor - INFO - ✅ Immediate placeholder already sent at ASR end, proceeding with tool execution
2025-07-27 10:10:50,654 - audio_agent.events.event_processor - INFO - 🔧 Executing tools: ['datetime']
2025-07-27 10:10:50,654 - audio_agent.tools.executor - INFO - Executing tool: datetime with parameters: {'operation': 'current_time'}
2025-07-27 10:10:50,654 - audio_agent.tools.builtin.datetime_tool - INFO - DateTime operation: current_time     
2025-07-27 10:10:50,654 - audio_agent.tools.executor - INFO - Tool datetime executed successfully
2025-07-27 10:10:50,654 - audio_agent.events.llm_enhancer - INFO - Tool datetime executed successfully
2025-07-27 10:10:50,659 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae_8_1', 'tts_type': 'default', 'v3_loundness_params': ''}}
2025-07-27 10:10:50,659 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,660 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,680 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 10:10:50,680 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:50,680 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:50,680 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 10:10:50,681 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-27 10:10:50,694 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 10:10:50,697 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38412bytes>', 'payload_msg': '<binary_data:38412bytes>'}
2025-07-27 10:10:50,697 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:50,697 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:50,697 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38412 bytes
2025-07-27 10:10:50,698 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38412 bytes)
2025-07-27 10:10:50,714 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38764bytes>', 'payload_msg': '<binary_data:38764bytes>'}
2025-07-27 10:10:50,714 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:50,715 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:50,715 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38764 bytes
2025-07-27 10:10:50,715 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38764 bytes)
2025-07-27 10:10:50,731 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': ''}}
2025-07-27 10:10:50,731 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,731 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,748 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '现在'}}      
2025-07-27 10:10:50,748 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,748 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,763 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '是'}}        
2025-07-27 10:10:50,763 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,763 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,780 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '下午'}}      
2025-07-27 10:10:50,780 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,780 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,795 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '，'}}        
2025-07-27 10:10:50,795 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,795 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,812 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '嗯'}}        
2025-07-27 10:10:50,812 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,812 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,827 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '，准确的说是'}}
2025-07-27 10:10:50,827 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,827 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,845 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '十五'}}      
2025-07-27 10:10:50,845 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,845 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,859 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '点'}}        
2025-07-27 10:10:50,860 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,860 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,876 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '三十二'}}    
2025-07-27 10:10:50,876 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,877 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,893 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '分'}}        
2025-07-27 10:10:50,893 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,893 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,918 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38392bytes>', 'payload_msg': '<binary_data:38392bytes>'}
2025-07-27 10:10:50,918 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:50,919 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:50,919 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38392 bytes
2025-07-27 10:10:50,921 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38392 bytes)
2025-07-27 10:10:50,939 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'content': '。'}}        
2025-07-27 10:10:50,939 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:50,940 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:50,956 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:37804bytes>', 'payload_msg': '<binary_data:37804bytes>'}
2025-07-27 10:10:50,956 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:50,956 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:50,956 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37804 bytes
2025-07-27 10:10:50,957 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37804 bytes)
2025-07-27 10:10:50,989 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38412bytes>', 'payload_msg': '<binary_data:38412bytes>'}
2025-07-27 10:10:50,989 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:50,989 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:50,989 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38412 bytes
2025-07-27 10:10:50,990 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38412 bytes)
2025-07-27 10:10:51,106 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38076bytes>', 'payload_msg': '<binary_data:38076bytes>'}
2025-07-27 10:10:51,106 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:51,106 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:51,106 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38076 bytes
2025-07-27 10:10:51,107 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38076 bytes)
2025-07-27 10:10:51,146 - audio_agent.events.event_processor - INFO - 🔧 Tool result ready to send to server:
2025-07-27 10:10:51,146 - audio_agent.events.event_processor - INFO -    User input: 现在的时间是多少呀？
2025-07-27 10:10:51,146 - audio_agent.events.event_processor - INFO -    Tool results: 1 tools executed
2025-07-27 10:10:51,146 - audio_agent.events.event_processor - INFO -    AI response: 现在是上午10点10分        
2025-07-27 10:10:51,153 - audio_agent.events.event_processor - INFO - ✅ Tool result sent to server successfully using streaming ChatTTSText
2025-07-27 10:10:51,153 - audio_agent.events.event_processor - INFO - 🔧 Tool processing complete, but keeping tool_calling_active=True to filter server responses
2025-07-27 10:10:51,153 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 10:10:51,153 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: a822b052-9be7-4b26-bf47-3eef90a4a9ae_8_1
2025-07-27 10:10:51,153 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True 
2025-07-27 10:10:51,153 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 10:10:51,153 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - TTS audio response: 38412 bytes
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - TTS audio response: 38764 bytes
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,154 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - TTS audio response: 38392 bytes
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - TTS audio response: 37804 bytes
2025-07-27 10:10:51,155 - audio_agent.events.event_handler - INFO - TTS audio response: 38412 bytes
2025-07-27 10:10:51,156 - audio_agent.events.event_handler - INFO - TTS audio response: 38076 bytes
2025-07-27 10:10:51,221 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38076bytes>', 'payload_msg': '<binary_data:38076bytes>'}
2025-07-27 10:10:51,221 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:51,221 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:51,221 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38076 bytes
2025-07-27 10:10:51,222 - audio_agent.events.event_handler - INFO - TTS audio response: 38076 bytes
2025-07-27 10:10:51,222 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38076 bytes)
2025-07-27 10:10:51,336 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38776bytes>', 'payload_msg': '<binary_data:38776bytes>'}
2025-07-27 10:10:51,338 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:51,338 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:51,338 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38776 bytes
2025-07-27 10:10:51,338 - audio_agent.events.event_handler - INFO - TTS audio response: 38776 bytes
2025-07-27 10:10:51,339 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38776 bytes)
2025-07-27 10:10:51,401 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:37836bytes>', 'payload_msg': '<binary_data:37836bytes>'}
2025-07-27 10:10:51,401 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:51,401 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:51,401 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37836 bytes
2025-07-27 10:10:51,401 - audio_agent.events.event_handler - INFO - TTS audio response: 37836 bytes
2025-07-27 10:10:51,403 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37836 bytes)
2025-07-27 10:10:51,520 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38828bytes>', 'payload_msg': '<binary_data:38828bytes>'}
2025-07-27 10:10:51,520 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:51,520 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:51,520 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38828 bytes
2025-07-27 10:10:51,520 - audio_agent.events.event_handler - INFO - TTS audio response: 38828 bytes
2025-07-27 10:10:51,521 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38828 bytes)
2025-07-27 10:10:51,565 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'enable_v3_loudness_balance': True, 'gta': True, 'model_type': 'v3', 'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":45,"flushed":true,"markdown":{"prev_parsed_text":"现在是上午10点10分\\n","prev_t...<truncated:509chars>', 'text': '现在是上午10点10分。', 'tts_task_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae_9_0', 'tts_type': 'chat_tts_text', 'v3_loundness_params': ''}}
2025-07-27 10:10:51,565 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:51,566 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:51,566 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 10:10:51,566 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: chat_tts_text, text: '现在是上午10点10分。', task_id: a822b052-9be7-4b26-bf47-3eef90a4a9ae_9_0
2025-07-27 10:10:51,566 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True 
2025-07-27 10:10:51,566 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 10:10:51,566 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 10:10:51,566 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 10:10:51,566 - audio_agent.events.event_handler - INFO - TTS sentence start - type: chat_tts_text, text: '现在是上午10点10分。'
2025-07-27 10:10:52,151 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 10:10:52,151 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:52,151 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:52,151 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 10:10:52,152 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 10:10:52,153 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-27 10:10:52,190 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38668bytes>', 'payload_msg': '<binary_data:38668bytes>'}
2025-07-27 10:10:52,190 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:52,190 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:52,190 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38668 bytes
2025-07-27 10:10:52,191 - audio_agent.events.event_handler - INFO - TTS audio response: 38668 bytes
2025-07-27 10:10:52,191 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38668 bytes)
2025-07-27 10:10:52,340 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:38668bytes>', 'payload_msg': '<binary_data:38668bytes>'}
2025-07-27 10:10:52,340 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:52,340 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:52,340 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38668 bytes
2025-07-27 10:10:52,340 - audio_agent.events.event_handler - INFO - TTS audio response: 38668 bytes
2025-07-27 10:10:52,341 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38668 bytes)
2025-07-27 10:10:52,453 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:37804bytes>', 'payload_msg': '<binary_data:37804bytes>'}
2025-07-27 10:10:52,453 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:52,453 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:52,453 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37804 bytes
2025-07-27 10:10:52,453 - audio_agent.events.event_handler - INFO - TTS audio response: 37804 bytes
2025-07-27 10:10:52,454 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37804 bytes)
2025-07-27 10:10:52,538 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:37948bytes>', 'payload_msg': '<binary_data:37948bytes>'}
2025-07-27 10:10:52,539 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:52,539 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:52,539 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37948 bytes
2025-07-27 10:10:52,539 - audio_agent.events.event_handler - INFO - TTS audio response: 37948 bytes
2025-07-27 10:10:52,539 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37948 bytes)
2025-07-27 10:10:52,609 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': '<binary_data:28776bytes>', 'payload_msg': '<binary_data:28776bytes>'}
2025-07-27 10:10:52,609 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:10:52,609 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:10:52,609 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 28776 bytes
2025-07-27 10:10:52,610 - audio_agent.events.event_handler - INFO - TTS audio response: 28776 bytes
2025-07-27 10:10:52,611 - audio_agent.core.session_manager - INFO - Audio response queued for playback (28776 bytes)
2025-07-27 10:10:52,628 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":45,"flushed":true,"markdown":{"prev_parsed_text":"现在是上午10点10分\\n","prev_t...<truncated:509chars>', 'speech_alignment_result': '', 'text': '现在是上午10点10分。'}}
2025-07-27 10:10:52,628 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:52,628 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:52,628 - audio_agent.events.event_handler - INFO - TTS sentence ended
2025-07-27 10:10:52,632 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': 'a822b052-9be7-4b26-bf47-3eef90a4a9ae', 'payload': {'no_content': False}}    
2025-07-27 10:10:52,632 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:10:52,632 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:10:52,632 - audio_agent.events.event_handler - INFO - TTS synthesis ended
2025-07-27 10:10:56,951 - audio_agent.core.session_manager - INFO - AI finished speaking