复一遍'}], 'end_time': 5.692038, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}]}}
2025-07-27 10:14:12,982 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:12,982 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:12,982 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:12,982 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:12,982 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:12,982 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀你重复一遍', interim: True
2025-07-27 10:14:12,982 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '刚才我问你的时间呀你重复一遍'
2025-07-27 10:14:13,080 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9569185376167297, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '刚 才我问你的时间呀你重复一遍', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 730.0038712158204}, 'results': [{'alternatives': [{'end_time': 5.79204, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}], 'end_time': 5.79204, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}]}}
2025-07-27 10:14:13,080 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,080 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,080 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:13,080 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:13,080 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:13,080 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀你重复一遍', interim: True
2025-07-27 10:14:13,080 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '刚才我问你的时间呀你重复一遍'
2025-07-27 10:14:13,149 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9569185376167297, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '刚 才我问你的时间呀你重复一遍', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 790.0047676696778}, 'results': [{'alternatives': [{'end_time': 5.852041, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重 复一遍'}], 'end_time': 5.852041, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}]}}
2025-07-27 10:14:13,149 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,149 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,149 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:13,149 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:13,149 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:13,149 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀你重复一遍', interim: True
2025-07-27 10:14:13,149 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '刚才我问你的时间呀你重复一遍'
2025-07-27 10:14:13,226 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9569185376167297, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '刚 才我问你的时间呀你重复一遍', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 890.0080101623536}, 'results': [{'alternatives': [{'end_time': 5.952044, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重 复一遍'}], 'end_time': 5.952044, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}]}}
2025-07-27 10:14:13,226 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,226 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,226 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:13,226 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:13,226 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:13,226 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀你重复一遍', interim: True
2025-07-27 10:14:13,226 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '刚才我问你的时间呀你重复一遍'
2025-07-27 10:14:13,308 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9569185376167297, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '刚 才我问你的时间呀你重复一遍', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 950.008906616211}, 'results': [{'alternatives': [{'end_time': 6.012045, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}], 'end_time': 6.012045, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}]}}
2025-07-27 10:14:13,308 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,308 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,308 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:13,308 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:13,308 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:13,308 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀你重复一遍', interim: True
2025-07-27 10:14:13,308 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '刚才我问你的时间呀你重复一遍'
2025-07-27 10:14:13,417 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9569185376167297, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '刚 才我问你的时间呀你重复一遍', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1050.0111954345703}, 'results': [{'alternatives': [{'end_time': 6.112047, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}], 'end_time': 6.112047, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}]}}
2025-07-27 10:14:13,417 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,417 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,417 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:13,417 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:13,417 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:13,417 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀你重复一遍', interim: True
2025-07-27 10:14:13,417 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '刚才我问你的时间呀你重复一遍'
2025-07-27 10:14:13,495 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9569185376167297, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '刚 才我问你的时间呀你重复一遍', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1110.013045562744}, 'results': [{'alternatives': [{'end_time': 6.172049, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重 复一遍'}], 'end_time': 6.172049, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}]}}
2025-07-27 10:14:13,495 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,495 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,495 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:13,495 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:13,495 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:13,495 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀你重复一遍', interim: True
2025-07-27 10:14:13,495 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '刚才我问你的时间呀你重复一遍'
2025-07-27 10:14:13,560 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9569185376167297, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '刚 才我问你的时间呀你重复一遍', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1210.0148575439453}, 'results': [{'alternatives': [{'end_time': 6.272051, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}], 'end_time': 6.272051, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀你重复一遍'}]}}
2025-07-27 10:14:13,560 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,560 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,560 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:13,560 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:13,560 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:13,560 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀你重复一遍', interim: True
2025-07-27 10:14:13,560 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '刚才我问你的时间呀你重复一遍'
2025-07-27 10:14:13,660 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.15521155297756195, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '刚才我问你的时间呀你重复一遍', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '刚才我问你的时间呀？你重复一遍。', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1249.9637978210449}, 'results': [{'alternatives': [{'end_time': 6.312, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.472, 'text': '刚才我问你的时间呀？你 重复一遍。'}], 'end_time': 6.312, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 2.472, 'text': '刚才我问你的时间呀？你重复一遍。'}]}}
2025-07-27 10:14:13,660 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,660 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,660 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:13,660 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:13,660 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:13,660 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '刚才我问你的时间呀？你重复一遍。', interim: False
2025-07-27 10:14:13,660 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '刚才我问你的时间呀？你重复一遍。'
2025-07-27 10:14:13,665 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 1543, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 4887}}
2025-07-27 10:14:13,666 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,668 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,668 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-27 10:14:13,668 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-27 10:14:13,668 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '刚才我问你的时间呀？你重复一遍。'
2025-07-27 10:14:13,668 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '刚才我问你的时间呀？你重复一遍。'
2025-07-27 10:14:13,668 - audio_agent.events.event_processor - INFO - 🤔 Time mentioned in conversation context, skipping tool activation: '刚才我问你的时间呀？你重复一遍。'
2025-07-27 10:14:13,671 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
2025-07-27 10:14:13,672 - audio_agent.events.llm_enhancer - INFO - 🤔 Time mentioned in conversation context, not a query: '刚才我问你的时间呀？你重复一遍。'
2025-07-27 10:14:13,673 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.8, 'requires_tools': False, 'tools': []}
2025-07-27 10:14:13,673 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.8, 'requires_tools': False, 'tools': []}
2025-07-27 10:14:13,677 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31_8_1', 'tts_type': 'default', 'v3_loundness_params': ''}}
2025-07-27 10:14:13,680 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,680 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,694 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 10:14:13,694 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:13,695 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:13,695 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 10:14:13,696 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-27 10:14:13,709 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 10:14:13,714 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38684bytes>', 'payload_msg': '<binary_data:38684bytes>'}
2025-07-27 10:14:13,714 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:13,714 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:13,714 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38684 bytes
2025-07-27 10:14:13,715 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38684 bytes)
2025-07-27 10:14:13,732 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38360bytes>', 'payload_msg': '<binary_data:38360bytes>'}
2025-07-27 10:14:13,733 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:13,733 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:13,733 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38360 bytes
2025-07-27 10:14:13,734 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38360 bytes)
2025-07-27 10:14:13,750 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38408bytes>', 'payload_msg': '<binary_data:38408bytes>'}
2025-07-27 10:14:13,750 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:13,750 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:13,750 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38408 bytes
2025-07-27 10:14:13,751 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38408 bytes)
2025-07-27 10:14:13,864 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38376bytes>', 'payload_msg': '<binary_data:38376bytes>'}
2025-07-27 10:14:13,864 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:13,864 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:13,864 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38376 bytes
2025-07-27 10:14:13,865 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38376 bytes)
2025-07-27 10:14:13,881 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38444bytes>', 'payload_msg': '<binary_data:38444bytes>'}
2025-07-27 10:14:13,881 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:13,881 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:13,881 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38444 bytes
2025-07-27 10:14:13,883 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38444 bytes)
2025-07-27 10:14:13,899 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38764bytes>', 'payload_msg': '<binary_data:38764bytes>'}
2025-07-27 10:14:13,899 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:13,900 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:13,900 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38764 bytes
2025-07-27 10:14:13,901 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38764 bytes)
2025-07-27 10:14:13,917 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {}}
2025-07-27 10:14:13,917 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,917 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,933 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': ''}}
2025-07-27 10:14:13,933 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,934 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,950 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '现在'}}      
2025-07-27 10:14:13,950 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,950 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,961 - audio_agent.events.event_handler - INFO - LLM result: {'user_input': '刚才我问你的时间呀？你重复一遍。', 'ai_response': "我理解您说的是'刚才我问你的时间呀？你重复一遍。'。我可以为您做些什么吗？"}   
2025-07-27 10:14:13,961 - audio_agent.events.event_processor - INFO - LLM response ready for TTS: 我理解您说的是'刚才我问你的时间呀？你重复一遍。'。我可以为您做些什么吗？
2025-07-27 10:14:13,961 - audio_agent.events.event_handler - INFO - TTS audio response: 127 bytes
2025-07-27 10:14:13,961 - audio_agent.events.event_processor - INFO - Mock TTS response created for: '我理解您说的是'刚才我问你的时间呀？你重复一遍。'。我可以为您做些什么吗？...'
2025-07-27 10:14:13,961 - audio_agent.events.event_processor - INFO - LLM enhanced response sent to user successfully
2025-07-27 10:14:13,962 - audio_agent.events.event_processor - INFO - Response generated: '我理解您说的是'刚才我问你的时间呀？你重复一遍。'。我可以为您做些什么吗？'
2025-07-27 10:14:13,962 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 10:14:13,962 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: 8a980f11-ade6-4c3a-ba6c-a3db123acb31_8_1
2025-07-27 10:14:13,962 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True 
2025-07-27 10:14:13,962 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 10:14:13,962 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 10:14:13,962 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 10:14:13,962 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - TTS audio response: 38684 bytes
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - TTS audio response: 38360 bytes
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - TTS audio response: 38408 bytes
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - TTS audio response: 38376 bytes
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - TTS audio response: 38444 bytes
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - TTS audio response: 38764 bytes
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - TTS sentence ended
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:13,963 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:13,968 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '是'}}        
2025-07-27 10:14:13,969 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,969 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,969 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:13,972 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '上午'}}      
2025-07-27 10:14:13,973 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,973 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,973 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:13,978 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '1'}}
2025-07-27 10:14:13,978 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,978 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,978 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:13,982 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '0'}}
2025-07-27 10:14:13,982 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,982 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,982 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:13,986 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '点'}}        
2025-07-27 10:14:13,986 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,986 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,986 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:13,991 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '1'}}
2025-07-27 10:14:13,991 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,991 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,991 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:13,995 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '3'}}
2025-07-27 10:14:13,996 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:13,996 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:13,996 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:14,001 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '分'}}        
2025-07-27 10:14:14,001 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:14,001 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:14,001 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:14,005 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '。'}}        
2025-07-27 10:14:14,005 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:14,005 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:14,005 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:14,009 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 559, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {}}
2025-07-27 10:14:14,009 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 559 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:14,009 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:14,009 - audio_agent.events.event_handler - INFO - Chat response ended
2025-07-27 10:14:14,013 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'no_content': False}}    
2025-07-27 10:14:14,013 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:14,013 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:14,013 - audio_agent.events.event_handler - INFO - TTS synthesis ended
2025-07-27 10:14:15,995 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 10:14:26,659 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 450, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'asr_task_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31_8_4', 'question_id': '13622179953221378', 'round_id': 10}}
2025-07-27 10:14:26,659 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 450 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:26,659 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:26,659 - audio_agent.events.event_handler - INFO - ASR first word detected - can interrupt playback
2025-07-27 10:14:26,663 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9291902780532837, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 12.382006, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复'}], 'end_time': 12.382006, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复'}]}}
2025-07-27 10:14:26,663 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:26,663 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:26,663 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:26,663 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:26,663 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:26,663 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复', interim: True        
2025-07-27 10:14:26,663 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复'       
2025-07-27 10:14:26,741 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9291902780532837, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 12.442007, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复'}], 'end_time': 12.442007, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复'}]}}
2025-07-27 10:14:26,741 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:26,741 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:26,741 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:26,741 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:26,741 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:26,741 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复', interim: True        
2025-07-27 10:14:26,741 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复'       
2025-07-27 10:14:26,805 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9291902780532837, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 12.542009, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复'}], 'end_time': 12.542009, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复'}]}}
2025-07-27 10:14:26,805 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:26,805 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:26,805 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:26,805 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:26,805 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:26,805 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复', interim: True        
2025-07-27 10:14:26,805 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复'       
2025-07-27 10:14:26,889 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 12.602011, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 12.602011, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:26,889 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:26,889 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:26,889 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:26,889 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:26,889 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:26,889 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:26,889 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:26,995 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'prefetch': True, 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 90.00001574706928}, 'results': [{'alternatives': [{'end_time': 12.702013, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下。'}], 'end_time': 12.702013, 'index': 0, 'is_interim': True, 'is_soft_finished': True, 'is_vad_timeout': False, 'soft_vad_type': 999, 'start_time': 11.622, 'text': '重复一下。'}]}}
2025-07-27 10:14:26,995 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:26,995 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:26,995 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:26,995 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:26,995 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:26,995 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下。', interim: True  
2025-07-27 10:14:26,995 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下。' 
2025-07-27 10:14:27,087 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 139.99938903808484}, 'results': [{'alternatives': [{'end_time': 12.762014, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 12.762014, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,087 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,087 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,087 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,087 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,087 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,087 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,087 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,093 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 240.00167785644422}, 'results': [{'alternatives': [{'end_time': 12.862017, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 12.862017, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,093 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,093 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,093 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,093 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,093 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,093 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,093 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,171 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 300.0030511474598}, 'results': [{'alternatives': [{'end_time': 12.922018, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 12.922018, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,171 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,171 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,171 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,172 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,172 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,172 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,172 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,257 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 390.00033996581965}, 'results': [{'alternatives': [{'end_time': 13.02202, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.02202, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,257 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,257 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,257 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,257 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,257 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,257 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,257 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,290 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 450.0017132568353}, 'results': [{'alternatives': [{'end_time': 13.082022, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.082022, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,290 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,290 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,291 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,291 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,291 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,291 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,291 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,380 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 550.0040020751946}, 'results': [{'alternatives': [{'end_time': 13.182024, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.182024, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,380 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,380 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,380 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,380 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,380 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,380 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,382 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,465 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 610.0053753662103}, 'results': [{'alternatives': [{'end_time': 13.242025, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.242025, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,465 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,465 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,465 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,465 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,465 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,466 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,466 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,540 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 710.0076641845696}, 'results': [{'alternatives': [{'end_time': 13.342028, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.342028, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,540 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,540 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,540 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,540 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,540 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,540 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,540 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,572 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 770.0090374755853}, 'results': [{'alternatives': [{'end_time': 13.402029, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.402029, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,572 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,572 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,572 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,572 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,572 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,572 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,572 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,644 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 870.0113262939446}, 'results': [{'alternatives': [{'end_time': 13.502031, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.502031, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,644 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,644 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,644 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,644 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,644 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,644 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,644 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,713 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 930.0126995849603}, 'results': [{'alternatives': [{'end_time': 13.562033, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.562033, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,713 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,713 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,713 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,713 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,713 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,713 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,713 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,746 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1030.0149884033196}, 'results': [{'alternatives': [{'end_time': 13.662035, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.662035, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,746 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,746 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,746 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,746 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,746 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,746 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,746 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,840 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1090.0163616943353}, 'results': [{'alternatives': [{'end_time': 13.722036, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.722036, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,840 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,840 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,840 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,840 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,840 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,840 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,840 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:27,913 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'interrupt_score': 0.9319528341293335, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重 复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1190.0186505126946}, 'results': [{'alternatives': [{'end_time': 13.822039, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下'}], 'end_time': 13.822039, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下'}]}}
2025-07-27 10:14:27,913 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:27,913 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:27,913 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:27,913 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:27,913 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:27,913 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下', interim: True    
2025-07-27 10:14:27,913 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '重复一下'   
2025-07-27 10:14:28,002 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.07564149051904678, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '重复一下', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '重复一下。', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1250.0200238037103}, 'results': [{'alternatives': [{'end_time': 13.88204, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 11.622, 'text': '重复一下。'}], 'end_time': 13.88204, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 11.622, 'text': '重复一下。'}]}}
2025-07-27 10:14:28,002 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,002 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,002 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 10:14:28,003 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 10:14:28,003 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']     
2025-07-27 10:14:28,003 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '重复一下。', interim: False 
2025-07-27 10:14:28,003 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '重复一下。'   
2025-07-27 10:14:28,008 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 1645, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 12632}}
2025-07-27 10:14:28,008 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,008 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,008 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-27 10:14:28,008 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-27 10:14:28,008 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '重复一下。'       
2025-07-27 10:14:28,008 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '重复一下。'
2025-07-27 10:14:28,011 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
2025-07-27 10:14:28,011 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.7, 'requires_tools': False, 'tools': []}
2025-07-27 10:14:28,011 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.7, 'requires_tools': False, 'tools': []}
2025-07-27 10:14:28,014 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31_10_1', 'tts_type': 'default', 'v3_loundness_params': ''}}
2025-07-27 10:14:28,014 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,015 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,032 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 10:14:28,032 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:28,032 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:28,032 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 10:14:28,037 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-27 10:14:28,053 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 10:14:28,058 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38056bytes>', 'payload_msg': '<binary_data:38056bytes>'}
2025-07-27 10:14:28,059 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:28,059 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:28,059 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38056 bytes
2025-07-27 10:14:28,061 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38056 bytes)
2025-07-27 10:14:28,078 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:37912bytes>', 'payload_msg': '<binary_data:37912bytes>'}
2025-07-27 10:14:28,079 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:28,079 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:28,079 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37912 bytes
2025-07-27 10:14:28,080 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37912 bytes)
2025-07-27 10:14:28,096 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-27 10:14:28,096 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:28,097 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:28,097 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-27 10:14:28,098 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38396 bytes)
2025-07-27 10:14:28,115 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': ''}}
2025-07-27 10:14:28,116 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,116 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,133 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '现在'}}      
2025-07-27 10:14:28,133 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,133 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,150 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '是'}}        
2025-07-27 10:14:28,150 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,150 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,168 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '上午'}}      
2025-07-27 10:14:28,168 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,168 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,184 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '1'}}
2025-07-27 10:14:28,184 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,184 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,199 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '0'}}
2025-07-27 10:14:28,200 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,200 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,216 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '点'}}        
2025-07-27 10:14:28,216 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,217 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,232 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '1'}}
2025-07-27 10:14:28,232 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,232 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,249 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '3'}}
2025-07-27 10:14:28,249 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,249 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,265 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '分'}}        
2025-07-27 10:14:28,265 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,265 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,283 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'content': '。'}}        
2025-07-27 10:14:28,283 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,283 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,300 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:37976bytes>', 'payload_msg': '<binary_data:37976bytes>'}
2025-07-27 10:14:28,300 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:28,300 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:28,300 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37976 bytes
2025-07-27 10:14:28,300 - audio_agent.events.event_handler - INFO - LLM result: {'user_input': '重复一下。', 'ai_response': "我理解您说的是'重复一下。'。我可以为您做些什么吗？"}
2025-07-27 10:14:28,301 - audio_agent.events.event_processor - INFO - LLM response ready for TTS: 我理解您说的是'重复一下。'。我可以为您做些什么吗？
2025-07-27 10:14:28,301 - audio_agent.events.event_handler - INFO - TTS audio response: 94 bytes
2025-07-27 10:14:28,301 - audio_agent.events.event_processor - INFO - Mock TTS response created for: '我理解您说的是'重复一下。'。我可以为您做些什么吗？...'
2025-07-27 10:14:28,301 - audio_agent.events.event_processor - INFO - LLM enhanced response sent to user successfully
2025-07-27 10:14:28,301 - audio_agent.events.event_processor - INFO - Response generated: '我理解您说的是'重复一下。'。我可以为您做些什么吗？'
2025-07-27 10:14:28,301 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 10:14:28,301 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: 8a980f11-ade6-4c3a-ba6c-a3db123acb31_10_1
2025-07-27 10:14:28,301 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True 
2025-07-27 10:14:28,301 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - TTS audio response: 38056 bytes
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - TTS audio response: 37912 bytes
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,302 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,303 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,303 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,303 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,303 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,303 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,303 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 10:14:28,303 - audio_agent.events.event_handler - INFO - TTS audio response: 37976 bytes
2025-07-27 10:14:28,304 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37976 bytes)
2025-07-27 10:14:28,320 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-27 10:14:28,320 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:28,321 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:28,321 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-27 10:14:28,321 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-27 10:14:28,322 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38396 bytes)
2025-07-27 10:14:28,339 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:38988bytes>', 'payload_msg': '<binary_data:38988bytes>'}
2025-07-27 10:14:28,339 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:28,339 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:28,339 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38988 bytes
2025-07-27 10:14:28,341 - audio_agent.events.event_handler - INFO - TTS audio response: 38988 bytes
2025-07-27 10:14:28,342 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38988 bytes)
2025-07-27 10:14:28,357 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': '<binary_data:10200bytes>', 'payload_msg': '<binary_data:10200bytes>'}
2025-07-27 10:14:28,357 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 10:14:28,357 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 10:14:28,357 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 10200 bytes
2025-07-27 10:14:28,357 - audio_agent.events.event_handler - INFO - TTS audio response: 10200 bytes
2025-07-27 10:14:28,358 - audio_agent.core.session_manager - INFO - Audio response queued for playback (10200 bytes)
2025-07-27 10:14:28,375 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {}}
2025-07-27 10:14:28,375 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,375 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,376 - audio_agent.events.event_handler - INFO - TTS sentence ended
2025-07-27 10:14:28,379 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 559, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {}}
2025-07-27 10:14:28,379 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 559 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,379 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,379 - audio_agent.events.event_handler - INFO - Chat response ended
2025-07-27 10:14:28,383 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': '8a980f11-ade6-4c3a-ba6c-a3db123acb31', 'payload': {'no_content': False}}    
2025-07-27 10:14:28,383 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 10:14:28,383 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 10:14:28,383 - audio_agent.events.event_handler - INFO - TTS synthesis ended
2025-07-27 10:14:30,727 - audio_agent.core.session_manager - INFO - AI finished speaking