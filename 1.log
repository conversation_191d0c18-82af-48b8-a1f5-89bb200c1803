2025-07-27 17:14:28,319 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,319 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:28,319 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:28,319 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:28,319 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '喂喂喂', interim: True
2025-07-27 17:14:28,319 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '喂喂喂'
2025-07-27 17:14:28,373 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8855019807815552, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '喂喂喂', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 450.0002364196778}, 'results': [{'alternatives': [{'end_time': 6.082021, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.682, 'text': '喂喂喂'}], 'end_time': 6.082021, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.682, 'text': '喂喂喂'}]}}
2025-07-27 17:14:28,373 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:28,373 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,373 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:28,373 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:28,373 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:28,373 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '喂喂喂', interim: True
2025-07-27 17:14:28,373 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '喂喂喂'
2025-07-27 17:14:28,448 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8855019807815552, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '喂喂喂', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 510.0011328735352}, 'results': [{'alternatives': [{'end_time': 6.142022, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.682, 'text': '喂喂喂'}], 'end_time': 6.142022, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.682, 'text': '喂喂喂'}]}}
2025-07-27 17:14:28,448 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:28,448 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,448 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:28,448 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:28,448 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:28,448 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '喂喂喂', interim: True
2025-07-27 17:14:28,448 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '喂喂喂'
2025-07-27 17:14:28,546 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8855019807815552, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '喂喂喂', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 610.0029448547364}, 'results': [{'alternatives': [{'end_time': 6.242024, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.682, 'text': '喂喂喂'}], 'end_time': 6.242024, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.682, 'text': '喂喂喂'}]}}
2025-07-27 17:14:28,546 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:28,546 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,546 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:28,546 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:28,546 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:28,546 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '喂喂喂', interim: True
2025-07-27 17:14:28,546 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '喂喂喂'
2025-07-27 17:14:28,633 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8855019807815552, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '喂喂喂', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 670.0047949829102}, 'results': [{'alternatives': [{'end_time': 6.302026, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.682, 'text': '喂喂喂'}], 'end_time': 6.302026, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.682, 'text': '喂喂喂'}]}}
2025-07-27 17:14:28,633 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:28,633 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,633 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:28,633 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:28,633 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:28,633 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '喂喂喂', interim: True
2025-07-27 17:14:28,633 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '喂喂喂'
2025-07-27 17:14:28,678 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8855019807815552, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '喂喂喂', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 770.0070838012696}, 'results': [{'alternatives': [{'end_time': 6.402028, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.682, 'text': '喂喂喂'}], 'end_time': 6.402028, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.682, 'text': '喂喂喂'}]}}
2025-07-27 17:14:28,678 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:28,678 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,678 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:28,678 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:28,678 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:28,678 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '喂喂喂', interim: True
2025-07-27 17:14:28,678 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '喂喂喂'
2025-07-27 17:14:28,955 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.12526607513427734, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '喂喂喂', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '喂喂喂。', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 830.007980255127}, 'results': [{'alternatives': [{'end_time': 6.462029, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.682, 'text': '喂喂喂。'}], 'end_time': 6.462029, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 4.682, 'text': '喂喂喂。'}]}}
2025-07-27 17:14:28,955 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:28,955 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,955 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:28,955 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:28,955 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:28,955 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '喂喂喂。', interim: False
2025-07-27 17:14:28,955 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '喂喂喂。'
2025-07-27 17:14:28,955 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 812, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 5632}}
2025-07-27 17:14:28,955 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:28,955 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,955 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-27 17:14:28,955 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-27 17:14:28,955 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '喂喂喂。'
2025-07-27 17:14:28,955 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '喂喂喂。'
2025-07-27 17:14:28,967 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
2025-07-27 17:14:28,967 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.7, 'requires_tools': False, 'tools': []}
2025-07-27 17:14:28,967 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.7, 'requires_tools': False, 'tools': []}      
2025-07-27 17:14:28,968 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': '5da74279-32e6-4cae-b864-5187ab026a7e_8_1', 'tts_type': 'default', 'v3_loundness_params': ''}}        
2025-07-27 17:14:28,968 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:28,968 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:28,989 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 17:14:28,989 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:28,989 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:28,989 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 17:14:28,989 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (36000 bytes) - LLM enhanced response already sent
2025-07-27 17:14:29,037 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38920bytes>', 'payload_msg': '<binary_data:38920bytes>'}
2025-07-27 17:14:29,037 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:29,037 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:29,037 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38920 bytes
2025-07-27 17:14:29,037 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38920 bytes) - LLM enhanced response already sent
2025-07-27 17:14:29,058 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': ''}}
2025-07-27 17:14:29,059 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,059 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,086 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '喂'}}
2025-07-27 17:14:29,086 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,086 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,114 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '喂'}}
2025-07-27 17:14:29,114 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,114 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,134 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '喂'}}
2025-07-27 17:14:29,134 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,134 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,149 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '，'}}
2025-07-27 17:14:29,149 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,149 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,165 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '我'}}
2025-07-27 17:14:29,165 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,165 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,185 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '能'}}
2025-07-27 17:14:29,186 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,186 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,203 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '听到'}}
2025-07-27 17:14:29,203 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,203 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,217 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '你'}}
2025-07-27 17:14:29,218 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,218 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,234 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '说话'}}
2025-07-27 17:14:29,234 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,234 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,255 - audio_agent.events.event_handler - INFO - LLM result: {'user_input': '喂喂喂。', 'ai_response': "我理解您说的是'喂喂喂。'。我可以为您做些什么吗？"}
2025-07-27 17:14:29,255 - audio_agent.events.event_processor - INFO - LLM response ready for TTS: 我理解您说的是'喂喂喂。'。我可以为您做些什么吗？
2025-07-27 17:14:29,255 - audio_agent.events.event_handler - INFO - TTS audio response: 91 bytes
2025-07-27 17:14:29,255 - audio_agent.events.event_processor - INFO - Mock TTS response created for: '我理解您说的是'喂喂喂。'。我可以为您做些什么吗？...'
2025-07-27 17:14:29,255 - audio_agent.events.event_processor - INFO - LLM enhanced response sent to user successfully
2025-07-27 17:14:29,256 - audio_agent.events.event_processor - INFO - Response generated: '我理解您说的是'喂喂喂。'。我可以为您做些什么吗？'
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: 5da74279-32e6-4cae-b864-5187ab026a7e_8_1
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - TTS audio response: 38920 bytes
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,256 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,257 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,257 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,257 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,257 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,257 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,257 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,257 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,257 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,261 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '，'}}
2025-07-27 17:14:29,262 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,262 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,263 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,265 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-27 17:14:29,266 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:29,266 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:29,266 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-27 17:14:29,266 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38396 bytes) - LLM enhanced response already sent
2025-07-27 17:14:29,266 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-27 17:14:29,268 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '你'}}
2025-07-27 17:14:29,269 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,269 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,270 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,274 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '能'}}
2025-07-27 17:14:29,274 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,274 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,275 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,277 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38220bytes>', 'payload_msg': '<binary_data:38220bytes>'}
2025-07-27 17:14:29,277 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:29,277 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:29,277 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38220 bytes
2025-07-27 17:14:29,277 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38220 bytes) - LLM enhanced response already sent
2025-07-27 17:14:29,277 - audio_agent.events.event_handler - INFO - TTS audio response: 38220 bytes
2025-07-27 17:14:29,277 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '听到'}}
2025-07-27 17:14:29,277 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,277 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,277 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:29,285 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38376bytes>', 'payload_msg': '<binary_data:38376bytes>'}
2025-07-27 17:14:29,285 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:29,285 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:29,285 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38376 bytes
2025-07-27 17:14:29,285 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38376 bytes) - LLM enhanced response already sent
2025-07-27 17:14:29,285 - audio_agent.events.event_handler - INFO - TTS audio response: 38376 bytes
2025-07-27 17:14:29,317 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38748bytes>', 'payload_msg': '<binary_data:38748bytes>'}
2025-07-27 17:14:29,317 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:29,317 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:29,317 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38748 bytes
2025-07-27 17:14:29,317 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38748 bytes) - LLM enhanced response already sent
2025-07-27 17:14:29,317 - audio_agent.events.event_handler - INFO - TTS audio response: 38748 bytes
2025-07-27 17:14:29,388 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38376bytes>', 'payload_msg': '<binary_data:38376bytes>'}
2025-07-27 17:14:29,388 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:29,388 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:29,388 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38376 bytes
2025-07-27 17:14:29,388 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38376 bytes) - LLM enhanced response already sent
2025-07-27 17:14:29,388 - audio_agent.events.event_handler - INFO - TTS audio response: 38376 bytes
2025-07-27 17:14:29,573 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38792bytes>', 'payload_msg': '<binary_data:38792bytes>'}
2025-07-27 17:14:29,573 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:29,573 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:29,573 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38792 bytes
2025-07-27 17:14:29,573 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38792 bytes) - LLM enhanced response already sent
2025-07-27 17:14:29,573 - audio_agent.events.event_handler - INFO - TTS audio response: 38792 bytes
2025-07-27 17:14:29,573 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {}}
2025-07-27 17:14:29,573 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:29,573 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:29,573 - audio_agent.events.event_handler - INFO - TTS sentence ended       
2025-07-27 17:14:32,353 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 450, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'asr_task_id': '5da74279-32e6-4cae-b864-5187ab026a7e_8_4', 'question_id': '13081351394753026', 'round_id': 10}}
2025-07-27 17:14:32,353 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 450 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,353 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,353 - audio_agent.events.event_handler - INFO - ASR first word detected - can interrupt playback
2025-07-27 17:14:32,353 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.9257638454437256, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 3.452, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还'}], 'end_time': 3.452, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还'}]}}
2025-07-27 17:14:32,353 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,353 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,353 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,353 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,353 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,353 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还', interim: True
2025-07-27 17:14:32,353 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还'
2025-07-27 17:14:32,366 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.9257638454437256, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 3.522, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还'}], 'end_time': 3.522, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还'}]}}
2025-07-27 17:14:32,366 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,366 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,367 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,367 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,367 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,367 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还', interim: True
2025-07-27 17:14:32,367 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还'
2025-07-27 17:14:32,484 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8833447098731995, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 20.00098855590826}, 'results': [{'alternatives': [{'end_time': 3.612, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在'}], 'end_time': 3.612, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在'}]}}
2025-07-27 17:14:32,484 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,484 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,484 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,484 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,484 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,484 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在', interim: True
2025-07-27 17:14:32,484 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在'
2025-07-27 17:14:32,511 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 89.99996812438971}, 'results': [{'alternatives': [{'end_time': 3.681999, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗'}], 'end_time': 3.681999, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:32,511 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,511 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,511 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,511 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,511 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,511 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:32,511 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:32,652 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'prefetch': True, 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 179.99988229370123}, 'results': [{'alternatives': [{'end_time': 3.771999, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗？'}], 'end_time': 3.771999, 'index': 0, 'is_interim': True, 'is_soft_finished': True, 'is_vad_timeout': False, 'soft_vad_type': 999, 'start_time': 2.702, 'text': '还 在吗？'}]}}
2025-07-27 17:14:32,652 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,652 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,652 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,652 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,652 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,652 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗？', interim: True
2025-07-27 17:14:32,652 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗？'
2025-07-27 17:14:32,720 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 240.00005395507796}, 'results': [{'alternatives': [{'end_time': 3.841999, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在 吗'}], 'end_time': 3.841999, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:32,720 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,720 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,720 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,720 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,720 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,720 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:32,720 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:32,740 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 329.99996812438945}, 'results': [{'alternatives': [{'end_time': 3.931999, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在 吗'}], 'end_time': 3.931999, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:32,742 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,742 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,742 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,742 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,742 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,742 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:32,742 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:32,794 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 399.9999013671873}, 'results': [{'alternatives': [{'end_time': 4.001999, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗'}], 'end_time': 4.001999, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:32,794 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,794 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,795 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,795 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,795 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,795 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:32,795 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:32,889 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 479.9999613037107}, 'results': [{'alternatives': [{'end_time': 4.092001, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗'}], 'end_time': 4.092001, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:32,889 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,889 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,889 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,889 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,889 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,889 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:32,889 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:32,944 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 540.0000403137204}, 'results': [{'alternatives': [{'end_time': 4.162003, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗'}], 'end_time': 4.162003, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:32,944 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:32,944 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:32,944 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:32,944 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:32,944 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:32,944 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:32,944 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:33,035 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 630.0021002502439}, 'results': [{'alternatives': [{'end_time': 4.252005, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗'}], 'end_time': 4.252005, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:33,035 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,035 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,035 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:33,035 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:33,035 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:33,035 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:33,035 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:33,093 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 700.0041792602536}, 'results': [{'alternatives': [{'end_time': 4.322007, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗'}], 'end_time': 4.322007, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:33,093 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,093 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,093 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:33,093 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:33,093 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:33,093 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:33,093 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:33,228 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'interrupt_score': 0.8859437704086304, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 790.0057623596189}, 'results': [{'alternatives': [{'end_time': 4.412009, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗'}], 'end_time': 4.412009, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗'}]}}
2025-07-27 17:14:33,228 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,228 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,228 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:33,228 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:33,228 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:33,228 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗', interim: True
2025-07-27 17:14:33,228 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '还在吗'
2025-07-27 17:14:33,329 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.09042003750801086, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '还在吗', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '还在吗？', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 860.0068876953122}, 'results': [{'alternatives': [{'end_time': 4.48201, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 2.702, 'text': '还在吗？'}], 'end_time': 4.48201, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 2.702, 'text': '还在吗？'}]}}
2025-07-27 17:14:33,329 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,329 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,329 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:14:33,329 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:14:33,329 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:14:33,329 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '还在吗？', interim: False
2025-07-27 17:14:33,329 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '还在吗？'
2025-07-27 17:14:33,338 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 692, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 3622}}
2025-07-27 17:14:33,338 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,338 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,338 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-27 17:14:33,338 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-27 17:14:33,340 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '还在吗？'
2025-07-27 17:14:33,340 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '还在吗？'
2025-07-27 17:14:33,342 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
2025-07-27 17:14:33,342 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.7, 'requires_tools': False, 'tools': []}
2025-07-27 17:14:33,343 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.7, 'requires_tools': False, 'tools': []}      
2025-07-27 17:14:33,344 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': '5da74279-32e6-4cae-b864-5187ab026a7e_10_1', 'tts_type': 'default', 'v3_loundness_params': ''}}       
2025-07-27 17:14:33,344 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,344 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,366 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 17:14:33,366 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:33,366 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:33,366 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 17:14:33,366 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (36000 bytes) - LLM enhanced response already sent
2025-07-27 17:14:33,379 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': ''}}
2025-07-27 17:14:33,379 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,379 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,395 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '我'}}
2025-07-27 17:14:33,396 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,396 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,413 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '在'}}
2025-07-27 17:14:33,413 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,413 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,442 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '呀'}}
2025-07-27 17:14:33,442 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,442 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,467 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '，'}}
2025-07-27 17:14:33,467 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,467 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,495 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '你'}}
2025-07-27 17:14:33,495 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,495 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,514 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '是不是'}}
2025-07-27 17:14:33,514 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,514 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,527 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '有'}}
2025-07-27 17:14:33,527 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,528 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,544 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:37960bytes>', 'payload_msg': '<binary_data:37960bytes>'}
2025-07-27 17:14:33,544 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:33,544 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:33,544 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37960 bytes
2025-07-27 17:14:33,544 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (37960 bytes) - LLM enhanced response already sent
2025-07-27 17:14:33,566 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '什么'}}
2025-07-27 17:14:33,566 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,566 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,583 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {'content': '事儿'}}
2025-07-27 17:14:33,583 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:33,583 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:33,598 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38120bytes>', 'payload_msg': '<binary_data:38120bytes>'}
2025-07-27 17:14:33,598 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:33,598 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:33,598 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38120 bytes
2025-07-27 17:14:33,599 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38120 bytes) - LLM enhanced response already sent
2025-07-27 17:14:33,626 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38392bytes>', 'payload_msg': '<binary_data:38392bytes>'}
2025-07-27 17:14:33,626 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:33,626 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:33,627 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38392 bytes
2025-07-27 17:14:33,627 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38392 bytes) - LLM enhanced response already sent
2025-07-27 17:14:33,635 - audio_agent.events.event_handler - INFO - LLM result: {'user_input': '还在吗？', 'ai_response': "我理解您说的是'还在吗？'。我可以为您做些什么吗？"}
2025-07-27 17:14:33,635 - audio_agent.events.event_processor - INFO - LLM response ready for TTS: 我理解您说的是'还在吗？'。我可以为您做些什么吗？
2025-07-27 17:14:33,635 - audio_agent.events.event_handler - INFO - TTS audio response: 91 bytes
2025-07-27 17:14:33,636 - audio_agent.events.event_processor - INFO - Mock TTS response created for: '我理解您说的是'还在吗？'。我可以为您做些什么吗？...'
2025-07-27 17:14:33,636 - audio_agent.events.event_processor - INFO - LLM enhanced response sent to user successfully
2025-07-27 17:14:33,636 - audio_agent.events.event_processor - INFO - Response generated: '我理解您说的是'还在吗？'。我可以为您做些什么吗？'
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: 5da74279-32e6-4cae-b864-5187ab026a7e_10_1
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 17:14:33,636 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - TTS audio response: 37960 bytes
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - TTS audio response: 38120 bytes
2025-07-27 17:14:33,637 - audio_agent.events.event_handler - INFO - TTS audio response: 38392 bytes
2025-07-27 17:14:33,737 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38716bytes>', 'payload_msg': '<binary_data:38716bytes>'}
2025-07-27 17:14:33,737 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:33,737 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:33,737 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38716 bytes
2025-07-27 17:14:33,737 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38716 bytes) - LLM enhanced response already sent
2025-07-27 17:14:33,737 - audio_agent.events.event_handler - INFO - TTS audio response: 38716 bytes
2025-07-27 17:14:33,842 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38092bytes>', 'payload_msg': '<binary_data:38092bytes>'}
2025-07-27 17:14:33,842 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:33,842 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:33,842 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38092 bytes
2025-07-27 17:14:33,842 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38092 bytes) - LLM enhanced response already sent
2025-07-27 17:14:33,842 - audio_agent.events.event_handler - INFO - TTS audio response: 38092 bytes
2025-07-27 17:14:33,978 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:38360bytes>', 'payload_msg': '<binary_data:38360bytes>'}
2025-07-27 17:14:33,978 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:33,978 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:33,978 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38360 bytes
2025-07-27 17:14:33,978 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38360 bytes) - LLM enhanced response already sent
2025-07-27 17:14:33,978 - audio_agent.events.event_handler - INFO - TTS audio response: 38360 bytes
2025-07-27 17:14:34,017 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': '<binary_data:4488bytes>', 'payload_msg': '<binary_data:4488bytes>'}
2025-07-27 17:14:34,017 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:14:34,017 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:14:34,018 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 4488 bytes
2025-07-27 17:14:34,018 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (4488 bytes) - LLM enhanced response already sent
2025-07-27 17:14:34,018 - audio_agent.events.event_handler - INFO - TTS audio response: 4488 bytes
2025-07-27 17:14:34,018 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '5da74279-32e6-4cae-b864-5187ab026a7e', 'payload': {}}
2025-07-27 17:14:34,018 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:14:34,018 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:14:34,018 - audio_agent.events.event_handler - INFO - TTS sentence ended 