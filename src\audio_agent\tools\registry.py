"""
Tool registry for managing available tools.

This module provides a centralized registry for all available tools
that can be called by the AI agent.
"""

import logging
from typing import Dict, List, Optional, Type
from .base import BaseTool

logger = logging.getLogger(__name__)


class ToolRegistry:
    """
    Registry for managing available tools.
    
    This class maintains a collection of tools that can be called
    by the AI agent and provides methods to register, retrieve,
    and manage these tools.
    """
    
    def __init__(self):
        """Initialize the tool registry."""
        self._tools: Dict[str, BaseTool] = {}
        self._tool_classes: Dict[str, Type[BaseTool]] = {}
    
    def register_tool(self, tool: BaseTool) -> None:
        """
        Register a tool instance.
        
        Args:
            tool: Tool instance to register
            
        Raises:
            ValueError: If tool name already exists
        """
        if tool.name in self._tools:
            raise ValueError(f"Tool '{tool.name}' is already registered")
        
        self._tools[tool.name] = tool
        logger.info(f"Registered tool: {tool.name}")
    
    def register_tool_class(self, tool_class: Type[BaseTool], **kwargs) -> None:
        """
        Register a tool class and instantiate it.
        
        Args:
            tool_class: Tool class to register
            **kwargs: Arguments to pass to tool constructor
        """
        tool_instance = tool_class(**kwargs)
        self.register_tool(tool_instance)
        self._tool_classes[tool_instance.name] = tool_class
    
    def unregister_tool(self, tool_name: str) -> bool:
        """
        Unregister a tool.
        
        Args:
            tool_name: Name of the tool to unregister
            
        Returns:
            True if tool was unregistered, False if not found
        """
        if tool_name in self._tools:
            del self._tools[tool_name]
            if tool_name in self._tool_classes:
                del self._tool_classes[tool_name]
            logger.info(f"Unregistered tool: {tool_name}")
            return True
        return False
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """
        Get a tool by name.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool instance or None if not found
        """
        return self._tools.get(tool_name)
    
    def list_tools(self) -> List[str]:
        """
        Get list of all registered tool names.
        
        Returns:
            List of tool names
        """
        return list(self._tools.keys())
    
    def get_all_tools(self) -> Dict[str, BaseTool]:
        """
        Get all registered tools.
        
        Returns:
            Dictionary of tool name to tool instance
        """
        return self._tools.copy()
    
    def get_function_schemas(self) -> List[Dict]:
        """
        Get function schemas for all tools in Volcengine format.
        
        Returns:
            List of function schemas for Volcengine API
        """
        return [tool.to_function_schema() for tool in self._tools.values()]
    
    def clear(self) -> None:
        """Clear all registered tools."""
        self._tools.clear()
        self._tool_classes.clear()
        logger.info("Cleared all tools from registry")
    
    def __len__(self) -> int:
        """Get number of registered tools."""
        return len(self._tools)
    
    def __contains__(self, tool_name: str) -> bool:
        """Check if tool is registered."""
        return tool_name in self._tools
    
    def __iter__(self):
        """Iterate over tool names."""
        return iter(self._tools.keys())


# Global tool registry instance
_global_registry: Optional[ToolRegistry] = None


def get_tool_registry() -> ToolRegistry:
    """
    Get the global tool registry instance.
    
    Returns:
        Global ToolRegistry instance
    """
    global _global_registry
    if _global_registry is None:
        _global_registry = ToolRegistry()
    return _global_registry


def register_tool(tool: BaseTool) -> None:
    """
    Register a tool in the global registry.
    
    Args:
        tool: Tool instance to register
    """
    get_tool_registry().register_tool(tool)


def register_tool_class(tool_class: Type[BaseTool], **kwargs) -> None:
    """
    Register a tool class in the global registry.
    
    Args:
        tool_class: Tool class to register
        **kwargs: Arguments to pass to tool constructor
    """
    get_tool_registry().register_tool_class(tool_class, **kwargs)


def get_tool(tool_name: str) -> Optional[BaseTool]:
    """
    Get a tool from the global registry.
    
    Args:
        tool_name: Name of the tool
        
    Returns:
        Tool instance or None if not found
    """
    return get_tool_registry().get_tool(tool_name)


def list_tools() -> List[str]:
    """
    Get list of all registered tool names from global registry.
    
    Returns:
        List of tool names
    """
    return get_tool_registry().list_tools()


def get_function_schemas() -> List[Dict]:
    """
    Get function schemas for all tools from global registry.
    
    Returns:
        List of function schemas for Volcengine API
    """
    return get_tool_registry().get_function_schemas()
