#!/usr/bin/env python3
"""
测试立即阻止服务器自动回复的脚本

验证在ASR结束瞬间发送ChatTTSText是否能完全阻止服务器的自动回复
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from audio_agent.config import get_config
from audio_agent.events import EventProcessor, EventType, BaseEvent
from datetime import datetime
import uuid

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockDialogClientActor:
    """模拟的 DialogClientActor 用于测试立即阻止"""
    
    def __init__(self):
        self.sent_messages = []
        self.send_times = []
        
    async def send_chat_tts_text(self, text: str, start: bool = True, end: bool = True) -> bool:
        """模拟发送ChatTTSText并记录时间"""
        timestamp = datetime.now()
        message = {
            "text": text,
            "start": start,
            "end": end,
            "timestamp": timestamp
        }
        self.sent_messages.append(message)
        self.send_times.append(timestamp)
        
        logger.info(f"📤 ChatTTSText: start={start}, end={end}, text='{text}', time={timestamp.strftime('%H:%M:%S.%f')[:-3]}")
        return True
    
    def get_timing_analysis(self):
        """分析发送时机"""
        if len(self.send_times) < 2:
            return "Not enough messages to analyze timing"
        
        first_send = self.send_times[0]
        intervals = []
        for i in range(1, len(self.send_times)):
            interval = (self.send_times[i] - first_send).total_seconds() * 1000  # 毫秒
            intervals.append(interval)
        
        return {
            "first_send_time": first_send.strftime('%H:%M:%S.%f')[:-3],
            "intervals_ms": intervals,
            "total_messages": len(self.sent_messages)
        }


async def test_immediate_blocking():
    """测试立即阻止服务器自动回复"""
    logger.info("🧪 测试立即阻止服务器自动回复")
    
    # 获取配置
    config = get_config()
    
    # 创建模拟的 DialogClientActor
    mock_dialog_actor = MockDialogClientActor()
    
    # 创建 EventProcessor
    session_id = "test-immediate-blocking"
    event_processor = EventProcessor(session_id, config, mock_dialog_actor)
    
    logger.info("✅ EventProcessor 创建成功")
    
    # 模拟ASR结束事件
    asr_end_event = BaseEvent(
        event_type=EventType.ASR_ENDED,
        timestamp=datetime.now(),
        session_id=session_id,
        event_id=str(uuid.uuid4()),
        data={"user_input": "现在是什么时间？"}
    )
    
    # 设置用户输入
    event_processor.current_user_input = "现在是什么时间？"
    
    logger.info("🏁 模拟ASR结束事件")
    start_time = datetime.now()
    
    try:
        # 处理ASR结束事件
        await event_processor._handle_asr_ended_with_enhancement(asr_end_event)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds() * 1000
        
        logger.info(f"✅ ASR结束事件处理完成，耗时: {processing_time:.2f}ms")
        
        # 分析发送时机
        timing_analysis = mock_dialog_actor.get_timing_analysis()
        logger.info("📊 发送时机分析:")
        logger.info(f"   首次发送时间: {timing_analysis.get('first_send_time', 'N/A')}")
        logger.info(f"   总消息数: {timing_analysis.get('total_messages', 0)}")
        
        if 'intervals_ms' in timing_analysis:
            for i, interval in enumerate(timing_analysis['intervals_ms']):
                logger.info(f"   消息{i+2}间隔: {interval:.2f}ms")
        
        # 验证发送序列
        messages = mock_dialog_actor.sent_messages
        if len(messages) >= 3:
            logger.info("📋 发送序列验证:")
            logger.info(f"   消息1: start={messages[0]['start']}, end={messages[0]['end']}, text='{messages[0]['text']}'")
            logger.info(f"   消息2: start={messages[1]['start']}, end={messages[1]['end']}, text='{messages[1]['text'][:30]}...'")
            logger.info(f"   消息3: start={messages[2]['start']}, end={messages[2]['end']}, text='{messages[2]['text']}'")
            
            # 检查是否符合预期序列
            expected_sequence = [
                (True, False),   # 立即阻止：start=True, end=False
                (False, False),  # 工具结果：start=False, end=False
                (False, True)    # 结束标记：start=False, end=True
            ]
            
            actual_sequence = [(msg['start'], msg['end']) for msg in messages[:3]]
            
            if actual_sequence == expected_sequence:
                logger.info("✅ 发送序列正确！")
            else:
                logger.error(f"❌ 发送序列错误！期望: {expected_sequence}, 实际: {actual_sequence}")
        else:
            logger.error(f"❌ 消息数量不足，期望至少3条，实际: {len(messages)}")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_timing_critical():
    """测试时机关键性"""
    logger.info("🧪 测试时机关键性 - 验证立即发送的重要性")
    
    config = get_config()
    mock_dialog_actor = MockDialogClientActor()
    event_processor = EventProcessor("test-timing", config, mock_dialog_actor)
    
    # 模拟不同的发送时机
    scenarios = [
        ("立即发送", 0),      # 0ms延迟
        ("轻微延迟", 10),     # 10ms延迟
        ("明显延迟", 50),     # 50ms延迟
        ("严重延迟", 100),    # 100ms延迟
    ]
    
    for scenario_name, delay_ms in scenarios:
        logger.info(f"🔬 测试场景: {scenario_name} (延迟{delay_ms}ms)")
        
        # 重置消息记录
        mock_dialog_actor.sent_messages.clear()
        mock_dialog_actor.send_times.clear()
        
        # 模拟延迟
        if delay_ms > 0:
            await asyncio.sleep(delay_ms / 1000)
        
        # 发送占位符
        await mock_dialog_actor.send_chat_tts_text("", start=True, end=False)
        
        # 模拟工具执行时间
        await asyncio.sleep(0.1)
        
        # 发送工具结果
        await mock_dialog_actor.send_chat_tts_text("现在是晚上10点40分", start=False, end=False)
        await mock_dialog_actor.send_chat_tts_text("", start=False, end=True)
        
        timing = mock_dialog_actor.get_timing_analysis()
        logger.info(f"   结果: {timing['total_messages']}条消息，首次发送: {timing['first_send_time']}")


async def main():
    """主测试函数"""
    logger.info("🚀 开始立即阻止测试")
    
    try:
        # 测试立即阻止
        await test_immediate_blocking()
        
        # 测试时机关键性
        await test_timing_critical()
        
        logger.info("🎉 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
