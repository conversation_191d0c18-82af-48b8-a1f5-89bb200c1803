#!/usr/bin/env python3
"""
测试工具调用修复的脚本

这个脚本验证工具调用结果是否能正确发送到火山引擎服务器
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from audio_agent.config import get_config, update_config
from audio_agent.events import EventProcessor, EventType, LLMEvent
from audio_agent.core.actors import DialogClientActor
from datetime import datetime
import uuid

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockDialogClientActor:
    """模拟的 DialogClientActor 用于测试"""
    
    def __init__(self):
        self.sent_messages = []
        
    async def send_tool_result(self, user_input: str, tool_results: list, ai_response: str) -> bool:
        """模拟发送工具调用结果"""
        message = {
            "user_input": user_input,
            "tool_results": tool_results,
            "ai_response": ai_response,
            "timestamp": datetime.now()
        }
        self.sent_messages.append(message)
        logger.info(f"✅ Mock: Tool result sent - {ai_response[:50]}...")
        return True


async def test_tool_calling_fix():
    """测试工具调用修复"""
    logger.info("🧪 开始测试工具调用修复")
    
    # 获取配置
    config = get_config()
    
    # 创建模拟的 DialogClientActor
    mock_dialog_actor = MockDialogClientActor()
    
    # 创建 EventProcessor，传入模拟的 dialog_client_actor
    session_id = "test-session-123"
    event_processor = EventProcessor(session_id, config, mock_dialog_actor)
    
    logger.info("✅ EventProcessor 创建成功，包含 dialog_client_actor 引用")
    
    # 模拟工具调用事件
    tool_call_event = LLMEvent(
        event_type=EventType.LLM_TOOL_CALL,
        timestamp=datetime.now(),
        session_id=session_id,
        event_id=str(uuid.uuid4()),
        data={
            "user_input": "今天天气怎么样？",
            "intent": {"intent": "weather_query", "requires_tools": True},
            "tools_needed": ["web_search"]
        },
        user_message="今天天气怎么样？",
        intent="weather_query",
        tool_calls=["web_search"]
    )
    
    logger.info("🔧 模拟工具调用事件创建")
    
    # 处理工具调用事件
    try:
        await event_processor._handle_llm_tool_execution(tool_call_event)
        logger.info("✅ 工具调用事件处理完成")
        
        # 检查是否发送了消息
        if mock_dialog_actor.sent_messages:
            logger.info(f"✅ 成功发送了 {len(mock_dialog_actor.sent_messages)} 条消息到服务器")
            for i, msg in enumerate(mock_dialog_actor.sent_messages):
                logger.info(f"   消息 {i+1}: {msg['ai_response'][:100]}...")
        else:
            logger.error("❌ 没有发送任何消息到服务器")
            
    except Exception as e:
        logger.error(f"❌ 工具调用处理失败: {e}")
        import traceback
        traceback.print_exc()


async def test_event_processor_initialization():
    """测试 EventProcessor 初始化"""
    logger.info("🧪 测试 EventProcessor 初始化")
    
    config = get_config()
    mock_dialog_actor = MockDialogClientActor()
    
    # 测试带 dialog_client_actor 的初始化
    event_processor = EventProcessor("test-session", config, mock_dialog_actor)
    
    if event_processor.dialog_client_actor is not None:
        logger.info("✅ EventProcessor 正确保存了 dialog_client_actor 引用")
    else:
        logger.error("❌ EventProcessor 没有保存 dialog_client_actor 引用")
        
    # 测试不带 dialog_client_actor 的初始化
    event_processor_no_actor = EventProcessor("test-session-2", config)
    
    if event_processor_no_actor.dialog_client_actor is None:
        logger.info("✅ EventProcessor 在没有 dialog_client_actor 时正确处理")
    else:
        logger.error("❌ EventProcessor 在没有 dialog_client_actor 时处理错误")


async def main():
    """主测试函数"""
    logger.info("🚀 开始工具调用修复测试")
    
    try:
        # 测试 EventProcessor 初始化
        await test_event_processor_initialization()
        
        # 测试工具调用修复
        await test_tool_calling_fix()
        
        logger.info("🎉 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
