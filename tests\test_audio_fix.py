#!/usr/bin/env python3
"""
测试音频修复的脚本
"""

import asyncio
import logging
import time
from audio_agent.config import get_config
from audio_agent.core.actors import AudioInputActor
import ray

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_audio_input():
    """测试音频输入"""
    print("=== 测试音频输入修复 ===")
    
    # 获取配置
    config = get_config()
    print(f"输入音频配置:")
    print(f"  - 采样率: {config.input_audio.sample_rate}Hz")
    print(f"  - 通道数: {config.input_audio.channels}")
    print(f"  - 块大小: {config.input_audio.chunk}")
    print(f"  - 位深度: {config.input_audio.bit_size}")
    
    # 初始化Ray
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    
    try:
        # 创建音频输入Actor
        audio_input_actor = AudioInputActor.remote(config.input_audio)
        
        # 启动录音
        print("\n启动录音...")
        started = await audio_input_actor.start_recording.remote()
        
        if not started:
            print("❌ 启动录音失败")
            return
        
        print("✅ 录音启动成功")
        print("请说话，观察音频检测日志...")
        print("按 Ctrl+C 停止测试")
        
        # 监控音频数据
        audio_count = 0
        try:
            while True:
                # 获取音频数据
                audio_data = await audio_input_actor.get_audio_data.remote()
                
                if audio_data:
                    audio_count += 1
                    print(f"收到音频数据 #{audio_count}: {len(audio_data)} 字节")
                
                await asyncio.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n停止测试")
        
        # 停止录音
        await audio_input_actor.stop_recording.remote()
        print("录音已停止")
        
    except Exception as e:
        print(f"测试出错: {e}")
    finally:
        if ray.is_initialized():
            ray.shutdown()

if __name__ == "__main__":
    asyncio.run(test_audio_input())
