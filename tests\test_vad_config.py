#!/usr/bin/env python3
"""
Test script to verify VAD configuration loading from environment variables.
"""

import os
import logging
from audio_agent.config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_vad_config():
    """Test VAD configuration loading."""
    logger.info("🔧 Testing VAD Configuration Loading")
    
    # Load configuration
    config = get_config()
    
    # Display current VAD configuration
    logger.info("\n📋 Current VAD Configuration:")
    logger.info(f"   VAD Enabled: {config.input_audio.vad_enabled}")
    logger.info(f"   VAD Model: {config.input_audio.vad_model}")
    logger.info(f"   VAD Threshold: {config.input_audio.vad_threshold}")
    logger.info(f"   VAD Window Size: {config.input_audio.vad_window_size_ms}ms")
    logger.info(f"   Min Speech Duration: {config.input_audio.vad_min_speech_duration_ms}ms")
    
    # Display interrupt configuration
    logger.info("\n🛑 Current Interrupt Configuration:")
    logger.info(f"   Interrupt Enabled: {config.interrupt.enabled}")
    logger.info(f"   Response Delay: {config.interrupt.response_delay_ms}ms")
    logger.info(f"   Min Speech Duration: {config.interrupt.min_speech_duration_ms}ms")
    logger.info(f"   Debounce Time: {config.interrupt.debounce_time_ms}ms")
    
    # Check environment variables
    logger.info("\n🌍 Environment Variables Check:")
    vad_env_vars = [
        "AUDIO_AGENT_INPUT_AUDIO_VAD_ENABLED",
        "AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL", 
        "AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD",
        "AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS",
        "AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS"
    ]
    
    interrupt_env_vars = [
        "AUDIO_AGENT_INTERRUPT_ENABLED",
        "AUDIO_AGENT_INTERRUPT_RESPONSE_DELAY_MS",
        "AUDIO_AGENT_INTERRUPT_MIN_SPEECH_DURATION_MS",
        "AUDIO_AGENT_INTERRUPT_DEBOUNCE_TIME_MS"
    ]
    
    for var in vad_env_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var} = {value}")
        else:
            logger.info(f"   ⚪ {var} = (using default)")
    
    for var in interrupt_env_vars:
        value = os.getenv(var)
        if value:
            logger.info(f"   ✅ {var} = {value}")
        else:
            logger.info(f"   ⚪ {var} = (using default)")
    
    # Validate configuration
    logger.info("\n✅ Configuration Validation:")
    
    # Check VAD model
    valid_models = ["silero", "webrtc", "simple"]
    if config.input_audio.vad_model in valid_models:
        logger.info(f"   ✅ VAD Model '{config.input_audio.vad_model}' is valid")
    else:
        logger.warning(f"   ⚠️ VAD Model '{config.input_audio.vad_model}' is not recognized")
    
    # Check VAD threshold
    if 0.0 <= config.input_audio.vad_threshold <= 1.0:
        logger.info(f"   ✅ VAD Threshold {config.input_audio.vad_threshold} is valid")
    else:
        logger.warning(f"   ⚠️ VAD Threshold {config.input_audio.vad_threshold} should be between 0.0 and 1.0")
    
    # Check window size
    if 10 <= config.input_audio.vad_window_size_ms <= 1000:
        logger.info(f"   ✅ VAD Window Size {config.input_audio.vad_window_size_ms}ms is reasonable")
    else:
        logger.warning(f"   ⚠️ VAD Window Size {config.input_audio.vad_window_size_ms}ms might be too extreme")
    
    # Check min speech duration
    if 50 <= config.input_audio.vad_min_speech_duration_ms <= 1000:
        logger.info(f"   ✅ Min Speech Duration {config.input_audio.vad_min_speech_duration_ms}ms is reasonable")
    else:
        logger.warning(f"   ⚠️ Min Speech Duration {config.input_audio.vad_min_speech_duration_ms}ms might be too extreme")
    
    # Provide recommendations
    logger.info("\n💡 Configuration Recommendations:")
    
    if config.input_audio.vad_model == "silero":
        logger.info("   🎯 Excellent choice! Silero VAD provides the best accuracy")
        logger.info("   📝 Recommended threshold: 0.3-0.7 depending on environment")
    elif config.input_audio.vad_model == "webrtc":
        logger.info("   ⚡ Good choice for lightweight applications")
        logger.info("   📝 WebRTC VAD is binary (0 or 1), threshold acts as a filter")
    elif config.input_audio.vad_model == "simple":
        logger.info("   🔧 Basic VAD - consider upgrading to Silero for better results")
        logger.info("   📝 May require careful threshold tuning for your environment")
    
    # Environment-specific suggestions
    if config.input_audio.vad_threshold < 0.3:
        logger.info("   🔊 Low threshold - good for quiet environments, may trigger on noise")
    elif config.input_audio.vad_threshold > 0.7:
        logger.info("   🔇 High threshold - good for noisy environments, may miss quiet speech")
    else:
        logger.info("   🎯 Balanced threshold - good for most environments")
    
    if config.input_audio.vad_window_size_ms < 30:
        logger.info("   ⚡ Small window - fast response but may be unstable")
    elif config.input_audio.vad_window_size_ms > 100:
        logger.info("   🐌 Large window - stable but slower response")
    else:
        logger.info("   ⚖️ Balanced window size - good compromise")
    
    logger.info("\n🎉 VAD Configuration Test Complete!")
    
    return config


def test_vad_initialization():
    """Test VAD manager initialization."""
    logger.info("\n🚀 Testing VAD Manager Initialization")
    
    try:
        from audio_agent.core.vad import VADManager
        
        config = get_config()
        
        # Test VAD manager creation
        vad_manager = VADManager(
            vad_model=config.input_audio.vad_model,
            threshold=config.input_audio.vad_threshold,
            window_size_ms=config.input_audio.vad_window_size_ms,
            min_speech_duration_ms=config.input_audio.vad_min_speech_duration_ms
        )
        
        logger.info(f"✅ VAD Manager initialized successfully with {config.input_audio.vad_model} model")
        
        # Test VAD status
        status = vad_manager.get_speech_status()
        logger.info(f"📊 Initial VAD Status: {status}")
        
        # Test reset
        vad_manager.reset()
        logger.info("✅ VAD reset successful")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ VAD Manager initialization failed: {e}")
        logger.info("💡 This might be due to missing dependencies:")
        logger.info("   - For Silero VAD: pip install silero-vad torch")
        logger.info("   - For WebRTC VAD: pip install webrtcvad")
        return False


def main():
    """Main test function."""
    logger.info("🔧 VAD Configuration Test Suite")
    logger.info("=" * 50)
    
    # Test configuration loading
    config = test_vad_config()
    
    # Test VAD initialization
    vad_init_success = test_vad_initialization()
    
    logger.info("\n" + "=" * 50)
    logger.info("📋 Test Summary:")
    logger.info(f"   Configuration Loading: ✅ Success")
    logger.info(f"   VAD Initialization: {'✅ Success' if vad_init_success else '❌ Failed'}")
    
    if vad_init_success:
        logger.info("\n🎉 All tests passed! Your VAD configuration is ready.")
        logger.info("💡 You can now use the audio agent with professional VAD.")
    else:
        logger.info("\n⚠️ VAD initialization failed. Please check dependencies.")
        logger.info("📝 See the error messages above for installation instructions.")
    
    logger.info("\n📖 For more configuration options, see .env.example")


if __name__ == "__main__":
    main()
