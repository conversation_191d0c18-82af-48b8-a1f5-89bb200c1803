# Voice Activity Detection (VAD) Configuration Guide

This guide explains how to configure the professional Voice Activity Detection (VAD) system in the Audio Agent.

## Overview

The Audio Agent now includes professional-grade VAD with support for multiple models:

- **Silero VAD** (recommended): Enterprise-grade accuracy, 1ms processing time
- **WebRTC VAD**: Lightweight alternative for resource-constrained environments  
- **Simple VAD**: Basic amplitude-based fallback

## Quick Start

1. Copy the example configuration:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` to configure VAD settings:
   ```bash
   # Enable professional VAD
   AUDIO_AGENT_INPUT_AUDIO_VAD_ENABLED=true
   
   # Use Silero VAD (recommended)
   AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
   
   # Set sensitivity (0.0-1.0)
   AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.5
   ```

3. Test your configuration:
   ```bash
   cd src
   python test_vad_config.py
   ```

## Environment Variables

### Core VAD Settings

| Variable | Default | Description |
|----------|---------|-------------|
| `AUDIO_AGENT_INPUT_AUDIO_VAD_ENABLED` | `true` | Enable/disable VAD |
| `AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL` | `silero` | VAD model: `silero`, `webrtc`, or `simple` |
| `AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD` | `0.5` | Confidence threshold (0.0-1.0) |
| `AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS` | `30` | Processing window size (ms) |
| `AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS` | `100` | Min speech duration for interrupt (ms) |

### Interrupt Settings

| Variable | Default | Description |
|----------|---------|-------------|
| `AUDIO_AGENT_INTERRUPT_ENABLED` | `true` | Enable audio playback interruption |
| `AUDIO_AGENT_INTERRUPT_RESPONSE_DELAY_MS` | `50` | Interrupt response delay (ms) |
| `AUDIO_AGENT_INTERRUPT_MIN_SPEECH_DURATION_MS` | `200` | Min speech duration to trigger interrupt (ms) |
| `AUDIO_AGENT_INTERRUPT_DEBOUNCE_TIME_MS` | `100` | Debounce time to avoid false interrupts (ms) |

## VAD Model Comparison

### Silero VAD (Recommended)
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.5
```

**Pros:**
- Highest accuracy (trained on 6000+ languages)
- Very fast (< 1ms processing time)
- Robust to noise and different audio qualities
- Supports 8kHz and 16kHz sample rates

**Cons:**
- Requires `silero-vad` package
- Slightly larger memory footprint

**Best for:** Production environments, high-accuracy requirements

### WebRTC VAD
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=webrtc
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.5
```

**Pros:**
- Lightweight and fast
- Battle-tested in WebRTC applications
- Low memory usage

**Cons:**
- Binary output (less nuanced than Silero)
- May be less accurate in noisy environments

**Best for:** Resource-constrained environments, embedded systems

### Simple VAD (Fallback)
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=simple
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.01
```

**Pros:**
- No external dependencies
- Very lightweight
- Always available

**Cons:**
- Basic amplitude-based detection
- Sensitive to noise and volume changes
- Requires careful threshold tuning

**Best for:** Development, testing, fallback scenarios

## Environment-Specific Configurations

### Quiet Office Environment
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.3
AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS=150
AUDIO_AGENT_INTERRUPT_MIN_SPEECH_DURATION_MS=200
```

### Noisy Environment
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.7
AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS=300
AUDIO_AGENT_INTERRUPT_MIN_SPEECH_DURATION_MS=400
```

### Low-Latency Applications
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS=20
AUDIO_AGENT_INTERRUPT_RESPONSE_DELAY_MS=25
AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS=100
```

### Resource-Constrained Systems
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=webrtc
AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS=50
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.5
```

## Installation Requirements

### For Silero VAD (Recommended)
```bash
pip install silero-vad torch
```

### For WebRTC VAD
```bash
pip install webrtcvad
```

### For Simple VAD
No additional dependencies required.

## Testing Your Configuration

1. **Basic Configuration Test:**
   ```bash
   cd src
   python test_vad_config.py
   ```

2. **Real-time VAD Test:**
   ```bash
   cd src
   python test_professional_vad.py
   ```

3. **Full Integration Test:**
   ```bash
   cd src
   python -m audio_agent.main
   ```

## Troubleshooting

### VAD Not Working
1. Check if the required packages are installed
2. Verify environment variables are set correctly
3. Test with `python test_vad_config.py`

### False Interrupts
- Increase `AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD`
- Increase `AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS`
- Increase `AUDIO_AGENT_INTERRUPT_DEBOUNCE_TIME_MS`

### Missed Speech Detection
- Decrease `AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD`
- Decrease `AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS`
- Switch to Silero VAD if using WebRTC or Simple

### High Latency
- Decrease `AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS`
- Decrease `AUDIO_AGENT_INTERRUPT_RESPONSE_DELAY_MS`
- Use smaller audio chunks

## Performance Tuning

### For Maximum Accuracy
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.4
AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS=50
```

### For Minimum Latency
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS=20
AUDIO_AGENT_INTERRUPT_RESPONSE_DELAY_MS=25
```

### For Resource Efficiency
```bash
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=webrtc
AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS=30
```

## Advanced Configuration

### Custom VAD Thresholds by Environment
You can create different `.env` files for different environments:

- `.env.development` - Lower thresholds for testing
- `.env.production` - Optimized for real-world use
- `.env.noisy` - Higher thresholds for noisy environments

### Monitoring VAD Performance
Enable detailed logging to monitor VAD performance:

```bash
# Add to your .env file
AUDIO_AGENT_DEBUG_VAD=true
AUDIO_AGENT_DEBUG_AUDIO=true
```

## Best Practices

1. **Start with Silero VAD** - It provides the best out-of-the-box experience
2. **Test in your target environment** - Acoustic conditions vary significantly
3. **Monitor false positive/negative rates** - Adjust thresholds based on real usage
4. **Use appropriate window sizes** - Balance between responsiveness and stability
5. **Consider your use case** - Different applications may need different settings

## Support

If you encounter issues with VAD configuration:

1. Run the configuration test: `python test_vad_config.py`
2. Check the logs for VAD-related errors
3. Try different VAD models if one isn't working
4. Refer to the troubleshooting section above

For more advanced configuration options, see the source code in `src/audio_agent/core/vad.py`.
