"""
Intent recognizer for analyzing user messages.

This module provides intent recognition capabilities to determine
what the user wants to do and whether tools are needed.
"""

import logging
import re
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class IntentRecognizer:
    """
    Intent recognizer that analyzes user messages to determine intent
    and whether tool calling is needed.
    """
    
    def __init__(self):
        """Initialize the intent recognizer."""
        self.intent_patterns = {
            "search": [
                r"搜索|查找|找|search|find",
                r"什么是|what is|告诉我关于",
                r"查询|query|lookup"
            ],
            "calculation": [
                r"计算|算|calculate|compute",
                r"\d+\s*[+\-*/]\s*\d+",
                r"多少|how much|how many",
                r"加|减|乘|除|plus|minus|times|divide"
            ],
            "time": [
                r"时间|时候|time|when",
                r"日期|date|今天|明天|昨天|today|tomorrow|yesterday",
                r"现在|now|当前|current",
                r"几点|what time|几号|what date"
            ],
            "greeting": [
                r"你好|hello|hi|嗨|您好",
                r"早上好|下午好|晚上好|good morning|good afternoon|good evening",
                r"开始|start|begin"
            ],
            "farewell": [
                r"再见|goodbye|bye|拜拜",
                r"结束|end|finish|stop",
                r"谢谢|thank you|thanks"
            ],
            "help": [
                r"帮助|help|怎么|how to",
                r"可以做什么|what can you do",
                r"功能|features|capabilities"
            ]
        }
        
        self.tool_requiring_intents = {
            "search", "calculation", "time"
        }
    
    async def analyze_intent(self, message: str) -> Dict[str, Any]:
        """
        Analyze user message to determine intent.
        
        Args:
            message: User message to analyze
            
        Returns:
            Dictionary containing intent analysis results
        """
        try:
            message_lower = message.lower().strip()
            
            # Detect intent type
            intent_type = self._detect_intent_type(message_lower)
            
            # Determine if tools are needed
            needs_tools = intent_type in self.tool_requiring_intents
            
            # Extract entities/parameters
            entities = self._extract_entities(message, intent_type)
            
            # Calculate confidence
            confidence = self._calculate_confidence(message_lower, intent_type)
            
            result = {
                "type": intent_type,
                "needs_tools": needs_tools,
                "confidence": confidence,
                "entities": entities,
                "original_message": message
            }
            
            logger.info(f"Intent analysis: {intent_type} (confidence: {confidence:.2f})")
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing intent: {e}")
            return {
                "type": "general",
                "needs_tools": False,
                "confidence": 0.0,
                "entities": {},
                "original_message": message,
                "error": str(e)
            }
    
    def _detect_intent_type(self, message: str) -> str:
        """
        Detect the intent type from the message.
        
        Args:
            message: Preprocessed message
            
        Returns:
            Intent type string
        """
        intent_scores = {}
        
        for intent_type, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    score += 1
            
            if score > 0:
                intent_scores[intent_type] = score
        
        if intent_scores:
            # Return the intent with the highest score
            return max(intent_scores, key=intent_scores.get)
        else:
            return "general"
    
    def _extract_entities(self, message: str, intent_type: str) -> Dict[str, Any]:
        """
        Extract entities from the message based on intent type.
        
        Args:
            message: Original message
            intent_type: Detected intent type
            
        Returns:
            Dictionary of extracted entities
        """
        entities = {}
        
        if intent_type == "search":
            # Extract search query
            query = self._extract_search_query(message)
            if query:
                entities["query"] = query
        
        elif intent_type == "calculation":
            # Extract mathematical expression
            expression = self._extract_math_expression(message)
            if expression:
                entities["expression"] = expression
        
        elif intent_type == "time":
            # Extract time-related entities
            time_entities = self._extract_time_entities(message)
            entities.update(time_entities)
        
        return entities
    
    def _extract_search_query(self, message: str) -> Optional[str]:
        """Extract search query from message."""
        # Remove common search prefixes
        query = message
        prefixes = ["搜索", "查找", "找", "search", "find", "什么是", "告诉我关于"]
        
        for prefix in prefixes:
            if query.lower().startswith(prefix.lower()):
                query = query[len(prefix):].strip()
                break
        
        return query if query and query != message else None
    
    def _extract_math_expression(self, message: str) -> Optional[str]:
        """Extract mathematical expression from message."""
        # Look for mathematical patterns
        math_patterns = [
            r'\d+\s*[+\-*/]\s*\d+[\d+\-*/\s]*',  # Basic arithmetic
            r'sqrt\(\d+\)',  # Square root
            r'sin\([^)]+\)',  # Trigonometric functions
            r'cos\([^)]+\)',
            r'tan\([^)]+\)',
            r'log\([^)]+\)',
        ]
        
        for pattern in math_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return None
    
    def _extract_time_entities(self, message: str) -> Dict[str, Any]:
        """Extract time-related entities from message."""
        entities = {}
        
        # Time operation patterns
        if any(word in message.lower() for word in ["时间", "time", "几点"]):
            entities["operation"] = "current_time"
        elif any(word in message.lower() for word in ["日期", "date", "今天", "today", "几号"]):
            entities["operation"] = "current_date"
        elif any(word in message.lower() for word in ["现在", "now", "当前", "current"]):
            entities["operation"] = "current_datetime"
        
        # Timezone extraction (basic)
        timezone_patterns = [
            r"UTC[+-]?\d*",
            r"北京时间|beijing time",
            r"东京时间|tokyo time",
            r"纽约时间|new york time"
        ]
        
        for pattern in timezone_patterns:
            match = re.search(pattern, message, re.IGNORECASE)
            if match:
                entities["timezone"] = match.group(0)
                break
        
        return entities
    
    def _calculate_confidence(self, message: str, intent_type: str) -> float:
        """
        Calculate confidence score for the detected intent.
        
        Args:
            message: Preprocessed message
            intent_type: Detected intent type
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        if intent_type == "general":
            return 0.5  # Default confidence for general intent
        
        patterns = self.intent_patterns.get(intent_type, [])
        if not patterns:
            return 0.0
        
        matches = 0
        for pattern in patterns:
            if re.search(pattern, message, re.IGNORECASE):
                matches += 1
        
        # Calculate confidence based on pattern matches
        confidence = min(matches / len(patterns) + 0.3, 1.0)
        return confidence
    
    def get_supported_intents(self) -> List[str]:
        """Get list of supported intent types."""
        return list(self.intent_patterns.keys())
    
    def add_intent_pattern(self, intent_type: str, pattern: str) -> None:
        """
        Add a new pattern for an intent type.
        
        Args:
            intent_type: Intent type to add pattern for
            pattern: Regular expression pattern
        """
        if intent_type not in self.intent_patterns:
            self.intent_patterns[intent_type] = []
        
        self.intent_patterns[intent_type].append(pattern)
        logger.info(f"Added pattern '{pattern}' for intent '{intent_type}'")
