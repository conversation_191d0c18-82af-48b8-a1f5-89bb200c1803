"""
Simple test to verify TTS filtering logic.
"""
import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from audio_agent.events.event_processor import EventProcessor
from audio_agent.config.settings import AudioConfig


async def test_filtering():
    """Test TTS filtering logic."""
    print("🧪 Testing TTS filtering logic...")
    
    # Create config and event processor
    config = AudioConfig(sample_rate=24000, bit_size=16)
    processor = EventProcessor(
        session_id="test-session",
        config=config,
        dialog_client_actor=None  # We'll mock this
    )
    
    # Test 1: Default TTS type during tool calling should be filtered
    print("\n📝 Test 1: Default TTS type during tool calling")
    processor.tool_calling_active = True
    processor.current_tts_should_skip = False  # Reset
    
    # Simulate TTS start event with default type
    tts_start_message = {
        'message_type': 'SERVER_ACK',
        'event': 350,  # TTS_SENTENCE_START
        'session_id': 'test-session',
        'payload': {
            'tts_type': 'default',
            'text': 'This is server auto response',
            'tts_task_id': 'task-123'
        }
    }
    
    await processor.process_websocket_message(tts_start_message)
    
    if processor.current_tts_should_skip:
        print("✅ PASS: Default TTS type correctly marked for filtering")
    else:
        print("❌ FAIL: Default TTS type should be filtered")
        return False
    
    # Test 2: chat_tts_text type during tool calling should be allowed
    print("\n📝 Test 2: chat_tts_text type during tool calling")
    processor.current_tts_should_skip = True  # Reset to opposite
    
    tts_start_message_tool = {
        'message_type': 'SERVER_ACK',
        'event': 350,  # TTS_SENTENCE_START
        'session_id': 'test-session',
        'payload': {
            'tts_type': 'chat_tts_text',
            'text': 'This is tool result response',
            'tts_task_id': 'task-456'
        }
    }
    
    await processor.process_websocket_message(tts_start_message_tool)
    
    if not processor.current_tts_should_skip:
        print("✅ PASS: chat_tts_text type correctly allowed")
    else:
        print("❌ FAIL: chat_tts_text type should be allowed")
        return False
    
    # Test 3: Normal mode (not tool calling) should allow all
    print("\n📝 Test 3: Normal mode should allow all TTS types")
    processor.tool_calling_active = False
    processor.current_tts_should_skip = True  # Reset to opposite
    
    await processor.process_websocket_message(tts_start_message)  # default type
    
    if not processor.current_tts_should_skip:
        print("✅ PASS: Normal mode correctly allows all TTS types")
    else:
        print("❌ FAIL: Normal mode should allow all TTS types")
        return False
    
    print("\n🎉 All tests passed! TTS filtering logic is working correctly.")
    return True


if __name__ == "__main__":
    result = asyncio.run(test_filtering())
    if not result:
        sys.exit(1)
