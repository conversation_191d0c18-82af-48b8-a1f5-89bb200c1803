"""
Event processor that integrates with Volcengine's event system.

This module processes events and coordinates between different components
following the official Volcengine architecture.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid

from .event_types import EventType, BaseEvent, ServerEvent, LLMEvent, DialogEvent
from .event_handler import <PERSON><PERSON>and<PERSON>
from .llm_enhancer import LLMEnhancer

logger = logging.getLogger(__name__)


class EventProcessor:
    """
    Event processor that coordinates the entire event-driven flow.
    
    This implements the complete flow shown in Volcengine's documentation:
    1. Audio input → ASR → Intent recognition
    2. LLM enhancement with tool calling
    3. Response generation → TTS → Audio output
    """
    
    def __init__(self, session_id: str, config: Any, dialog_client_actor=None):
        """
        Initialize event processor.

        Args:
            session_id: Session ID
            config: Audio agent configuration
            dialog_client_actor: DialogClientActor for sending messages to server
        """
        self.session_id = session_id
        self.config = config
        self.dialog_client_actor = dialog_client_actor
        self.event_handler = EventHandler(session_id, self)
        self.llm_enhancer = LLMEnhancer(config)

        # State tracking
        self.current_user_input = ""
        self.conversation_context: List[Dict[str, Any]] = []
        self.is_processing_llm = False
        self.processing_task = None
        self.llm_response_sent = False  # Flag to prevent original system from responding

        # Tool calling state to filter unwanted server responses
        self.tool_calling_active = False
        self.server_auto_response_started = False
        self.tool_response_tts_task_id = None
        self.current_tts_should_skip = False  # Flag to skip current TTS audio
        
        # Register enhanced handlers
        self._register_enhanced_handlers()
        
        logger.info(f"EventProcessor initialized for session {session_id}")
    
    def _register_enhanced_handlers(self):
        """Register enhanced event handlers that include LLM processing."""

        # Handle ASR response to trigger LLM enhancement
        self.event_handler.register_handler(
            EventType.ASR_RESPONSE,
            self._handle_asr_with_llm_enhancement
        )

        # Handle ASR ended to finalize speech recognition
        self.event_handler.register_handler(
            EventType.ASR_ENDED,
            self._handle_asr_ended_with_enhancement
        )

        # Handle LLM tool calls
        self.event_handler.register_handler(
            EventType.LLM_TOOL_CALL,
            self._handle_llm_tool_execution
        )

        # Handle LLM final results
        self.event_handler.register_handler(
            EventType.LLM_FINAL_RESULT,
            self._handle_llm_response_generation
        )
    
    async def process_websocket_message(self, message: Dict[str, Any]) -> Optional[BaseEvent]:
        """
        Process incoming WebSocket message through event system.
        
        Args:
            message: Raw WebSocket message
            
        Returns:
            Processed event
        """
        return await self.event_handler.handle_server_event(message)
    
    async def start_processing(self):
        """Start the event processing loop as a background task."""
        logger.info("Starting event processing")
        # Start event processing as a background task, don't await it
        self.processing_task = asyncio.create_task(self.event_handler.process_events())
        logger.info("Event processing task started")
    
    def stop_processing(self):
        """Stop event processing."""
        logger.info("Stopping event processing")

        # Cancel the processing task
        if self.processing_task and not self.processing_task.done():
            self.processing_task.cancel()

        self.event_handler.stop_processing()
    
    async def _handle_asr_with_llm_enhancement(self, event: BaseEvent):
        """
        Handle ASR response event (451) - collect speech recognition results.

        This collects interim and final ASR results.
        """
        try:
            logger.info(f"🎤 ASR event received: {event.event_type}")

            # Extract ASR results from payload
            payload = event.payload or {}
            results = payload.get('results', [])
            logger.info(f"🎤 ASR results count: {len(results)}")
            logger.info(f"🎤 Payload keys: {list(payload.keys()) if payload else 'None'}")

            for result in results:
                text = result.get('text', '').strip()
                is_interim = result.get('is_interim', False)
                logger.info(f"🎤 ASR text: '{text}', interim: {is_interim}")

                if text:
                    if is_interim:
                        # Update current partial input
                        self.current_user_input = text
                        logger.info(f"🎤 ASR interim result saved: '{text}'")

                        # Early tool detection: if text looks like it needs tools, prepare
                        if len(text.strip()) > 8:  # Only check longer texts to avoid false positives
                            await self._early_tool_detection(text)

                        # AGGRESSIVE: Send placeholder immediately when we detect tool-requiring patterns
                        # This is more aggressive than waiting for ASR end
                        if len(text.strip()) > 10 and self._quick_tool_pattern_check(text):
                            if not hasattr(self, '_aggressive_placeholder_sent'):
                                logger.info(f"🚨 AGGRESSIVE: Sending placeholder during ASR interim for: '{text}'")
                                if self.dialog_client_actor:
                                    try:
                                        if hasattr(self.dialog_client_actor, 'send_chat_tts_text') and hasattr(self.dialog_client_actor.send_chat_tts_text, 'remote'):
                                            await self.dialog_client_actor.send_chat_tts_text.remote("", start=True, end=False)
                                        else:
                                            await self.dialog_client_actor.send_chat_tts_text("", start=True, end=False)
                                        self._aggressive_placeholder_sent = True
                                        logger.info("🚨 AGGRESSIVE placeholder sent during ASR interim")
                                    except Exception as e:
                                        logger.error(f"❌ Failed to send aggressive placeholder: {e}")
                    else:
                        # Final result for this segment
                        self.current_user_input = text
                        logger.info(f"🎤 ASR final result saved: '{text}'")

        except Exception as e:
            logger.error(f"Failed to handle ASR response: {e}")

    async def _handle_asr_ended_with_enhancement(self, event: BaseEvent):
        """
        Handle ASR ended event (459) - user finished speaking, trigger LLM enhancement.

        This follows the Volcengine flow: ASR End → Intent Recognition → LLM Enhancement
        """
        try:
            logger.info(f"🏁 ASR ended event received: {event.event_type}")
            logger.info(f"🏁 Current user input: '{self.current_user_input}'")

            if self.is_processing_llm:
                logger.info("LLM is already processing, skipping new request")
                return

            if not self.current_user_input:
                logger.warning("🚨 No user input available when ASR ended")
                return

            user_input = self.current_user_input.strip()
            if not user_input:
                logger.warning("🚨 Empty user input when ASR ended")
                return

            logger.info(f"🚀 ASR ended, processing user input: '{user_input}'")

            # Quick check if this might need tools - use smarter detection
            if self._might_need_tools(user_input):
                self.tool_calling_active = True
                self.server_auto_response_started = False
                # CRITICAL: Set filter flag immediately to catch early audio data
                self.current_tts_should_skip = True
                logger.info("🔧 Tool calling activated - will filter server auto-responses")
                logger.info("🚫 Pre-emptively setting current_tts_should_skip=True to filter early audio")

            # CRITICAL: 按照火山引擎官方流程图，在ASREnded瞬间发送ChatTTSText(start)
            # 这是"当piquery查询时间结果无法满足，客户端会合成音频"的第一步
            if self.dialog_client_actor:
                try:
                    # 立即发送ChatTTSText(start)以阻止服务器自动回复
                    if hasattr(self.dialog_client_actor, 'send_chat_tts_text') and hasattr(self.dialog_client_actor.send_chat_tts_text, 'remote'):
                        await self.dialog_client_actor.send_chat_tts_text.remote("", start=True, end=False)
                    else:
                        await self.dialog_client_actor.send_chat_tts_text("", start=True, end=False)
                    logger.info("🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow")
                    self._immediate_placeholder_sent = True
                except Exception as e:
                    logger.error(f"❌ Failed to send ChatTTSText(start): {e}")
            else:
                logger.warning("⚠️ No DialogClientActor available for ChatTTSText(start)")

            # Reset placeholder flags for next interaction (but keep immediate flag during this processing)
            if hasattr(self, '_early_placeholder_sent'):
                delattr(self, '_early_placeholder_sent')

            # Check if we should use LLM enhancement
            if self._should_use_llm_enhancement(user_input):
                logger.info(f"🤖 Using LLM enhancement for: '{user_input}'")

                # Create LLM event for processing
                llm_event = LLMEvent(
                    event_type=EventType.LLM_START,
                    timestamp=datetime.now(),
                    session_id=self.session_id,
                    event_id=str(uuid.uuid4()),
                    data={"user_input": user_input},
                    user_message=user_input,
                    context={"conversation_history": self.conversation_context}
                )

                # Process with LLM enhancer
                self.is_processing_llm = True
                await self._process_with_llm_enhancer(llm_event)
            else:
                logger.info(f"💬 Using normal conversation for: '{user_input}'")
                # Let the server handle normal conversation without LLM enhancement
                # Reset any flags that might have been set
                self.is_processing_llm = False
                if hasattr(self, '_immediate_placeholder_sent'):
                    delattr(self, '_immediate_placeholder_sent')

        except Exception as e:
            logger.error(f"Failed to handle ASR ended with LLM enhancement: {e}")
            self.is_processing_llm = False
    
    async def _process_with_llm_enhancer(self, llm_event: LLMEvent):
        """Process user input with LLM enhancement."""
        try:
            # Step 1: Intent recognition
            intent_result = await self.llm_enhancer.recognize_intent(
                llm_event.user_message,
                self.conversation_context
            )
            
            logger.info(f"Intent recognized: {intent_result}")
            
            # Step 2: Check if tool calling is needed
            if intent_result.get("requires_tools", False):
                logger.info(f"🔧 Tools needed, sending placeholder first: {intent_result.get('tools', [])}")

                # Check if immediate placeholder was sent at ASR end
                if hasattr(self, '_immediate_placeholder_sent'):
                    logger.info("✅ Immediate placeholder already sent at ASR end, proceeding with tool execution")
                else:
                    logger.warning("⚠️ No immediate placeholder was sent! Sending now as backup")
                    # Send backup placeholder
                    if self.dialog_client_actor:
                        try:
                            if hasattr(self.dialog_client_actor, 'send_chat_tts_text') and hasattr(self.dialog_client_actor.send_chat_tts_text, 'remote'):
                                await self.dialog_client_actor.send_chat_tts_text.remote("", start=True, end=False)
                            else:
                                await self.dialog_client_actor.send_chat_tts_text("", start=True, end=False)
                            logger.info("✅ Backup placeholder sent")
                        except Exception as e:
                            logger.error(f"❌ Failed to send backup placeholder: {e}")

                # Execute tools immediately to get results
                logger.info(f"🔧 Executing tools: {intent_result.get('tools', [])}")

                # Execute tools using LLM enhancer
                tool_results = await self.llm_enhancer.execute_tools(
                    llm_event.user_message,
                    intent_result.get("tools", []),
                    self.conversation_context
                )

                # Generate enhanced response with tool results
                response = await self.llm_enhancer.generate_enhanced_response(
                    llm_event.user_message,
                    tool_results,
                    self.conversation_context
                )

                # Send final tool result to server
                await self._send_tool_result_to_server(
                    llm_event.user_message,
                    tool_results,
                    response
                )
            else:
                # Direct response generation
                await self._generate_direct_response(llm_event, intent_result)
                
        except Exception as e:
            logger.error(f"Failed to process with LLM enhancer: {e}")
            self.is_processing_llm = False
    
    async def _handle_llm_tool_execution(self, event: LLMEvent):
        """Handle LLM tool execution."""
        try:
            logger.info(f"Executing tools for intent: {event.intent}")
            
            # Execute tools using LLM enhancer
            tool_results = await self.llm_enhancer.execute_tools(
                event.user_message,
                event.tool_calls or [],
                self.conversation_context
            )
            
            # Create tool result event
            tool_result_event = LLMEvent(
                event_type=EventType.LLM_TOOL_RESULT,
                timestamp=datetime.now(),
                session_id=self.session_id,
                event_id=str(uuid.uuid4()),
                data={
                    "user_input": event.user_message,
                    "tool_results": tool_results
                },
                user_message=event.user_message,
                tool_results=tool_results
            )
            
            # Generate enhanced response with tool results
            await self._generate_enhanced_response(tool_result_event)
            
        except Exception as e:
            logger.error(f"Failed to execute LLM tools: {e}")
            self.is_processing_llm = False
    
    async def _generate_direct_response(self, llm_event: LLMEvent, intent_result: Dict[str, Any]):
        """Generate direct response without tools."""
        try:
            response = await self.llm_enhancer.generate_response(
                llm_event.user_message,
                self.conversation_context,
                intent_result
            )
            
            await self._finalize_response(llm_event.user_message, response)
            
        except Exception as e:
            logger.error(f"Failed to generate direct response: {e}")
            self.is_processing_llm = False
    
    async def _generate_enhanced_response(self, tool_result_event: LLMEvent):
        """Generate enhanced response with tool results and send to server."""
        try:
            response = await self.llm_enhancer.generate_enhanced_response(
                tool_result_event.user_message,
                tool_result_event.tool_results or [],
                self.conversation_context
            )

            # Send tool result to server instead of generating local response
            await self._send_tool_result_to_server(
                tool_result_event.user_message,
                tool_result_event.tool_results or [],
                response
            )

        except Exception as e:
            logger.error(f"Failed to generate enhanced response: {e}")
            self.is_processing_llm = False
    
    async def _finalize_response(self, user_input: str, ai_response: str):
        """Finalize the response and update conversation history."""
        try:
            # Update conversation history
            self.conversation_context.append({
                "role": "user",
                "content": user_input,
                "timestamp": datetime.now().isoformat()
            })
            self.conversation_context.append({
                "role": "assistant", 
                "content": ai_response,
                "timestamp": datetime.now().isoformat()
            })
            
            # Keep only last 10 exchanges
            if len(self.conversation_context) > 20:
                self.conversation_context = self.conversation_context[-20:]
            
            # Create final LLM result event
            final_event = LLMEvent(
                event_type=EventType.LLM_FINAL_RESULT,
                timestamp=datetime.now(),
                session_id=self.session_id,
                event_id=str(uuid.uuid4()),
                data={
                    "user_input": user_input,
                    "ai_response": ai_response
                },
                user_message=user_input,
                ai_response=ai_response
            )
            
            await self.event_handler._process_single_event(final_event)
            
            logger.info(f"Response generated: '{ai_response}'")
            
        except Exception as e:
            logger.error(f"Failed to finalize response: {e}")
        finally:
            self.is_processing_llm = False
    
    async def _handle_llm_response_generation(self, event: LLMEvent):
        """Handle final LLM response - trigger TTS and send to user."""
        logger.info(f"LLM response ready for TTS: {event.ai_response}")

        # Send the LLM enhanced response directly to the user
        # This bypasses the original dialog system to prevent conflicts
        try:
            await self._send_llm_response_to_user(event.ai_response)
            self.llm_response_sent = True  # Mark that LLM has sent a response
            logger.info("LLM enhanced response sent to user successfully")
        except Exception as e:
            logger.error(f"Failed to send LLM response to user: {e}")

    async def _send_llm_response_to_user(self, response_text: str):
        """
        Send LLM enhanced response to server for TTS conversion and audio playback.
        """
        # Send the LLM enhanced response to server for proper TTS
        if self.dialog_client_actor:
            try:
                # Send the LLM enhanced response as ChatTTSText stream
                if hasattr(self.dialog_client_actor, 'send_chat_tts_text') and hasattr(self.dialog_client_actor.send_chat_tts_text, 'remote'):
                    # Send middle packet with the LLM enhanced response
                    await self.dialog_client_actor.send_chat_tts_text.remote(response_text, start=False, end=False)
                    # Send end packet to complete the stream
                    await self.dialog_client_actor.send_chat_tts_text.remote("", start=False, end=True)
                else:
                    # Send middle packet with the LLM enhanced response
                    await self.dialog_client_actor.send_chat_tts_text(response_text, start=False, end=False)
                    # Send end packet to complete the stream
                    await self.dialog_client_actor.send_chat_tts_text("", start=False, end=True)

                logger.info(f"✅ LLM enhanced response sent to server for TTS: '{response_text[:50]}...'")

                # Don't set llm_response_sent here - let the server handle TTS
                # The server will generate proper TTS audio for our enhanced response

            except Exception as e:
                logger.error(f"❌ Failed to send LLM response to server: {e}")
                # Fallback to mock TTS if server communication fails
                await self._create_mock_tts_response(response_text)
        else:
            logger.warning("⚠️ No DialogClientActor available, using mock TTS")
            await self._create_mock_tts_response(response_text)

    async def _create_mock_tts_response(self, response_text: str):
        """Create a mock TTS response as fallback."""
        # Create a mock audio response (in practice, this would be real TTS audio)
        mock_audio_data = b"mock_audio_data_for_" + response_text.encode('utf-8')

        # Create a TTS response event to simulate the audio response
        from datetime import datetime
        import uuid

        tts_event = ServerEvent(
            event_type=EventType.TTS_RESPONSE,
            timestamp=datetime.now(),
            session_id=self.session_id,
            event_id=str(uuid.uuid4()),
            data={
                "text": response_text,
                "audio_length": len(mock_audio_data),
                "event": 352  # TTS audio response
            },
            message_type="SERVER_ACK",
            payload=mock_audio_data
        )

        # Process the TTS event to trigger audio playback
        await self.event_handler._process_single_event(tts_event)

        logger.info(f"Mock TTS response created for: '{response_text[:50]}...'")

        # Set the flag to indicate LLM response was sent
        self.llm_response_sent = True

    async def _send_tool_result_to_server(self, user_input: str, tool_results: list, ai_response: str):
        """
        Send tool calling result to server via DialogClientActor.

        This is the correct way to handle tool calling according to Volcengine's official practice:
        1. Client executes tools
        2. Client sends tool results to server
        3. Server generates final response based on tool results
        4. Server sends TTS response back to client
        """
        try:
            # We need access to the DialogClientActor to send the tool result
            # For now, we'll log this and mark that we need to implement the connection
            logger.info(f"🔧 Tool result ready to send to server:")
            logger.info(f"   User input: {user_input}")
            logger.info(f"   Tool results: {len(tool_results)} tools executed")
            logger.info(f"   AI response: {ai_response}")

            # Mark that LLM has processed this request
            self.llm_response_sent = True

            # Send tool result to server using streaming ChatTTSText format
            if self.dialog_client_actor:
                try:
                    # Send the actual tool result as continuation of the stream
                    # This replaces the placeholder "正在为您查询" with the actual result
                    if hasattr(self.dialog_client_actor, 'send_chat_tts_text') and hasattr(self.dialog_client_actor.send_chat_tts_text, 'remote'):
                        # Send middle packet with the actual result
                        await self.dialog_client_actor.send_chat_tts_text.remote(ai_response, start=False, end=False)
                        # Send end packet to complete the stream
                        await self.dialog_client_actor.send_chat_tts_text.remote("", start=False, end=True)
                    else:
                        # Send middle packet with the actual result
                        await self.dialog_client_actor.send_chat_tts_text(ai_response, start=False, end=False)
                        # Send end packet to complete the stream
                        await self.dialog_client_actor.send_chat_tts_text("", start=False, end=True)

                    logger.info("✅ Tool result sent to server successfully using streaming ChatTTSText")

                except Exception as e:
                    logger.error(f"❌ Failed to send tool result via ChatTTSText: {e}")
            else:
                logger.warning("⚠️ No DialogClientActor available, cannot send tool result to server")

        except Exception as e:
            logger.error(f"Failed to send tool result to server: {e}")
        finally:
            self.is_processing_llm = False
            # DON'T reset tool_calling_active here - we need to keep it active
            # to filter server auto-responses until our TTS response is complete
            # The flag will be reset when TTS synthesis ends (event 359)
            logger.info("🔧 Tool processing complete, but keeping tool_calling_active=True to filter server responses")

            # Reset immediate placeholder flag after tool processing is complete
            if hasattr(self, '_immediate_placeholder_sent'):
                delattr(self, '_immediate_placeholder_sent')

    def _might_need_tools(self, user_input: str) -> bool:
        """
        Quick check if user input might need tools.
        This is a fast pre-filter before the full LLM intent recognition.
        """
        user_input_lower = user_input.lower()

        # Time-related patterns that are likely NOT queries
        non_query_patterns = [
            "记得", "想起", "刚才", "之前", "告诉我", "说了", "问了", "提到",
            "你还记得", "还记得吗", "刚刚", "刚才问", "之前问", "刚问了"
        ]

        # If it contains time keywords but also conversation patterns, likely not a tool call
        time_keywords = ["时间", "几点", "现在"]
        has_time_keywords = any(keyword in user_input_lower for keyword in time_keywords)
        has_conversation_context = any(pattern in user_input_lower for pattern in non_query_patterns)

        if has_time_keywords and has_conversation_context:
            logger.info(f"🤔 Time mentioned in conversation context, skipping tool activation: '{user_input}'")
            return False

        # Check for clear tool patterns
        tool_patterns = ["几点了", "什么时间", "现在时间", "时间是多少", "多少时间", "天气", "温度", "计算", "算", "搜索", "查"]
        if any(pattern in user_input_lower for pattern in tool_patterns):
            logger.info(f"✅ Tool pattern detected: '{user_input}'")
            return True

        # If it has time keywords and is a question, likely a time query
        if has_time_keywords:
            question_indicators = ["？", "?", "吗", "呢", "啊", "呀"]
            has_question = any(indicator in user_input_lower for indicator in question_indicators)
            if has_question:
                logger.info(f"✅ Time question detected: '{user_input}'")
                return True

        # Default: no tools needed
        return False

    def _should_use_llm_enhancement(self, user_input: str) -> bool:
        """
        Determine if we should use LLM enhancement for this user input.

        LLM enhancement should only be used for:
        1. Tool calling scenarios (time queries, calculations, etc.)
        2. Complex questions that need intelligent processing
        3. NOT for simple greetings, acknowledgments, or casual conversation
        """
        user_input_lower = user_input.lower()

        # Always use LLM for tool calling scenarios
        if self._might_need_tools(user_input):
            logger.info(f"🔧 LLM enhancement needed for tool calling: '{user_input}'")
            return True

        # Patterns that definitely DON'T need LLM enhancement (simple conversation)
        simple_patterns = [
            # Greetings
            "你好", "hi", "hello", "嗨", "早上好", "晚上好", "下午好",
            # Acknowledgments
            "好的", "ok", "okay", "嗯", "是的", "对", "没错", "谢谢", "thank you",
            # Simple responses
            "不错", "还行", "挺好", "一般", "不太好", "很好", "太好了",
            # Casual conversation
            "怎么样", "还好吗", "在干嘛", "忙吗", "有空吗",
            # Short phrases (less than 5 characters, likely simple)
        ]

        # If it matches simple patterns, don't use LLM
        if any(pattern in user_input_lower for pattern in simple_patterns):
            logger.info(f"💬 Simple conversation detected, no LLM enhancement needed: '{user_input}'")
            return False

        # If it's very short (less than 4 characters), likely simple
        if len(user_input.strip()) < 4:
            logger.info(f"💬 Very short input, no LLM enhancement needed: '{user_input}'")
            return False

        # Patterns that DO need LLM enhancement (complex questions)
        complex_patterns = [
            "为什么", "怎么", "如何", "什么是", "能不能", "可以吗", "帮我", "告诉我",
            "why", "how", "what is", "can you", "could you", "help me", "tell me",
            "解释", "说明", "分析", "比较", "推荐", "建议"
        ]

        # If it matches complex patterns, use LLM
        if any(pattern in user_input_lower for pattern in complex_patterns):
            logger.info(f"🤖 Complex question detected, using LLM enhancement: '{user_input}'")
            return True

        # For medium-length inputs (5-20 characters), use simple heuristics
        if 5 <= len(user_input.strip()) <= 20:
            # If it contains question marks, likely a question
            if "？" in user_input or "?" in user_input:
                logger.info(f"🤖 Question detected, using LLM enhancement: '{user_input}'")
                return True
            else:
                logger.info(f"💬 Medium-length statement, no LLM enhancement needed: '{user_input}'")
                return False

        # For longer inputs (>20 characters), likely complex, use LLM
        if len(user_input.strip()) > 20:
            logger.info(f"🤖 Long input detected, using LLM enhancement: '{user_input}'")
            return True

        # Default: don't use LLM for unclear cases
        logger.info(f"💬 Default: no LLM enhancement for: '{user_input}'")
        return False

    def _extract_user_input_from_asr(self, event: BaseEvent) -> Optional[str]:
        """Extract user input text from ASR event."""
        try:
            if hasattr(event, 'data') and event.data:
                # Try different possible keys for ASR result
                for key in ['text', 'transcription', 'result', 'content']:
                    if key in event.data:
                        return event.data[key].strip()
                
                # If payload contains text
                if 'payload' in event.data and isinstance(event.data['payload'], dict):
                    payload = event.data['payload']
                    for key in ['text', 'transcription', 'result', 'content']:
                        if key in payload:
                            return payload[key].strip()
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to extract user input from ASR: {e}")
            return None

    async def _early_tool_detection(self, text: str):
        """
        Early detection of tool calling needs during ASR interim phase.
        This allows us to send ChatTTSText even earlier to prevent server default response.
        """
        try:
            # Quick pattern matching for common tool-requiring queries
            tool_patterns = [
                "时间", "几点", "现在", "what time", "time",
                "天气", "weather", "温度", "temperature",
                "计算", "算", "calculate", "math",
                "搜索", "查", "search", "find"
            ]

            text_lower = text.lower()
            needs_tools = any(pattern in text_lower for pattern in tool_patterns)

            if needs_tools and not hasattr(self, '_early_placeholder_sent'):
                logger.info(f"🔧 Early tool detection: '{text}' likely needs tools")

                # Send early placeholder to prevent server default response
                if self.dialog_client_actor:
                    try:
                        if hasattr(self.dialog_client_actor, 'send_chat_tts_text') and hasattr(self.dialog_client_actor.send_chat_tts_text, 'remote'):
                            await self.dialog_client_actor.send_chat_tts_text.remote("", start=True, end=False)
                        else:
                            await self.dialog_client_actor.send_chat_tts_text("", start=True, end=False)

                        self._early_placeholder_sent = True
                        logger.info("✅ Early placeholder ChatTTSText sent during ASR interim")
                    except Exception as e:
                        logger.error(f"❌ Failed to send early placeholder: {e}")

        except Exception as e:
            logger.error(f"Failed in early tool detection: {e}")

    def _quick_tool_pattern_check(self, text: str) -> bool:
        """
        Quick check if text contains tool-requiring patterns.
        Used for sending tool calling intent at ASR end.
        """
        tool_patterns = [
            "时间", "几点", "现在", "什么时间",
            "天气", "温度",
            "计算", "算",
            "搜索", "查"
        ]

        text_lower = text.lower()
        return any(pattern in text_lower for pattern in tool_patterns)

    def _quick_tool_pattern_check(self, text: str) -> bool:
        """
        Quick check if text contains tool-requiring patterns.
        Used for aggressive placeholder sending during ASR interim.
        """
        tool_patterns = [
            "时间", "几点", "现在", "什么时间",
            "天气", "温度",
            "计算", "算",
            "搜索", "查"
        ]

        text_lower = text.lower()
        return any(pattern in text_lower for pattern in tool_patterns)
