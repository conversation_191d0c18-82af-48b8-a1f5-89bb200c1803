# AudioAgent 配置指南

本文档详细介绍了AudioAgent的所有配置选项，包括新增的智能代理、工具调用和知识库功能。

## 📋 配置文件

AudioAgent使用环境变量进行配置，主要配置文件：

- `.env` - 主配置文件（包含敏感信息，不应提交到版本控制）
- `.env.example` - 配置模板文件

## 🔧 基础配置

### WebSocket API 配置

```bash
# WebSocket连接配置
AUDIO_AGENT_WEBSOCKET_BASE_URL=wss://openspeech.bytedance.com/api/v3/realtime/dialogue
AUDIO_AGENT_WEBSOCKET_APP_ID=your_app_id_here
AUDIO_AGENT_WEBSOCKET_ACCESS_KEY=your_access_key_here
AUDIO_AGENT_WEBSOCKET_RESOURCE_ID=volc.speech.dialog
AUDIO_AGENT_WEBSOCKET_APP_KEY=PlgvMymc7f3tQnJ6
AUDIO_AGENT_WEBSOCKET_CONNECT_ID=
```

### 音频配置

```bash
# 输入音频配置
AUDIO_AGENT_INPUT_AUDIO_CHUNK=512
AUDIO_AGENT_INPUT_AUDIO_FORMAT=pcm
AUDIO_AGENT_INPUT_AUDIO_CHANNELS=1
AUDIO_AGENT_INPUT_AUDIO_SAMPLE_RATE=16000
AUDIO_AGENT_INPUT_AUDIO_BIT_SIZE=8

# 输出音频配置
AUDIO_AGENT_OUTPUT_AUDIO_CHUNK=512
AUDIO_AGENT_OUTPUT_AUDIO_FORMAT=pcm
AUDIO_AGENT_OUTPUT_AUDIO_CHANNELS=1
AUDIO_AGENT_OUTPUT_AUDIO_SAMPLE_RATE=24000
AUDIO_AGENT_OUTPUT_AUDIO_BIT_SIZE=1
```

### VAD 语音活动检测

```bash
# VAD配置
AUDIO_AGENT_INPUT_AUDIO_VAD_ENABLED=true
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.5
AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS=30
AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS=200
```

### 中断功能

```bash
# 音频播放中断配置
AUDIO_AGENT_INTERRUPT_ENABLED=true
AUDIO_AGENT_INTERRUPT_RESPONSE_DELAY_MS=25
AUDIO_AGENT_INTERRUPT_MIN_SPEECH_DURATION_MS=200
AUDIO_AGENT_INTERRUPT_DEBOUNCE_TIME_MS=100
```

## 🤖 智能代理配置

### 基础设置

```bash
# 启用智能代理功能
AUDIO_AGENT_INTELLIGENT_ENABLED=true

# 启用工具调用功能
AUDIO_AGENT_INTELLIGENT_TOOL_CALLING_ENABLED=true

# 启用知识检索功能
AUDIO_AGENT_INTELLIGENT_KNOWLEDGE_RETRIEVAL_ENABLED=true

# 工具执行超时时间（秒）
AUDIO_AGENT_INTELLIGENT_TOOL_EXECUTION_TIMEOUT=30.0

# 最大对话历史记录数
AUDIO_AGENT_INTELLIGENT_MAX_CONVERSATION_HISTORY=50
```

### 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | bool | true | 是否启用智能代理功能 |
| `tool_calling_enabled` | bool | true | 是否启用工具调用 |
| `knowledge_retrieval_enabled` | bool | true | 是否启用知识检索 |
| `tool_execution_timeout` | float | 30.0 | 工具执行超时时间（秒） |
| `max_conversation_history` | int | 50 | 最大对话历史记录数 |

## 🔧 工具系统配置

### 工具启用设置

```bash
# 启用的工具列表（逗号分隔，不要有空格）
AUDIO_AGENT_TOOLS_ENABLED_TOOLS=web_search,calculator,datetime

# 单独控制各工具
AUDIO_AGENT_TOOLS_WEB_SEARCH_ENABLED=true
AUDIO_AGENT_TOOLS_CALCULATOR_ENABLED=true
AUDIO_AGENT_TOOLS_DATETIME_ENABLED=true
```

### 工具参数设置

```bash
# 搜索结果最大数量
AUDIO_AGENT_TOOLS_SEARCH_MAX_RESULTS=5

# 搜索请求超时时间（秒）
AUDIO_AGENT_TOOLS_SEARCH_TIMEOUT=10.0
```

### 可用工具

| 工具名称 | 功能描述 | 触发示例 |
|----------|----------|----------|
| `web_search` | 网络搜索 | "搜索最新AI技术" |
| `calculator` | 数学计算 | "算一下2+3*4" |
| `datetime` | 时间查询 | "现在几点了？" |

### 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled_tools` | string | "web_search,calculator,datetime" | 启用的工具列表 |
| `web_search_enabled` | bool | true | 启用网络搜索工具 |
| `calculator_enabled` | bool | true | 启用计算器工具 |
| `datetime_enabled` | bool | true | 启用时间工具 |
| `search_max_results` | int | 5 | 搜索结果最大数量 |
| `search_timeout` | float | 10.0 | 搜索超时时间（秒） |

## 📚 知识库配置

### 基础设置

```bash
# 启用知识库功能
AUDIO_AGENT_KNOWLEDGE_ENABLED=true

# 知识库文件存储路径
AUDIO_AGENT_KNOWLEDGE_KNOWLEDGE_BASE_PATH=./knowledge

# 知识检索最大结果数
AUDIO_AGENT_KNOWLEDGE_MAX_RETRIEVAL_RESULTS=5

# 相似度阈值（0.0-1.0）
AUDIO_AGENT_KNOWLEDGE_SIMILARITY_THRESHOLD=0.7
```

### 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | bool | true | 是否启用知识库功能 |
| `knowledge_base_path` | string | "./knowledge" | 知识库文件存储路径 |
| `max_retrieval_results` | int | 5 | 知识检索最大结果数 |
| `similarity_threshold` | float | 0.7 | 相似度阈值（0.0-1.0） |

## ⚡ Ray 分布式配置

```bash
# Ray集群配置
AUDIO_AGENT_RAY_ADDRESS=auto
AUDIO_AGENT_RAY_DASHBOARD_HOST=127.0.0.1
AUDIO_AGENT_RAY_DASHBOARD_PORT=8265

# 可选的资源配置
# AUDIO_AGENT_RAY_NUM_CPUS=
# AUDIO_AGENT_RAY_NUM_GPUS=
# AUDIO_AGENT_RAY_OBJECT_STORE_MEMORY=
```

## 🎯 对话配置

```bash
# 对话设置
AUDIO_AGENT_SESSION_DIALOG_BOT_NAME=小凯
AUDIO_AGENT_SESSION_DIALOG_SYSTEM_ROLE=你使用活泼灵动的女声，性格开朗，热爱生活。
AUDIO_AGENT_SESSION_DIALOG_SPEAKING_STYLE=你的说话风格简洁明了，语速适中，语调自然。
AUDIO_AGENT_SESSION_DIALOG_STRICT_AUDIT=false
AUDIO_AGENT_SESSION_DIALOG_AUDIT_RESPONSE=支持客户自定义安全审核回复话术。
AUDIO_AGENT_SESSION_DIALOG_GREETING_MESSAGE=你好，我是小凯，有什么可以帮助您的？
```

## 📝 配置最佳实践

### 1. 环境变量管理

```bash
# 开发环境
cp .env.example .env
# 编辑 .env 文件，填入真实的API密钥

# 生产环境
# 使用环境变量或密钥管理系统
export AUDIO_AGENT_WEBSOCKET_APP_ID="your_app_id"
export AUDIO_AGENT_WEBSOCKET_ACCESS_KEY="your_access_key"
```

### 2. 功能开关

```bash
# 禁用某些功能以提高性能
AUDIO_AGENT_INTELLIGENT_TOOL_CALLING_ENABLED=false
AUDIO_AGENT_TOOLS_WEB_SEARCH_ENABLED=false

# 只启用特定工具
AUDIO_AGENT_TOOLS_ENABLED_TOOLS=calculator,datetime
```

### 3. 性能调优

```bash
# 减少超时时间以提高响应速度
AUDIO_AGENT_INTELLIGENT_TOOL_EXECUTION_TIMEOUT=15.0
AUDIO_AGENT_TOOLS_SEARCH_TIMEOUT=5.0

# 减少历史记录以节省内存
AUDIO_AGENT_INTELLIGENT_MAX_CONVERSATION_HISTORY=20

# 减少搜索结果数量
AUDIO_AGENT_TOOLS_SEARCH_MAX_RESULTS=3
```

### 4. 调试配置

```bash
# 启用详细日志
export PYTHONPATH="${PYTHONPATH}:./src"
export AUDIO_AGENT_LOG_LEVEL=DEBUG

# 禁用某些功能进行调试
AUDIO_AGENT_INTELLIGENT_ENABLED=false
AUDIO_AGENT_INTERRUPT_ENABLED=false
```

## 🔍 配置验证

使用以下代码验证配置是否正确：

```python
from audio_agent.config import get_config

config = get_config()
print(f"智能代理启用: {config.intelligent_agent.enabled}")
print(f"启用的工具: {config.tools.enabled_tools}")
print(f"知识库路径: {config.knowledge.knowledge_base_path}")
```

## ❗ 常见问题

### 1. 工具列表配置错误

**错误**: `enabled_tools` 配置不生效

**解决**: 确保使用逗号分隔，不要有空格：
```bash
# 正确
AUDIO_AGENT_TOOLS_ENABLED_TOOLS=web_search,calculator,datetime

# 错误
AUDIO_AGENT_TOOLS_ENABLED_TOOLS=web_search, calculator, datetime
```

### 2. 路径配置问题

**错误**: 知识库路径不存在

**解决**: 确保路径存在或使用绝对路径：
```bash
# 相对路径（推荐）
AUDIO_AGENT_KNOWLEDGE_KNOWLEDGE_BASE_PATH=./knowledge

# 绝对路径
AUDIO_AGENT_KNOWLEDGE_KNOWLEDGE_BASE_PATH=/path/to/knowledge
```

### 3. 布尔值配置

**错误**: 布尔值配置不生效

**解决**: 使用小写的 `true`/`false`：
```bash
# 正确
AUDIO_AGENT_INTELLIGENT_ENABLED=true

# 错误
AUDIO_AGENT_INTELLIGENT_ENABLED=True
AUDIO_AGENT_INTELLIGENT_ENABLED=1
```

## 📚 更多资源

- [工具调用文档](./TOOL_CALLING.md)
- [API文档](./API.md)
- [故障排除](./TROUBLESHOOTING.md)
