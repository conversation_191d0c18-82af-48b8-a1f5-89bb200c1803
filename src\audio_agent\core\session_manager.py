"""
Session manager for coordinating the audio dialog system.

This module provides the SessionManager class that coordinates all components
of the real-time audio dialog system using Ray actors.
"""

import asyncio
import logging
import uuid
from typing import Optional
import ray

from ..config import AudioAgentConfig
from .actors import AudioInputActor, AudioOutputActor, DialogClientActor
from ..agent import IntelligentAgentActor
from ..events import EventProcessor
from ..utils.logging_utils import log_response_summary, clean_data_for_logging


logger = logging.getLogger(__name__)


class SessionManager:
    """
    Session manager that coordinates the entire audio dialog system.
    
    This class will be implemented to use Ray actors for parallel processing
    of audio input, output, and WebSocket communication.
    """
    
    def __init__(self, config: AudioAgentConfig):
        """
        Initialize the session manager.

        Args:
            config: Configuration for the audio agent
        """
        self.config = config
        self.running = False
        self.session_id = str(uuid.uuid4())

        # Ray actors
        self.audio_input_actor = None
        self.audio_output_actor = None
        self.dialog_client_actor = None
        self.intelligent_agent_actor = None

        # Event processing system
        self.event_processor = None

        # Processing task
        self.processing_task = None

        # Interrupt management
        self.last_interrupt_time = None
        self.ai_is_speaking = False

        logger.info(f"SessionManager initialized with session ID: {self.session_id}")
    
    async def start(self) -> None:
        """
        Start the session manager and all Ray actors.

        This method will:
        1. Initialize Ray if not already initialized
        2. Create and start all Ray actors
        3. Set up communication between actors
        4. Start the audio processing pipeline
        """
        logger.info("Starting SessionManager...")

        try:
            # Initialize Ray if not already initialized
            if not ray.is_initialized():
                ray.init(
                    address=self.config.ray.address,
                    num_cpus=self.config.ray.num_cpus,
                    num_gpus=self.config.ray.num_gpus,
                    object_store_memory=self.config.ray.object_store_memory,
                    dashboard_host=self.config.ray.dashboard_host,
                    dashboard_port=self.config.ray.dashboard_port,
                    ignore_reinit_error=True
                )
                logger.info("Ray initialized")

            # Create Ray actors
            self.audio_input_actor = AudioInputActor.remote(self.config.input_audio)
            self.audio_output_actor = AudioOutputActor.remote(self.config.output_audio)
            self.dialog_client_actor = DialogClientActor.remote(self.config)

            # Create intelligent agent if enabled
            if self.config.intelligent_agent.enabled:
                self.intelligent_agent_actor = IntelligentAgentActor.remote(self.config)
                logger.info("Intelligent agent actor created")

            logger.info("Ray actors created")

            # Initialize event processing system with DialogClientActor access
            self.event_processor = EventProcessor(self.session_id, self.config, self.dialog_client_actor)
            logger.info("Event processor initialized")

            # Start audio input and output
            input_started = await self.audio_input_actor.start_recording.remote()
            output_started = await self.audio_output_actor.start_playback.remote()

            if not input_started or not output_started:
                raise RuntimeError("Failed to start audio input/output")

            # Connect to dialog service
            connected = await self.dialog_client_actor.connect.remote(self.session_id)
            if not connected:
                raise RuntimeError("Failed to connect to dialog service")

            # Start event processing
            event_started = await self.dialog_client_actor.start_event_processing.remote()
            if not event_started:
                logger.warning("Failed to start event processing, continuing without enhanced features")

            # Start event processor
            if self.event_processor:
                await self.event_processor.start_processing()
                logger.info("Event processor started")

            # Start the processing pipeline
            self.running = True
            self.processing_task = asyncio.create_task(self._processing_loop())

            logger.info("SessionManager started successfully with event system")

        except Exception as e:
            logger.error(f"Failed to start SessionManager: {e}")
            await self.stop()
            raise
    
    async def stop(self) -> None:
        """
        Stop the session manager and clean up resources.

        This method will:
        1. Stop all Ray actors gracefully
        2. Clean up audio resources
        3. Close WebSocket connections
        4. Shutdown Ray if needed
        """
        logger.info("Stopping SessionManager...")

        self.running = False

        try:
            # Stop processing task
            if self.processing_task:
                self.processing_task.cancel()
                try:
                    await self.processing_task
                except asyncio.CancelledError:
                    pass

            # Stop event processor
            if self.event_processor:
                self.event_processor.stop_processing()
                logger.info("Event processor stopped")

            # Stop Ray actors
            if self.audio_input_actor:
                await self.audio_input_actor.stop_recording.remote()

            if self.audio_output_actor:
                await self.audio_output_actor.stop_playback.remote()

            if self.dialog_client_actor:
                await self.dialog_client_actor.disconnect.remote()

            logger.info("SessionManager stopped")

        except Exception as e:
            logger.error(f"Error stopping SessionManager: {e}")
    
    def is_running(self) -> bool:
        """Check if the session manager is running."""
        return self.running

    async def _processing_loop(self) -> None:
        """
        Main processing loop that coordinates audio input, processing, and output.

        This loop:
        1. Gets audio data from input actor
        2. Sends it to dialog client actor
        3. Receives responses from dialog client
        4. Sends audio responses to output actor
        """
        logger.info("Starting processing loop")

        audio_check_count = 0
        while self.running:
            try:
                # Check for voice activity and handle interrupts
                if self.config.interrupt.enabled:
                    await self._handle_interrupt_detection()

                # Get audio data from input
                audio_data = await self.audio_input_actor.get_audio_data.remote()
                audio_check_count += 1

                if audio_data:
                    # Send audio data to dialog service (send all data, including silence)
                    sent = await self.dialog_client_actor.send_audio_data.remote(audio_data)
                    if sent:
                        logger.debug(f"Audio data sent to dialog service ({len(audio_data)} bytes)")
                    else:
                        logger.warning("Failed to send audio data to dialog service")
                else:
                    # Log periodically to show we're checking for audio
                    if audio_check_count % 100 == 0:
                        logger.debug(f"Checking for audio input... (check #{audio_check_count})")
                    # No audio data available, continue to check for responses
                    pass

                # Check for responses from dialog service
                response = await self.dialog_client_actor.receive_message.remote()

                if response:
                    # Use logging utility to clean response data
                    log_response_summary(logger, response)

                    # Process response through event system for LLM enhancement
                    if self.event_processor:
                        try:
                            await self.event_processor.process_websocket_message(response)
                        except Exception as e:
                            logger.error(f"Failed to process response through event system: {e}")

                    # Process response and extract audio if present
                    # Check for SERVER_ACK messages with audio data (like realtime_dialog)
                    if response.get('message_type') == 'SERVER_ACK' and isinstance(response.get('payload_msg'), bytes):
                        audio_response = response['payload_msg']
                        logger.info(f"SERVER_ACK: Received audio data of {len(audio_response)} bytes")

                        # 🎯 Check if we should skip this audio based on TTS filtering
                        should_skip = False

                        # Skip if tool calling filter is active
                        if (self.event_processor and
                            hasattr(self.event_processor, 'current_tts_should_skip') and
                            self.event_processor.current_tts_should_skip):
                            should_skip = True
                            logger.warning(f"🚫 DISCARDING SERVER_ACK audio chunk ({len(audio_response)} bytes) - filtered by TTS type (server auto-response)")

                        # Skip if LLM enhanced response was already sent (avoid double audio)
                        elif (self.event_processor and
                              hasattr(self.event_processor, 'llm_response_sent') and
                              self.event_processor.llm_response_sent):
                            should_skip = True
                            logger.warning(f"🚫 DISCARDING SERVER_ACK audio chunk ({len(audio_response)} bytes) - LLM enhanced response already sent")

                        if not should_skip:
                            # Mark AI as speaking and queue audio for playback
                            self.ai_is_speaking = True
                            queued = await self.audio_output_actor.queue_audio_data.remote(audio_response)
                            if queued:
                                logger.info(f"Audio response queued for playback ({len(audio_response)} bytes)")
                            else:
                                logger.warning("Failed to queue audio response for playback")

                    # Also check for audio data in event 352 (TTS audio response) for SERVER_FULL_RESPONSE
                    elif response.get('event') == 352 and 'payload' in response:
                        audio_response = response['payload']

                        # Ensure it's bytes
                        if isinstance(audio_response, bytes):
                            # 🎯 Check if we should skip this audio based on TTS filtering
                            should_skip = False
                            if (self.event_processor and
                                hasattr(self.event_processor, 'current_tts_should_skip') and
                                self.event_processor.current_tts_should_skip):
                                should_skip = True
                                logger.warning(f"🚫 DISCARDING audio chunk ({len(audio_response)} bytes) - filtered by TTS type (server auto-response)")

                            if not should_skip:
                                # Mark AI as speaking and queue audio for playback
                                self.ai_is_speaking = True
                                queued = await self.audio_output_actor.queue_audio_data.remote(audio_response)
                                if queued:
                                    logger.info(f"Audio response queued for playback ({len(audio_response)} bytes)")
                                else:
                                    logger.warning("Failed to queue audio response for playback")
                        else:
                            logger.warning(f"Audio payload is not bytes: {type(audio_response)}")

                    # Legacy support for "audio" field (if needed)
                    elif "audio" in response:
                        # Convert hex string back to bytes
                        audio_response = bytes.fromhex(response["audio"])

                        # Queue audio for playback directly (no processing)
                        queued = await self.audio_output_actor.queue_audio_data.remote(audio_response)
                        if queued:
                            logger.info(f"Audio response queued for playback ({len(audio_response)} bytes)")
                        else:
                            logger.warning("Failed to queue audio response for playback")

                # Small delay to prevent busy waiting
                await asyncio.sleep(0.01)

            except asyncio.CancelledError:
                logger.info("Processing loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in processing loop: {e}")
                # Continue processing even if there's an error
                await asyncio.sleep(0.1)

    async def _handle_interrupt_detection(self) -> None:
        """
        Handle voice activity detection and interrupt AI playback if needed.
        """
        try:
            import time

            # Check if user is speaking
            voice_active = await self.audio_input_actor.get_voice_activity.remote()

            if voice_active and self.ai_is_speaking:
                current_time = time.time()

                # Apply debounce to avoid false interrupts
                if (self.last_interrupt_time is None or
                    current_time - self.last_interrupt_time > self.config.interrupt.debounce_time_ms / 1000.0):

                    logger.info("User voice detected while AI is speaking - interrupting playback")

                    # Force stop AI playback immediately
                    interrupted = await self.audio_output_actor.force_stop_playback.remote()
                    if interrupted:
                        logger.info("AI playback successfully force stopped")
                        self.ai_is_speaking = False
                        self.last_interrupt_time = current_time

                        # Reset VAD to avoid continuous interrupts
                        await self.audio_input_actor.reset_vad.remote()
                    else:
                        logger.warning("Failed to force stop AI playback")

            # Check if AI finished speaking (queue is empty)
            elif self.ai_is_speaking:
                queue_empty = await self.audio_output_actor.is_queue_empty.remote()
                if queue_empty:
                    self.ai_is_speaking = False
                    logger.info("AI finished speaking")

        except Exception as e:
            logger.error(f"Error in interrupt detection: {e}")

        logger.debug("Processing loop stopped")


