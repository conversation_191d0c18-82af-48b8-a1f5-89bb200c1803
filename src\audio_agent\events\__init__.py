"""
Event system for AudioAgent following Volcengine's official practices.

This module implements the event-driven architecture as described in:
https://www.volcengine.com/docs/6561/1594356
"""

from .event_types import *
from .event_handler import EventHandler
from .event_processor import EventProcessor
from .llm_enhancer import LLMEnhancer

__all__ = [
    # Event types
    "EventType",
    "ServerEvent", 
    "ClientEvent",
    "DialogEvent",
    "TTSEvent",
    "ASREvent",
    "LLMEvent",
    
    # Event handling
    "EventHandler",
    "EventProcessor", 
    "LLMEnhancer",
]
