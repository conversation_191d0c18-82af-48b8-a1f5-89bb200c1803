#!/usr/bin/env python3
"""
检查 PyAudio 格式常量
"""

import pyaudio

print("PyAudio 格式常量:")
print(f"paInt16 = {pyaudio.paInt16}")
print(f"paFloat32 = {pyaudio.paFloat32}")
print(f"paInt32 = {pyaudio.paInt32}")
print(f"paInt24 = {pyaudio.paInt24}")
print(f"paInt8 = {pyaudio.paInt8}")
print(f"paUInt8 = {pyaudio.paUInt8}")

print("\n当前环境变量配置:")
print("AUDIO_AGENT_INPUT_AUDIO_BIT_SIZE=16  # 应该是", pyaudio.paInt16)
print("AUDIO_AGENT_OUTPUT_AUDIO_BIT_SIZE=1  # 应该是", pyaudio.paFloat32)
