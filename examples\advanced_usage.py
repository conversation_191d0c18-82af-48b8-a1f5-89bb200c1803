#!/usr/bin/env python3
"""
Audio Agent 高级使用示例

这个示例展示了如何自定义配置、监控状态、处理错误等高级功能。
"""

import sys
import asyncio
import logging
import time
from pathlib import Path
from typing import Optional

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.config import update_config, get_config
from audio_agent.core import SessionManager


class AdvancedAudioAgent:
    """高级音频代理类，包含监控和错误处理功能"""
    
    def __init__(self, app_id: str, access_key: str, bot_name: str = "高级助手"):
        self.app_id = app_id
        self.access_key = access_key
        self.bot_name = bot_name
        self.session_manager: Optional[SessionManager] = None
        self.start_time: Optional[float] = None
        self.message_count = 0
        
        # 配置日志
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 配置系统
        self.configure_system()
    
    def setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('audio_agent_advanced.log'),
                logging.StreamHandler()
            ]
        )
    
    def configure_system(self):
        """配置系统参数"""
        update_config(
            # WebSocket配置
            websocket={
                "app_id": self.app_id,
                "access_key": self.access_key
            },
            
            # 音频配置优化
            input_audio={
                "sample_rate": 16000,
                "chunk": 1600,  # 较小的块大小，降低延迟
                "channels": 1
            },
            output_audio={
                "sample_rate": 24000,
                "chunk": 2400,
                "channels": 1
            },
            
            # 会话配置
            session={
                "dialog": {
                    "bot_name": self.bot_name,
                    "system_role": "你是一个专业的AI助手，具有丰富的知识和经验。",
                    "speaking_style": "你的回答准确、友好、有条理。"
                }
            },
            
            # Ray配置
            ray={
                "address": "auto",       # 自动启动本地集群
                "dashboard_port": 8265,
                "num_cpus": 4           # 限制CPU使用
            }
        )
        
        self.logger.info("系统配置完成")
    
    async def start(self, max_retries: int = 3) -> bool:
        """启动音频代理，支持重试"""
        for attempt in range(max_retries):
            try:
                self.logger.info(f"启动尝试 {attempt + 1}/{max_retries}")
                
                # 创建会话管理器
                config = get_config()
                self.session_manager = SessionManager(config)
                
                # 启动会话
                await self.session_manager.start()
                
                self.start_time = time.time()
                self.logger.info("音频代理启动成功")
                return True
                
            except Exception as e:
                self.logger.error(f"启动失败 (尝试 {attempt + 1}): {e}")
                
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    self.logger.error("所有启动尝试都失败了")
                    return False
        
        return False
    
    async def stop(self):
        """停止音频代理"""
        if self.session_manager:
            try:
                await self.session_manager.stop()
                
                if self.start_time:
                    duration = time.time() - self.start_time
                    self.logger.info(f"会话结束，运行时长: {duration:.2f}秒，处理消息: {self.message_count}")
                
                self.logger.info("音频代理已停止")
            except Exception as e:
                self.logger.error(f"停止过程中出错: {e}")
    
    def is_running(self) -> bool:
        """检查是否正在运行"""
        return self.session_manager and self.session_manager.is_running()
    
    async def monitor_status(self, interval: int = 10):
        """监控系统状态"""
        while self.is_running():
            try:
                # 获取系统信息
                import psutil
                cpu_percent = psutil.cpu_percent()
                memory_percent = psutil.virtual_memory().percent
                
                # 获取Ray信息
                import ray
                if ray.is_initialized():
                    cluster_resources = ray.cluster_resources()
                    available_cpus = cluster_resources.get('CPU', 0)
                    available_memory = cluster_resources.get('memory', 0) / 1e9
                    
                    self.logger.info(
                        f"状态监控 - CPU: {cpu_percent}%, 内存: {memory_percent}%, "
                        f"Ray CPU: {available_cpus}, Ray 内存: {available_memory:.1f}GB"
                    )
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"状态监控错误: {e}")
                await asyncio.sleep(interval)
    
    async def run_with_monitoring(self, duration: int = 300):
        """运行并监控指定时间"""
        if not await self.start():
            return False
        
        try:
            # 启动监控任务
            monitor_task = asyncio.create_task(self.monitor_status())
            
            self.logger.info(f"开始运行 {duration} 秒，启用状态监控")
            print(f"🎤 高级语音对话已启动")
            print(f"📊 Ray仪表板: http://localhost:8265")
            print(f"📝 日志文件: audio_agent_advanced.log")
            print(f"⏰ 运行时长: {duration}秒")
            print("🎯 开始说话，AI将智能回复...")
            
            # 运行指定时间
            await asyncio.sleep(duration)
            
            # 取消监控任务
            monitor_task.cancel()
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("用户中断")
            return True
        except Exception as e:
            self.logger.error(f"运行过程中出错: {e}")
            return False
        finally:
            await self.stop()


async def demo_advanced_features():
    """演示高级功能"""
    print("🚀 Audio Agent 高级功能演示")
    print("=" * 50)
    
    # 配置参数（请替换为您的真实密钥）
    APP_ID = "your_app_id_here"
    ACCESS_KEY = "your_access_key_here"
    BOT_NAME = "高级AI助手"
    DURATION = 120  # 运行2分钟
    
    # 检查配置
    if APP_ID == "your_app_id_here" or ACCESS_KEY == "your_access_key_here":
        print("⚠️  请先配置API密钥:")
        print("1. 修改此文件中的 APP_ID 和 ACCESS_KEY")
        print("2. 或设置环境变量:")
        print("   export AUDIO_AGENT_WEBSOCKET__APP_ID='your_app_id'")
        print("   export AUDIO_AGENT_WEBSOCKET__ACCESS_KEY='your_access_key'")
        return
    
    # 创建高级代理
    agent = AdvancedAudioAgent(APP_ID, ACCESS_KEY, BOT_NAME)
    
    # 运行演示
    success = await agent.run_with_monitoring(DURATION)
    
    if success:
        print("✅ 高级功能演示完成")
    else:
        print("❌ 演示过程中出现问题")


async def demo_custom_configuration():
    """演示自定义配置"""
    print("\n🔧 自定义配置演示")
    print("-" * 30)
    
    # 技术专家配置
    update_config(
        session={
            "dialog": {
                "bot_name": "技术专家",
                "system_role": "你是一个资深的软件工程师，专门解答技术问题。",
                "speaking_style": "你的回答准确专业，会提供具体的解决方案和代码示例。"
            }
        },
        input_audio={
            "sample_rate": 16000,
            "chunk": 800  # 更小的块，更低延迟
        }
    )
    
    print("✅ 已配置为技术专家模式")
    print("🎯 现在可以问技术问题，如编程、架构设计等")


async def demo_error_handling():
    """演示错误处理"""
    print("\n🛡️ 错误处理演示")
    print("-" * 30)
    
    try:
        # 故意使用错误的配置来演示错误处理
        update_config(
            websocket={
                "app_id": "invalid_app_id",
                "access_key": "invalid_access_key"
            }
        )
        
        config = get_config()
        session_manager = SessionManager(config)
        
        print("尝试使用无效配置启动...")
        await session_manager.start()
        
    except Exception as e:
        print(f"✅ 成功捕获错误: {e}")
        print("💡 这演示了系统的错误处理能力")


async def main():
    """主函数"""
    print("🎯 Audio Agent 高级使用示例")
    print("=" * 60)
    
    # 演示各种功能
    await demo_advanced_features()
    await demo_custom_configuration()
    await demo_error_handling()
    
    print("\n🎉 所有演示完成！")
    print("📚 更多信息请查看项目文档")


if __name__ == "__main__":
    asyncio.run(main())
