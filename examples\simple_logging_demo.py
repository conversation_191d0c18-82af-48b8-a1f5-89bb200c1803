#!/usr/bin/env python3
"""
简单日志演示

展示如何通过环境变量控制AudioAgent的日志输出。
"""

import os
import logging

def demo_logging_levels():
    """演示不同的日志级别"""
    print("🎯 AudioAgent 日志级别演示")
    print("=" * 50)
    
    # 设置不同的日志级别
    levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
    
    for level in levels:
        print(f"\n📊 {level} 级别演示:")
        print("-" * 30)
        
        # 配置日志
        logging.basicConfig(
            level=getattr(logging, level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            force=True
        )
        
        logger = logging.getLogger("demo")
        
        # 测试各种日志
        logger.debug("这是DEBUG日志 - 调试信息")
        logger.info("这是INFO日志 - 一般信息") 
        logger.warning("这是WARNING日志 - 警告信息")
        logger.error("这是ERROR日志 - 错误信息")


def demo_environment_config():
    """演示环境变量配置"""
    print("\n🌍 环境变量配置演示")
    print("=" * 50)
    
    print("通过以下环境变量控制AudioAgent日志:")
    print()
    
    # 基本配置
    print("# 基本日志配置")
    print("export AUDIO_AGENT_LOGGING_LEVEL=INFO")
    print("export AUDIO_AGENT_LOGGING_ENABLE_FILE_LOGGING=true")
    print("export AUDIO_AGENT_LOGGING_LOG_FILE=audio_agent.log")
    print()
    
    # 调试开关
    print("# 调试开关 (减少冗余日志)")
    print("export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=false")
    print("export AUDIO_AGENT_LOGGING_WEBSOCKET_DEBUG=false")
    print("export AUDIO_AGENT_LOGGING_EVENT_DEBUG=false")
    print()
    
    # 使用示例
    print("# 使用示例")
    print("# 1. 开发调试模式")
    print("export AUDIO_AGENT_LOGGING_LEVEL=DEBUG")
    print("export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=true")
    print("uv run python scripts/run.py")
    print()
    
    print("# 2. 生产环境模式")
    print("export AUDIO_AGENT_LOGGING_LEVEL=INFO")
    print("export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=false")
    print("export AUDIO_AGENT_LOGGING_ENABLE_FILE_LOGGING=true")
    print("uv run python scripts/run.py")
    print()
    
    print("# 3. 安静模式")
    print("export AUDIO_AGENT_LOGGING_LEVEL=WARNING")
    print("uv run python scripts/run.py")


def demo_log_reduction():
    """演示日志减少的效果"""
    print("\n🔇 日志减少演示")
    print("=" * 50)
    
    print("❌ 修改前 - 冗余的音频日志:")
    print("2025-07-26 20:19:30,212 - audio_agent.core.session_manager - INFO - Audio data sent to dialog service (1024 bytes)")
    print("2025-07-26 20:19:30,243 - audio_agent.core.session_manager - INFO - Processing loop stopped")
    print("2025-07-26 20:19:30,246 - audio_agent.core.session_manager - INFO - Audio data sent to dialog service (1024 bytes)")
    print("... (重复数百次)")
    print()
    
    print("✅ 修改后 - 清爽的重要日志:")
    print("2025-07-26 20:19:30,212 - audio_agent.main - INFO - Audio Agent started")
    print("2025-07-26 20:19:30,243 - audio_agent.core.session_manager - INFO - Session started")
    print("2025-07-26 20:19:35,246 - audio_agent.events.event_processor - INFO - Processing ASR event: 你好世界")
    print("2025-07-26 20:19:36,280 - audio_agent.events.llm_enhancer - INFO - Intent recognized: greeting")
    print("2025-07-26 20:19:37,315 - audio_agent.core.session_manager - INFO - AI response generated")
    print()
    
    print("🎯 改进说明:")
    print("- 音频数据传输日志改为DEBUG级别")
    print("- 处理循环停止日志改为DEBUG级别")
    print("- 音频检测日志提高阈值，减少噪音")
    print("- 响应日志简化，避免显示大量音频数据")
    print("- 保留重要的会话和事件日志")


def demo_practical_usage():
    """演示实际使用建议"""
    print("\n💡 实际使用建议")
    print("=" * 50)
    
    print("🔧 开发阶段:")
    print("  export AUDIO_AGENT_LOGGING_LEVEL=DEBUG")
    print("  export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=true")
    print("  export AUDIO_AGENT_LOGGING_EVENT_DEBUG=true")
    print("  # 可以看到详细的调试信息")
    print()
    
    print("🏭 生产环境:")
    print("  export AUDIO_AGENT_LOGGING_LEVEL=INFO")
    print("  export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=false")
    print("  export AUDIO_AGENT_LOGGING_ENABLE_FILE_LOGGING=true")
    print("  # 只记录重要信息，启用文件日志")
    print()
    
    print("🔍 问题排查:")
    print("  export AUDIO_AGENT_LOGGING_LEVEL=DEBUG")
    print("  export AUDIO_AGENT_LOGGING_WEBSOCKET_DEBUG=true")
    print("  # 临时启用详细日志进行问题诊断")
    print()
    
    print("🎵 演示模式:")
    print("  export AUDIO_AGENT_LOGGING_LEVEL=WARNING")
    print("  # 最小化日志输出，专注于对话体验")


def main():
    """主函数"""
    print("🎤 AudioAgent 日志优化演示")
    print("=" * 60)
    print("解决冗余日志问题，提供清爽的日志体验")
    print()
    
    demo_logging_levels()
    demo_environment_config()
    demo_log_reduction()
    demo_practical_usage()
    
    print("\n🎉 日志优化完成！")
    print()
    print("📋 总结:")
    print("  ✅ 音频数据传输日志改为DEBUG级别")
    print("  ✅ 处理循环日志改为DEBUG级别") 
    print("  ✅ 音频检测阈值提高，减少噪音")
    print("  ✅ 响应日志简化，避免显示大量数据")
    print("  ✅ 新增日志配置选项，灵活控制")
    print()
    print("🚀 现在可以享受清爽的日志体验了！")


if __name__ == "__main__":
    main()
