#!/usr/bin/env python3
"""
Test script for professional VAD implementation.

This script tests the new professional VAD system with multiple models.
"""

import asyncio
import logging
import time
import numpy as np
from audio_agent.config import get_config
from audio_agent.core import SessionManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProfessionalVADTest:
    """Test class for professional VAD functionality."""
    
    def __init__(self):
        """Initialize the test."""
        self.config = get_config()
        self.session_manager = None
        
        # Configure for professional VAD testing
        self.config.interrupt.enabled = True
        self.config.interrupt.response_delay_ms = 50
        self.config.input_audio.vad_enabled = True
        
        logger.info("Professional VAD test initialized")
    
    async def test_vad_models(self):
        """Test different VAD models."""
        logger.info("=== Testing Different VAD Models ===")
        
        vad_models = ["silero", "webrtc", "simple"]
        results = {}
        
        for model in vad_models:
            logger.info(f"\n--- Testing {model.upper()} VAD ---")
            
            try:
                # Configure VAD model
                self.config.input_audio.vad_model = model
                self.config.input_audio.vad_threshold = 0.5
                self.config.input_audio.vad_window_size_ms = 30
                
                # Start session manager
                self.session_manager = SessionManager(self.config)
                await self.session_manager.start()
                
                logger.info(f"🎤 {model.upper()} VAD is now monitoring...")
                logger.info("Please speak to test voice detection (monitoring for 10 seconds)")
                
                # Monitor for 10 seconds
                detections = []
                for i in range(100):  # 10 seconds at 0.1s intervals
                    voice_active = await self.session_manager.audio_input_actor.get_voice_activity.remote()
                    vad_status = await self.session_manager.audio_input_actor.get_vad_status.remote()
                    
                    if voice_active:
                        detections.append(i/10)
                        logger.info(f"✅ {model.upper()}: Voice detected at {i/10:.1f}s (confidence: {vad_status.get('avg_confidence', 0):.2f})")
                    elif i % 20 == 0:  # Log every 2 seconds
                        logger.info(f"🔇 {model.upper()}: No voice at {i/10:.1f}s")
                    
                    await asyncio.sleep(0.1)
                
                results[model] = {
                    "detections": len(detections),
                    "detection_times": detections,
                    "model_info": vad_status.get('vad_model', model)
                }
                
                logger.info(f"✅ {model.upper()} VAD test completed: {len(detections)} detections")
                
                await self.session_manager.stop()
                self.session_manager = None
                
                # Wait between tests
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ {model.upper()} VAD test failed: {e}")
                results[model] = {"error": str(e)}
                
                if self.session_manager:
                    await self.session_manager.stop()
                    self.session_manager = None
        
        return results
    
    async def test_interrupt_with_professional_vad(self):
        """Test interrupt functionality with professional VAD."""
        logger.info("=== Testing Interrupt with Professional VAD ===")
        
        try:
            # Use Silero VAD for best results
            self.config.input_audio.vad_model = "silero"
            self.config.input_audio.vad_threshold = 0.3  # Lower threshold for better sensitivity
            self.config.input_audio.vad_min_speech_duration_ms = 200
            
            # Start session manager
            self.session_manager = SessionManager(self.config)
            await self.session_manager.start()
            
            # Generate test audio for AI speaking
            test_audio = self._generate_test_tone(8.0, 440.0)
            chunk_size = self.config.output_audio.chunk
            
            logger.info("🎵 Starting AI audio playback (8-second tone)")
            logger.info("🎤 SPEAK NOW to test professional VAD interrupt!")
            logger.info("📢 The audio should stop when professional VAD detects your voice")
            
            # Queue audio chunks
            for i in range(0, len(test_audio), chunk_size):
                chunk = test_audio[i:i + chunk_size]
                if len(chunk) > 0:
                    await self.session_manager.audio_output_actor.queue_audio_data.remote(chunk)
            
            # Mark AI as speaking
            self.session_manager.ai_is_speaking = True
            
            # Monitor for voice activity and interrupt
            start_time = time.time()
            interrupted = False
            interrupt_time = None
            
            while time.time() - start_time < 8.0:
                # Check voice activity
                voice_active = await self.session_manager.audio_input_actor.get_voice_activity.remote()
                vad_status = await self.session_manager.audio_input_actor.get_vad_status.remote()
                
                if voice_active and self.session_manager.ai_is_speaking:
                    interrupt_time = time.time() - start_time
                    logger.info(f"🎤 Professional VAD detected voice at {interrupt_time:.1f}s!")
                    logger.info(f"📊 VAD Status: {vad_status}")
                    
                    # Trigger interrupt
                    force_stopped = await self.session_manager.audio_output_actor.force_stop_playback.remote()
                    if force_stopped:
                        logger.info("🛑 Audio playback interrupted by professional VAD!")
                        self.session_manager.ai_is_speaking = False
                        interrupted = True
                        break
                    else:
                        logger.warning("⚠️ Failed to interrupt playback")
                
                await asyncio.sleep(0.05)  # Check every 50ms for fast response
            
            if interrupted:
                logger.info(f"🎉 SUCCESS: Professional VAD interrupt worked! (Response time: {interrupt_time:.1f}s)")
                
                # Verify queue is cleared
                await asyncio.sleep(0.1)
                queue_size = await self.session_manager.audio_output_actor.get_queue_size.remote()
                is_empty = await self.session_manager.audio_output_actor.is_queue_empty.remote()
                
                logger.info(f"📊 After interrupt: queue_size={queue_size}, is_empty={is_empty}")
                
                if is_empty:
                    logger.info("✅ Queue successfully cleared")
                    return True
                else:
                    logger.warning("⚠️ Queue not completely cleared")
                    return False
            else:
                logger.info("ℹ️ No voice detected during test period")
                return True
                
        except Exception as e:
            logger.error(f"Professional VAD interrupt test failed: {e}")
            return False
        finally:
            if self.session_manager:
                await self.session_manager.stop()
    
    def _generate_test_tone(self, duration_seconds: float, frequency: float = 440.0) -> bytes:
        """Generate test audio tone."""
        sample_rate = self.config.output_audio.sample_rate
        samples = int(duration_seconds * sample_rate)
        
        # Generate sine wave
        t = np.linspace(0, duration_seconds, samples, False)
        wave = np.sin(2 * np.pi * frequency * t)
        
        # Convert to int16 format with moderate volume
        audio_int16 = (wave * 16383).astype(np.int16)
        return audio_int16.tobytes()
    
    async def test_vad_performance(self):
        """Test VAD performance metrics."""
        logger.info("=== Testing VAD Performance ===")
        
        try:
            # Use Silero VAD for performance testing
            self.config.input_audio.vad_model = "silero"
            
            # Start session manager
            self.session_manager = SessionManager(self.config)
            await self.session_manager.start()
            
            logger.info("📊 Measuring VAD performance...")
            
            # Measure VAD processing time
            processing_times = []
            
            for i in range(50):  # Test 50 iterations
                start_time = time.perf_counter()
                
                # Get VAD status (this triggers VAD processing)
                vad_status = await self.session_manager.audio_input_actor.get_vad_status.remote()
                
                end_time = time.perf_counter()
                processing_time = (end_time - start_time) * 1000  # Convert to ms
                processing_times.append(processing_time)
                
                if i % 10 == 0:
                    logger.info(f"VAD call {i+1}/50: {processing_time:.2f}ms")
                
                await asyncio.sleep(0.1)
            
            # Calculate statistics
            avg_time = np.mean(processing_times)
            min_time = np.min(processing_times)
            max_time = np.max(processing_times)
            
            logger.info(f"\n📊 VAD Performance Results:")
            logger.info(f"   Average processing time: {avg_time:.2f}ms")
            logger.info(f"   Min processing time: {min_time:.2f}ms")
            logger.info(f"   Max processing time: {max_time:.2f}ms")
            logger.info(f"   VAD Model: {vad_status.get('vad_model', 'unknown')}")
            
            if avg_time < 10:  # Less than 10ms is excellent
                logger.info("🚀 Excellent VAD performance!")
                return True
            elif avg_time < 50:
                logger.info("✅ Good VAD performance")
                return True
            else:
                logger.warning("⚠️ VAD performance could be better")
                return False
                
        except Exception as e:
            logger.error(f"VAD performance test failed: {e}")
            return False
        finally:
            if self.session_manager:
                await self.session_manager.stop()
    
    async def run_all_tests(self):
        """Run all professional VAD tests."""
        logger.info("🚀 Starting Professional VAD Tests")
        
        tests = [
            ("VAD Models Comparison", self.test_vad_models),
            ("Professional VAD Interrupt", self.test_interrupt_with_professional_vad),
            ("VAD Performance", self.test_vad_performance),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*60}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*60}")
            
            try:
                result = await test_func()
                results[test_name] = result
                
                if isinstance(result, bool):
                    if result:
                        logger.info(f"✅ {test_name} PASSED")
                    else:
                        logger.error(f"❌ {test_name} FAILED")
                else:
                    logger.info(f"ℹ️ {test_name} COMPLETED")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED with exception: {e}")
                results[test_name] = False
            
            # Wait between tests
            await asyncio.sleep(2)
        
        # Print summary
        logger.info(f"\n{'='*60}")
        logger.info("PROFESSIONAL VAD TEST SUMMARY")
        logger.info(f"{'='*60}")
        
        for test_name, result in results.items():
            if isinstance(result, bool):
                status = "✅ PASSED" if result else "❌ FAILED"
                logger.info(f"{test_name}: {status}")
            elif isinstance(result, dict) and "error" not in result:
                logger.info(f"{test_name}: ℹ️ COMPLETED")
            else:
                logger.info(f"{test_name}: ❌ FAILED")
        
        logger.info(f"\n🎉 Professional VAD testing completed!")
        logger.info("💡 Recommendation: Use Silero VAD for best results")
        
        return results


async def main():
    """Main test function."""
    test = ProfessionalVADTest()
    results = await test.run_all_tests()
    
    logger.info("🎉 Professional VAD implementation is ready!")
    logger.info("🔧 Your audio agent now has industry-grade voice activity detection!")
    
    return results


if __name__ == "__main__":
    asyncio.run(main())
