#!/usr/bin/env python3
"""
测试配置
"""

from audio_agent.config import get_config

def test_config():
    """测试配置"""
    print("=== 配置测试 ===")
    
    config = get_config()
    
    print("WebSocket配置:")
    print(f"  - Base URL: {config.websocket.base_url}")
    print(f"  - App ID: {config.websocket.app_id}")
    print(f"  - Access Key: {config.websocket.access_key[:10]}...")
    print(f"  - Resource ID: {config.websocket.resource_id}")
    print(f"  - App Key: {config.websocket.app_key[:10]}...")
    print(f"  - Headers: {config.websocket.headers}")
    
    print("\n输入音频配置:")
    print(f"  - 采样率: {config.input_audio.sample_rate}Hz")
    print(f"  - 通道数: {config.input_audio.channels}")
    print(f"  - 块大小: {config.input_audio.chunk}")
    print(f"  - 位深度: {config.input_audio.bit_size}")
    
    print("\n输出音频配置:")
    print(f"  - 采样率: {config.output_audio.sample_rate}Hz")
    print(f"  - 通道数: {config.output_audio.channels}")
    print(f"  - 块大小: {config.output_audio.chunk}")
    print(f"  - 位深度: {config.output_audio.bit_size}")

if __name__ == "__main__":
    test_config()
