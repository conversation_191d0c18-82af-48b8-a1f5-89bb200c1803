"""
Tests for Ray actors.
"""

import pytest
import ray
import async<PERSON>
from unittest.mock import patch, MagicMock

from audio_agent.config import AudioAgentConfig, InputAudioConfig, OutputAudioConfig
from audio_agent.core.actors import AudioInputActor, AudioOutputActor, DialogClientActor


@pytest.fixture(scope="module")
def ray_context():
    """Initialize Ray for testing."""
    if not ray.is_initialized():
        ray.init(
            ignore_reinit_error=True,
            runtime_env={"working_dir": None}  # Disable working directory upload
        )
    yield
    # Don't shutdown Ray here as it might be used by other tests


@pytest.fixture
def input_config():
    """Create input audio configuration for testing."""
    return InputAudioConfig()


@pytest.fixture
def output_config():
    """Create output audio configuration for testing."""
    return OutputAudioConfig()


@pytest.fixture
def agent_config():
    """Create agent configuration for testing."""
    return AudioAgentConfig()


def test_audio_input_actor_creation(ray_context, input_config):
    """Test AudioInputActor creation."""
    actor = AudioInputActor.remote(input_config)
    assert actor is not None
    
    # Test that we can call methods on the actor
    is_recording = ray.get(actor.is_recording_active.remote())
    assert is_recording is False


def test_audio_output_actor_creation(ray_context, output_config):
    """Test AudioOutputActor creation."""
    actor = AudioOutputActor.remote(output_config)
    assert actor is not None
    
    # Test that we can call methods on the actor
    is_playing = ray.get(actor.is_playback_active.remote())
    assert is_playing is False


def test_dialog_client_actor_creation(ray_context, agent_config):
    """Test DialogClientActor creation."""
    actor = DialogClientActor.remote(agent_config)
    assert actor is not None
    
    # Test that we can call methods on the actor
    is_connected = ray.get(actor.is_connection_active.remote())
    assert is_connected is False


@pytest.mark.asyncio
async def test_audio_input_actor_mock_recording(ray_context, input_config):
    """Test AudioInputActor with mocked PyAudio."""
    with patch('pyaudio.PyAudio') as mock_pyaudio:
        # Mock PyAudio
        mock_audio = MagicMock()
        mock_stream = MagicMock()
        mock_pyaudio.return_value = mock_audio
        mock_audio.open.return_value = mock_stream
        mock_stream.read.return_value = b'test_audio_data'
        
        actor = AudioInputActor.remote(input_config)
        
        # Test starting recording
        started = await actor.start_recording.remote()
        assert started is True
        
        # Test stopping recording
        stopped = await actor.stop_recording.remote()
        assert stopped is True


@pytest.mark.asyncio
async def test_audio_output_actor_mock_playback(ray_context, output_config):
    """Test AudioOutputActor with mocked PyAudio."""
    with patch('pyaudio.PyAudio') as mock_pyaudio:
        # Mock PyAudio
        mock_audio = MagicMock()
        mock_stream = MagicMock()
        mock_pyaudio.return_value = mock_audio
        mock_audio.open.return_value = mock_stream
        
        actor = AudioOutputActor.remote(output_config)
        
        # Test starting playback
        started = await actor.start_playback.remote()
        assert started is True
        
        # Test queuing audio data
        queued = await actor.queue_audio_data.remote(b'test_audio_data')
        assert queued is True
        
        # Test stopping playback
        stopped = await actor.stop_playback.remote()
        assert stopped is True


def test_multiple_actors_creation(ray_context, input_config, output_config, agent_config):
    """Test creating multiple actors simultaneously."""
    input_actor = AudioInputActor.remote(input_config)
    output_actor = AudioOutputActor.remote(output_config)
    dialog_actor = DialogClientActor.remote(agent_config)
    
    # Test that all actors are created successfully
    assert input_actor is not None
    assert output_actor is not None
    assert dialog_actor is not None
    
    # Test that we can call methods on all actors
    input_status = ray.get(input_actor.is_recording_active.remote())
    output_status = ray.get(output_actor.is_playback_active.remote())
    dialog_status = ray.get(dialog_actor.is_connection_active.remote())
    
    assert input_status is False
    assert output_status is False
    assert dialog_status is False
