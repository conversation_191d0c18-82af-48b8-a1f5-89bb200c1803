"""
Test WebSocket event handling to ensure proper parsing of server events.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from datetime import datetime

from audio_agent.events import (
    EventType, ServerEvent, EventHandler, EventProcessor
)


class TestWebSocketEventParsing:
    """Test parsing of WebSocket messages into events."""
    
    def test_asr_response_event_parsing(self):
        """Test parsing ASR response event (451) from WebSocket."""
        # Simulate WebSocket message for ASR response
        websocket_message = {
            'message_type': 'SERVER_ACK',
            'event': 451,  # ASR_RESPONSE
            'session_id': 'test-session-123',
            'payload': {
                'results': [
                    {
                        'text': '你好世界',
                        'is_interim': False
                    }
                ]
            }
        }
        
        # Parse into ServerEvent
        event = ServerEvent.from_websocket_message(websocket_message, 'test-session-123')
        
        # Verify event properties
        assert event.event_type == EventType.ASR_RESPONSE
        assert event.message_type == 'SERVER_ACK'
        assert event.session_id == 'test-session-123'
        assert event.data == websocket_message
        assert event.payload['results'][0]['text'] == '你好世界'
        assert event.payload['results'][0]['is_interim'] == False
    
    def test_asr_ended_event_parsing(self):
        """Test parsing ASR ended event (459) from WebSocket."""
        websocket_message = {
            'message_type': 'SERVER_ACK',
            'event': 459,  # ASR_ENDED
            'session_id': 'test-session-123'
        }
        
        event = ServerEvent.from_websocket_message(websocket_message, 'test-session-123')
        
        assert event.event_type == EventType.ASR_ENDED
        assert event.message_type == 'SERVER_ACK'
        assert event.session_id == 'test-session-123'
    
    def test_tts_response_event_parsing(self):
        """Test parsing TTS response event (352) from WebSocket."""
        audio_data = b"fake_audio_data_here"
        websocket_message = {
            'message_type': 'SERVER_ACK',
            'event': 352,  # TTS_RESPONSE
            'session_id': 'test-session-123',
            'payload_msg': audio_data
        }
        
        event = ServerEvent.from_websocket_message(websocket_message, 'test-session-123')
        
        assert event.event_type == EventType.TTS_RESPONSE
        assert event.message_type == 'SERVER_ACK'
        assert event.payload == audio_data
    
    def test_chat_response_event_parsing(self):
        """Test parsing Chat response event (550) from WebSocket."""
        websocket_message = {
            'message_type': 'SERVER_ACK',
            'event': 550,  # CHAT_RESPONSE
            'session_id': 'test-session-123',
            'payload': {
                'content': '你好！我是AI助手，很高兴为您服务。'
            }
        }
        
        event = ServerEvent.from_websocket_message(websocket_message, 'test-session-123')
        
        assert event.event_type == EventType.CHAT_RESPONSE
        assert event.message_type == 'SERVER_ACK'
        assert event.payload['content'] == '你好！我是AI助手，很高兴为您服务。'
    
    def test_session_started_event_parsing(self):
        """Test parsing Session started event (150) from WebSocket."""
        websocket_message = {
            'message_type': 'SERVER_ACK',
            'event': 150,  # SESSION_STARTED
            'session_id': 'test-session-123',
            'payload': {
                'dialog_id': 'dialog-456'
            }
        }
        
        event = ServerEvent.from_websocket_message(websocket_message, 'test-session-123')
        
        assert event.event_type == EventType.SESSION_STARTED
        assert event.payload['dialog_id'] == 'dialog-456'
    
    def test_connection_started_event_parsing(self):
        """Test parsing Connection started event (50) from WebSocket."""
        websocket_message = {
            'message_type': 'SERVER_ACK',
            'event': 50,  # CONNECTION_STARTED
            'session_id': 'test-session-123'
        }
        
        event = ServerEvent.from_websocket_message(websocket_message, 'test-session-123')
        
        assert event.event_type == EventType.CONNECTION_STARTED
        assert event.message_type == 'SERVER_ACK'
    
    def test_unknown_event_fallback(self):
        """Test handling of unknown event IDs."""
        websocket_message = {
            'message_type': 'SERVER_ACK',
            'event': 999,  # Unknown event ID
            'session_id': 'test-session-123'
        }
        
        event = ServerEvent.from_websocket_message(websocket_message, 'test-session-123')
        
        # Should fallback to CONNECTION_STARTED for SERVER_ACK without payload
        assert event.event_type == EventType.CONNECTION_STARTED
        assert event.message_type == 'SERVER_ACK'


class TestWebSocketEventFlow:
    """Test complete WebSocket event processing flow."""
    
    @pytest.fixture
    def event_processor(self):
        """Create event processor for testing."""
        from audio_agent.config import get_config
        config = get_config()
        return EventProcessor('test-session-123', config)
    
    @pytest.mark.asyncio
    async def test_asr_to_llm_flow(self, event_processor):
        """Test ASR → LLM enhancement flow."""
        # Mock LLM enhancer
        event_processor.llm_enhancer.recognize_intent = AsyncMock(return_value={
            'intent': 'weather_query',
            'confidence': 0.9,
            'requires_tools': True,
            'tools': ['get_weather']
        })
        event_processor.llm_enhancer.execute_tools = AsyncMock(return_value=[
            {'weather': '18°C, Cloudy'}
        ])
        event_processor.llm_enhancer.generate_response = AsyncMock(return_value=
            "今天东京的天气是18°C，多云。"
        )

        # Start event processing in background
        processing_task = asyncio.create_task(event_processor.start_processing())

        try:
            # Give time for processing to start
            await asyncio.sleep(0.05)

            # Simulate ASR response message
            asr_message = {
                'message_type': 'SERVER_ACK',
                'event': 451,  # ASR_RESPONSE
                'session_id': 'test-session-123',
                'payload': {
                    'results': [
                        {
                            'text': '东京今天天气怎么样？',
                            'is_interim': False
                        }
                    ]
                }
            }

            # Process ASR response
            await event_processor.process_websocket_message(asr_message)

            # Simulate ASR ended message
            asr_ended_message = {
                'message_type': 'SERVER_ACK',
                'event': 459,  # ASR_ENDED
                'session_id': 'test-session-123'
            }

            # Process ASR ended - this should trigger LLM enhancement
            await event_processor.process_websocket_message(asr_ended_message)

            # Give time for async processing
            await asyncio.sleep(0.2)

            # Verify LLM enhancer was called
            event_processor.llm_enhancer.recognize_intent.assert_called_once()
            event_processor.llm_enhancer.execute_tools.assert_called_once()
            event_processor.llm_enhancer.generate_response.assert_called_once()

        finally:
            # Stop processing
            event_processor.stop_processing()
            processing_task.cancel()
            try:
                await processing_task
            except asyncio.CancelledError:
                pass
    
    @pytest.mark.asyncio
    async def test_tts_audio_handling(self, event_processor):
        """Test TTS audio event handling."""
        # Mock audio handler
        audio_handler = AsyncMock()
        event_processor.event_handler.register_handler(
            EventType.TTS_RESPONSE,
            audio_handler
        )

        # Start event processing in background
        processing_task = asyncio.create_task(event_processor.start_processing())

        try:
            # Give time for processing to start
            await asyncio.sleep(0.05)

            # Simulate TTS response with audio data
            tts_message = {
                'message_type': 'SERVER_ACK',
                'event': 352,  # TTS_RESPONSE
                'session_id': 'test-session-123',
                'payload_msg': b"audio_data_here"
            }

            # Process TTS message
            await event_processor.process_websocket_message(tts_message)

            # Give time for processing
            await asyncio.sleep(0.1)

            # Verify audio handler was called
            audio_handler.assert_called_once()

        finally:
            # Stop processing
            event_processor.stop_processing()
            processing_task.cancel()
            try:
                await processing_task
            except asyncio.CancelledError:
                pass
    
    @pytest.mark.asyncio
    async def test_chat_response_handling(self, event_processor):
        """Test Chat response event handling."""
        # Mock chat handler
        chat_handler = AsyncMock()
        event_processor.event_handler.register_handler(
            EventType.CHAT_RESPONSE,
            chat_handler
        )

        # Start event processing in background
        processing_task = asyncio.create_task(event_processor.start_processing())

        try:
            # Give time for processing to start
            await asyncio.sleep(0.05)

            # Simulate Chat response
            chat_message = {
                'message_type': 'SERVER_ACK',
                'event': 550,  # CHAT_RESPONSE
                'session_id': 'test-session-123',
                'payload': {
                    'content': '我理解您的问题，让我为您查询天气信息。'
                }
            }

            # Process Chat message
            await event_processor.process_websocket_message(chat_message)

            # Give time for processing
            await asyncio.sleep(0.1)

            # Verify chat handler was called
            chat_handler.assert_called_once()

        finally:
            # Stop processing
            event_processor.stop_processing()
            processing_task.cancel()
            try:
                await processing_task
            except asyncio.CancelledError:
                pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
