"""
Intelligent Agent Actor for enhanced conversation processing.

This module provides an intelligent agent that can process user messages,
decide when to use tools, execute tool calls, and generate enhanced responses.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Tuple
import ray
from ..config.settings import AudioAgentConfig
from ..tools import ToolExecutor, get_tool_registry
from ..tools.builtin import register_builtin_tools
from .intent_recognizer import IntentRecognizer

logger = logging.getLogger(__name__)


@ray.remote
class IntelligentAgentActor:
    """
    Ray actor for intelligent conversation processing.
    
    This actor enhances the basic dialog functionality with:
    - Tool calling capabilities
    - Intent recognition
    - Knowledge retrieval
    - Enhanced response generation
    """
    
    def __init__(self, config: AudioAgentConfig):
        """
        Initialize the intelligent agent actor.
        
        Args:
            config: Audio agent configuration
        """
        self.config = config
        self.tool_executor = ToolExecutor()
        self.intent_recognizer = IntentRecognizer()
        self.conversation_history: List[Dict[str, Any]] = []
        
        # Register built-in tools
        register_builtin_tools()
        
        logger.info("IntelligentAgentActor initialized")
    
    async def process_user_message(self, message: str, session_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message and generate an enhanced response.
        
        Args:
            message: User's message text
            session_context: Optional session context information
            
        Returns:
            Dictionary containing response and metadata
        """
        try:
            logger.info(f"Processing user message: {message}")
            
            # Add to conversation history
            self.conversation_history.append({
                "role": "user",
                "content": message,
                "timestamp": asyncio.get_event_loop().time()
            })
            
            # Recognize intent and determine if tools are needed
            intent_result = await self.intent_recognizer.analyze_intent(message)
            
            response_data = {
                "original_message": message,
                "intent": intent_result,
                "tools_used": [],
                "response": "",
                "metadata": {}
            }
            
            # Check if tool calling is needed
            if intent_result.get("needs_tools", False):
                # Generate tool calls
                tool_calls = await self._generate_tool_calls(message, intent_result)
                
                if tool_calls:
                    # Execute tools
                    tool_results = await self.tool_executor.execute_tool_calls(tool_calls)
                    response_data["tools_used"] = [
                        {
                            "tool_name": call.get("function", {}).get("name", "unknown"),
                            "success": result.success,
                            "result": result.data if result.success else result.error
                        }
                        for call, result in zip(tool_calls, tool_results)
                    ]
                    
                    # Generate response with tool results
                    response_data["response"] = await self._generate_response_with_tools(
                        message, tool_results, intent_result
                    )
                else:
                    # No tools needed, generate normal response
                    response_data["response"] = await self._generate_normal_response(message, intent_result)
            else:
                # Generate normal response
                response_data["response"] = await self._generate_normal_response(message, intent_result)
            
            # Add response to conversation history
            self.conversation_history.append({
                "role": "assistant",
                "content": response_data["response"],
                "timestamp": asyncio.get_event_loop().time(),
                "tools_used": response_data["tools_used"]
            })
            
            logger.info("Message processed successfully")
            return response_data
            
        except Exception as e:
            logger.error(f"Error processing user message: {e}")
            return {
                "original_message": message,
                "intent": {},
                "tools_used": [],
                "response": "抱歉，我在处理您的消息时遇到了问题。请稍后再试。",
                "metadata": {"error": str(e)}
            }
    
    async def _generate_tool_calls(self, message: str, intent_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate tool calls based on user message and intent.
        
        Args:
            message: User message
            intent_result: Intent analysis result
            
        Returns:
            List of tool calls in Volcengine format
        """
        tool_calls = []
        
        # Simple rule-based tool calling for now
        # In a full implementation, you'd use an LLM to generate these
        
        intent_type = intent_result.get("type", "")
        
        if intent_type == "search" or "搜索" in message or "查找" in message:
            # Extract search query
            query = self._extract_search_query(message)
            if query:
                tool_calls.append({
                    "type": "function",
                    "function": {
                        "name": "web_search",
                        "arguments": json.dumps({"query": query, "max_results": 3})
                    }
                })
        
        elif intent_type == "calculation" or any(op in message for op in ["+", "-", "*", "/", "计算", "算"]):
            # Extract mathematical expression
            expression = self._extract_math_expression(message)
            if expression:
                tool_calls.append({
                    "type": "function",
                    "function": {
                        "name": "calculator",
                        "arguments": json.dumps({"expression": expression})
                    }
                })
        
        elif intent_type == "time" or any(word in message for word in ["时间", "日期", "现在", "今天"]):
            # Determine time operation
            if "时间" in message:
                operation = "current_time"
            elif "日期" in message or "今天" in message:
                operation = "current_date"
            else:
                operation = "current_datetime"
            
            tool_calls.append({
                "type": "function",
                "function": {
                    "name": "datetime",
                    "arguments": json.dumps({"operation": operation})
                }
            })
        
        return tool_calls
    
    def _extract_search_query(self, message: str) -> str:
        """Extract search query from user message."""
        # Simple extraction - remove common prefixes
        query = message
        for prefix in ["搜索", "查找", "找", "search", "find"]:
            if query.startswith(prefix):
                query = query[len(prefix):].strip()
        return query
    
    def _extract_math_expression(self, message: str) -> str:
        """Extract mathematical expression from user message."""
        # Simple extraction - look for mathematical patterns
        import re
        
        # Look for mathematical expressions
        math_pattern = r'[\d+\-*/().\s]+'
        matches = re.findall(math_pattern, message)
        
        if matches:
            # Return the longest match
            return max(matches, key=len).strip()
        
        return message
    
    async def _generate_response_with_tools(
        self, 
        message: str, 
        tool_results: List, 
        intent_result: Dict[str, Any]
    ) -> str:
        """
        Generate response incorporating tool results.
        
        Args:
            message: Original user message
            tool_results: Results from tool execution
            intent_result: Intent analysis result
            
        Returns:
            Generated response text
        """
        # Simple response generation based on tool results
        # In a full implementation, you'd use an LLM for this
        
        response_parts = []
        
        for result in tool_results:
            if result.success:
                data = result.data
                
                # Handle different tool types
                if "query" in data:  # Search result
                    response_parts.append(f"我为您搜索了相关信息：")
                    if data.get("results"):
                        for i, item in enumerate(data["results"][:2], 1):
                            response_parts.append(f"{i}. {item.get('snippet', item.get('title', ''))}")
                    else:
                        response_parts.append("抱歉，没有找到相关信息。")
                
                elif "expression" in data:  # Calculator result
                    response_parts.append(f"计算结果：{data['expression']} = {data['formatted_result']}")
                
                elif "operation" in data:  # DateTime result
                    if data["operation"] == "current_time":
                        response_parts.append(f"现在的时间是：{data['time']}")
                    elif data["operation"] == "current_date":
                        response_parts.append(f"今天是：{data['date']} ({data['weekday']})")
                    elif data["operation"] == "current_datetime":
                        response_parts.append(f"现在是：{data['datetime']}")
            else:
                response_parts.append(f"抱歉，执行操作时出现错误：{result.error}")
        
        if not response_parts:
            return "我已经处理了您的请求，但没有获得具体结果。"
        
        return "\n".join(response_parts)
    
    async def _generate_normal_response(self, message: str, intent_result: Dict[str, Any]) -> str:
        """
        Generate normal response without tools.
        
        Args:
            message: User message
            intent_result: Intent analysis result
            
        Returns:
            Generated response text
        """
        # Simple response generation
        # In a full implementation, you'd use an LLM for this
        
        intent_type = intent_result.get("type", "general")
        
        if intent_type == "greeting":
            return "您好！我是您的AI助手，有什么可以帮助您的吗？"
        elif intent_type == "farewell":
            return "再见！如果还有其他问题，随时可以问我。"
        else:
            return "我理解了您的问题。让我为您提供帮助。"
    
    async def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get the conversation history."""
        return self.conversation_history.copy()
    
    async def clear_conversation_history(self) -> None:
        """Clear the conversation history."""
        self.conversation_history.clear()
        logger.info("Conversation history cleared")
    
    async def get_available_tools(self) -> List[str]:
        """Get list of available tools."""
        return self.tool_executor.get_available_tools()
    
    async def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """Get tool schemas for available tools."""
        return self.tool_executor.get_tool_schemas()
