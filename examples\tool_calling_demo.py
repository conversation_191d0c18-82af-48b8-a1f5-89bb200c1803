"""
Tool calling demonstration for AudioAgent.

This example shows how to use the enhanced AudioAgent with tool calling
and knowledge enhancement capabilities.
"""

import asyncio
import logging
from audio_agent.config import get_config, update_config
from audio_agent.tools.builtin import register_builtin_tools
from audio_agent.tools import get_tool_registry, ToolExecutor
from audio_agent.agent import IntelligentAgentActor
from audio_agent.knowledge import SimpleKnowledgeBase, KnowledgeItem
import ray

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def demo_tool_calling():
    """Demonstrate tool calling functionality."""
    print("\n🔧 工具调用演示")
    print("-" * 40)
    
    # Initialize Ray with local cluster
    if not ray.is_initialized():
        ray.init(address="auto", ignore_reinit_error=True)
    
    # Register built-in tools
    register_builtin_tools()
    
    # Get tool registry and executor
    registry = get_tool_registry()
    executor = ToolExecutor(registry)
    
    print(f"✅ 已注册 {len(registry)} 个工具:")
    for tool_name in registry.list_tools():
        print(f"  - {tool_name}")
    
    # Test calculator tool
    print("\n📊 测试计算器工具:")
    calc_result = await executor.execute_tool_call("calculator", {"expression": "2 + 3 * 4"})
    if calc_result.success:
        print(f"  计算结果: {calc_result.data['formatted_result']}")
    else:
        print(f"  计算失败: {calc_result.error}")
    
    # Test datetime tool
    print("\n⏰ 测试时间工具:")
    time_result = await executor.execute_tool_call("datetime", {"operation": "current_datetime"})
    if time_result.success:
        print(f"  当前时间: {time_result.data['datetime']}")
    else:
        print(f"  时间查询失败: {time_result.error}")
    
    # Test web search tool
    print("\n🔍 测试搜索工具:")
    search_result = await executor.execute_tool_call("web_search", {
        "query": "Python programming",
        "max_results": 2
    })
    if search_result.success:
        print(f"  搜索到 {search_result.data['count']} 个结果:")
        for i, result in enumerate(search_result.data['results'], 1):
            print(f"    {i}. {result.get('title', 'No title')}")
    else:
        print(f"  搜索失败: {search_result.error}")


async def demo_knowledge_base():
    """Demonstrate knowledge base functionality."""
    print("\n📚 知识库演示")
    print("-" * 40)
    
    # Create knowledge base
    kb = SimpleKnowledgeBase("demo_kb", "./knowledge/demo.json")
    
    # Add some knowledge items
    items = [
        KnowledgeItem(
            id="python_basics",
            title="Python基础知识",
            content="Python是一种高级编程语言，具有简洁的语法和强大的功能。它广泛用于Web开发、数据科学、人工智能等领域。",
            category="编程",
            tags=["Python", "编程语言", "基础"]
        ),
        KnowledgeItem(
            id="ai_overview",
            title="人工智能概述",
            content="人工智能(AI)是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。包括机器学习、深度学习、自然语言处理等技术。",
            category="人工智能",
            tags=["AI", "机器学习", "深度学习"]
        ),
        KnowledgeItem(
            id="web_dev",
            title="Web开发技术",
            content="Web开发涉及创建网站和Web应用程序。主要技术包括HTML、CSS、JavaScript、以及各种后端框架如Django、Flask等。",
            category="Web开发",
            tags=["HTML", "CSS", "JavaScript", "Web"]
        )
    ]
    
    # Add items to knowledge base
    for item in items:
        success = await kb.add_item(item)
        if success:
            print(f"✅ 添加知识项: {item.title}")
        else:
            print(f"❌ 添加失败: {item.title}")
    
    # Search knowledge base
    print("\n🔍 搜索知识库:")
    search_queries = ["Python", "人工智能", "Web"]
    
    for query in search_queries:
        results = await kb.search(query, max_results=2)
        print(f"\n  搜索 '{query}':")
        if results:
            for result in results:
                print(f"    - {result.title}: {result.content[:50]}...")
        else:
            print("    没有找到相关结果")
    
    # Show statistics
    stats = kb.get_stats()
    print(f"\n📊 知识库统计:")
    print(f"  总项目数: {stats['total_items']}")
    print(f"  分类数: {stats['categories']}")
    for category, count in stats['category_breakdown'].items():
        print(f"    {category}: {count} 项")


async def demo_intelligent_agent():
    """Demonstrate intelligent agent functionality."""
    print("\n🤖 智能代理演示")
    print("-" * 40)
    
    # Configure the system
    update_config(
        intelligent_agent={
            "enabled": True,
            "tool_calling_enabled": True,
            "knowledge_retrieval_enabled": True
        },
        tools={
            "enabled_tools": ["web_search", "calculator", "datetime"],
            "web_search_enabled": True,
            "calculator_enabled": True,
            "datetime_enabled": True
        }
    )
    
    config = get_config()
    
    # Create intelligent agent actor
    agent_actor = IntelligentAgentActor.remote(config)
    
    # Test messages
    test_messages = [
        "现在几点了？",
        "计算 15 * 8 + 7",
        "搜索关于机器学习的信息",
        "你好，我是新用户"
    ]
    
    for message in test_messages:
        print(f"\n👤 用户: {message}")
        
        # Process message
        response = await agent_actor.process_user_message.remote(message)
        
        print(f"🤖 AI: {response['response']}")
        
        if response['tools_used']:
            print("🔧 使用的工具:")
            for tool in response['tools_used']:
                print(f"  - {tool['tool_name']}: {'成功' if tool['success'] else '失败'}")


async def main():
    """Main demonstration function."""
    print("🎯 AudioAgent 工具调用和知识增强演示")
    print("=" * 50)
    
    try:
        # Demo 1: Tool calling
        await demo_tool_calling()
        
        # Demo 2: Knowledge base
        await demo_knowledge_base()
        
        # Demo 3: Intelligent agent
        await demo_intelligent_agent()
        
        print("\n✅ 演示完成！")
        print("\n💡 提示:")
        print("  - 工具调用功能已集成到AudioAgent中")
        print("  - 知识库可以存储和检索相关信息")
        print("  - 智能代理能够自动决定何时使用工具")
        print("  - 您可以通过配置启用/禁用特定功能")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
        print(f"❌ 演示失败: {e}")
    
    finally:
        # Cleanup
        if ray.is_initialized():
            ray.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
