# =============================================================================
# Audio Agent 完整环境变量配置文件
# =============================================================================
# -----------------------------------------------------------------------------
# WebSocket API 配置 (必需)
# -----------------------------------------------------------------------------
AUDIO_AGENT_WEBSOCKET_BASE_URL=wss://openspeech.bytedance.com/api/v3/realtime/dialogue
AUDIO_AGENT_WEBSOCKET_APP_ID=
AUDIO_AGENT_WEBSOCKET_ACCESS_KEY=
AUDIO_AGENT_WEBSOCKET_RESOURCE_ID=volc.speech.dialog
AUDIO_AGENT_WEBSOCKET_APP_KEY=PlgvMymc7f3tQnJ6
AUDIO_AGENT_WEBSOCKET_CONNECT_ID=

# -----------------------------------------------------------------------------
# 输入音频配置（已自动检测并优化）
# -----------------------------------------------------------------------------
AUDIO_AGENT_INPUT_AUDIO_CHUNK=512
AUDIO_AGENT_INPUT_AUDIO_FORMAT=pcm
AUDIO_AGENT_INPUT_AUDIO_CHANNELS=1
# 16kHz最适合VAD和语音识别，已验证您的INZONE H5设备支持
AUDIO_AGENT_INPUT_AUDIO_SAMPLE_RATE=16000
AUDIO_AGENT_INPUT_AUDIO_BIT_SIZE=8

# -----------------------------------------------------------------------------
# 输出音频配置（已自动检测并优化）
# -----------------------------------------------------------------------------
AUDIO_AGENT_OUTPUT_AUDIO_CHUNK=512
AUDIO_AGENT_OUTPUT_AUDIO_FORMAT=pcm
AUDIO_AGENT_OUTPUT_AUDIO_CHANNELS=1
# 24kHz是OpenAI推荐的输出采样率，已验证您的设备支持
AUDIO_AGENT_OUTPUT_AUDIO_SAMPLE_RATE=24000
AUDIO_AGENT_OUTPUT_AUDIO_BIT_SIZE=1

# -----------------------------------------------------------------------------
# VAD 语音活动检测配置（专业级中断功能）
# -----------------------------------------------------------------------------
# 启用专业级VAD功能
AUDIO_AGENT_INPUT_AUDIO_VAD_ENABLED=true
# VAD模型选择：silero=专业级 | webrtc=轻量级 | simple=基础
AUDIO_AGENT_INPUT_AUDIO_VAD_MODEL=silero
# VAD敏感度：0.3=敏感 | 0.5=平衡 | 0.7=保守
AUDIO_AGENT_INPUT_AUDIO_VAD_THRESHOLD=0.5
# VAD检测窗口大小（毫秒）
AUDIO_AGENT_INPUT_AUDIO_VAD_WINDOW_SIZE_MS=30
# 触发中断的最小语音持续时间（毫秒）
AUDIO_AGENT_INPUT_AUDIO_VAD_MIN_SPEECH_DURATION_MS=200

# -----------------------------------------------------------------------------
# 音频播放中断配置
# -----------------------------------------------------------------------------
# 启用音频播放中断功能
AUDIO_AGENT_INTERRUPT_ENABLED=true
# 中断响应延迟（毫秒）- 系统响应中断信号的速度
AUDIO_AGENT_INTERRUPT_RESPONSE_DELAY_MS=25
# 触发中断的最小语音时长（毫秒）
AUDIO_AGENT_INTERRUPT_MIN_SPEECH_DURATION_MS=200
# 防抖时间（毫秒）- 防止连续快速中断
AUDIO_AGENT_INTERRUPT_DEBOUNCE_TIME_MS=100

# -----------------------------------------------------------------------------
# 会话配置 - 对话设置
# -----------------------------------------------------------------------------
AUDIO_AGENT_SESSION_DIALOG_BOT_NAME=小凯
AUDIO_AGENT_SESSION_DIALOG_SYSTEM_ROLE=你使用活泼灵动的女声，性格开朗，热爱生活。
AUDIO_AGENT_SESSION_DIALOG_SPEAKING_STYLE=你的说话风格简洁明了，语速适中，语调自然。
AUDIO_AGENT_SESSION_DIALOG_STRICT_AUDIT=false
AUDIO_AGENT_SESSION_DIALOG_AUDIT_RESPONSE=支持客户自定义安全审核回复话术。
AUDIO_AGENT_SESSION_DIALOG_GREETING_MESSAGE=你好，我是小凯，有什么可以帮助您的？

# -----------------------------------------------------------------------------
# Ray 分布式计算配置
# -----------------------------------------------------------------------------
AUDIO_AGENT_RAY_ADDRESS=auto
# AUDIO_AGENT_RAY_NUM_CPUS=
# AUDIO_AGENT_RAY_NUM_GPUS=
# AUDIO_AGENT_RAY_OBJECT_STORE_MEMORY=
AUDIO_AGENT_RAY_DASHBOARD_HOST=127.0.0.1
AUDIO_AGENT_RAY_DASHBOARD_PORT=8265

# -----------------------------------------------------------------------------
# 智能代理配置 - AI增强功能
# -----------------------------------------------------------------------------
# 启用智能代理功能
AUDIO_AGENT_INTELLIGENT_ENABLED=true
# 启用工具调用功能
AUDIO_AGENT_INTELLIGENT_TOOL_CALLING_ENABLED=true
# 启用知识检索功能
AUDIO_AGENT_INTELLIGENT_KNOWLEDGE_RETRIEVAL_ENABLED=true
# 工具执行超时时间（秒）
AUDIO_AGENT_INTELLIGENT_TOOL_EXECUTION_TIMEOUT=30.0
# 最大对话历史记录数
AUDIO_AGENT_INTELLIGENT_MAX_CONVERSATION_HISTORY=50

# -----------------------------------------------------------------------------
# 工具系统配置
# -----------------------------------------------------------------------------
# 启用的工具列表（逗号分隔）
AUDIO_AGENT_TOOLS_ENABLED_TOOLS=web_search,calculator,datetime
# 启用网络搜索工具
AUDIO_AGENT_TOOLS_WEB_SEARCH_ENABLED=true
# 启用计算器工具
AUDIO_AGENT_TOOLS_CALCULATOR_ENABLED=true
# 启用时间日期工具
AUDIO_AGENT_TOOLS_DATETIME_ENABLED=true
# 搜索结果最大数量
AUDIO_AGENT_TOOLS_SEARCH_MAX_RESULTS=5
# 搜索请求超时时间（秒）
AUDIO_AGENT_TOOLS_SEARCH_TIMEOUT=10.0

# -----------------------------------------------------------------------------
# 知识库配置
# -----------------------------------------------------------------------------
# 启用知识库功能
AUDIO_AGENT_KNOWLEDGE_ENABLED=true
# 知识库文件存储路径
AUDIO_AGENT_KNOWLEDGE_KNOWLEDGE_BASE_PATH=./knowledge
# 知识检索最大结果数
AUDIO_AGENT_KNOWLEDGE_MAX_RETRIEVAL_RESULTS=5
# 相似度阈值（0.0-1.0）
AUDIO_AGENT_KNOWLEDGE_SIMILARITY_THRESHOLD=0.7

# -----------------------------------------------------------------------------
# 使用说明
# -----------------------------------------------------------------------------
# 1. 修改 your_app_id_here 和 your_access_key_here 为真实值
# 2. 空值表示使用代码中的默认值
# 3. 布尔值使用 true/false
# 4. 数字值直接填写，不需要引号
# 5. 字符串值可以不加引号（除非包含特殊字符）
# 6. 工具列表使用逗号分隔，不要有空格
# 7. 路径使用正斜杠或双反斜杠（Windows）

# -----------------------------------------------------------------------------
# 新功能说明
# -----------------------------------------------------------------------------
# 🤖 智能代理功能：
#    - 自动判断用户意图
#    - 智能选择合适的工具
#    - 维护对话上下文
#
# 🔧 工具调用功能：
#    - web_search: 网络搜索工具
#    - calculator: 数学计算工具
#    - datetime: 时间日期工具
#
# 📚 知识库功能：
#    - 存储和检索知识内容
#    - 支持分类和标签
#    - 智能语义搜索
