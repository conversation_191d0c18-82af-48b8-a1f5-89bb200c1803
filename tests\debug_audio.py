#!/usr/bin/env python3
"""
音频设备调试脚本
用于检测和测试音频输入设备
"""

import pyaudio
import numpy as np
import time
import threading
import queue

def list_audio_devices():
    """列出所有音频设备"""
    print("=== 音频设备列表 ===")
    audio = pyaudio.PyAudio()
    
    print(f"默认输入设备: {audio.get_default_input_device_info()}")
    print(f"默认输出设备: {audio.get_default_output_device_info()}")
    print()
    
    for i in range(audio.get_device_count()):
        info = audio.get_device_info_by_index(i)
        print(f"设备 {i}: {info['name']}")
        print(f"  - 最大输入通道: {info['maxInputChannels']}")
        print(f"  - 最大输出通道: {info['maxOutputChannels']}")
        print(f"  - 默认采样率: {info['defaultSampleRate']}")
        print()
    
    audio.terminate()

def test_microphone(device_index=None, duration=5):
    """测试麦克风录音"""
    print(f"=== 测试麦克风录音 (设备 {device_index if device_index is not None else '默认'}) ===")
    
    # 音频配置
    chunk = 3200
    format = pyaudio.paInt16
    channels = 1
    rate = 16000
    
    audio = pyaudio.PyAudio()
    
    try:
        # 打开音频流
        stream = audio.open(
            format=format,
            channels=channels,
            rate=rate,
            input=True,
            input_device_index=device_index,
            frames_per_buffer=chunk
        )
        
        print(f"开始录音 {duration} 秒，请说话...")
        
        audio_data = []
        max_amplitudes = []
        
        for i in range(int(rate / chunk * duration)):
            try:
                data = stream.read(chunk, exception_on_overflow=False)
                audio_data.append(data)
                
                # 计算音频幅度
                audio_int16 = np.frombuffer(data, dtype=np.int16)
                max_amplitude = np.abs(audio_int16).max()
                avg_amplitude = np.abs(audio_int16).mean()
                max_amplitudes.append(max_amplitude)
                
                # 实时显示音频级别
                if i % 10 == 0:  # 每秒显示几次
                    level_bar = "█" * min(int(max_amplitude / 1000), 50)
                    print(f"\r音频级别: {max_amplitude:5d} |{level_bar:<50}|", end="", flush=True)
                
            except Exception as e:
                print(f"\n读取音频数据出错: {e}")
                break
        
        print("\n录音完成!")
        
        # 分析录音结果
        if max_amplitudes:
            overall_max = max(max_amplitudes)
            overall_avg = np.mean(max_amplitudes)
            
            print(f"最大音频幅度: {overall_max}")
            print(f"平均音频幅度: {overall_avg:.2f}")
            
            if overall_max < 10:
                print("⚠️  音频幅度很低，可能麦克风没有工作或音量太小")
            elif overall_max < 100:
                print("⚠️  音频幅度较低，建议检查麦克风音量")
            else:
                print("✅ 音频幅度正常")
        
        stream.stop_stream()
        stream.close()
        
    except Exception as e:
        print(f"打开音频流失败: {e}")
    finally:
        audio.terminate()

def test_real_time_audio(device_index=None):
    """实时音频监控"""
    print(f"=== 实时音频监控 (设备 {device_index if device_index is not None else '默认'}) ===")
    print("按 Ctrl+C 停止监控")
    
    # 音频配置
    chunk = 1024
    format = pyaudio.paInt16
    channels = 1
    rate = 16000
    
    audio = pyaudio.PyAudio()
    
    try:
        stream = audio.open(
            format=format,
            channels=channels,
            rate=rate,
            input=True,
            input_device_index=device_index,
            frames_per_buffer=chunk
        )
        
        print("开始监控，请说话...")
        
        while True:
            try:
                data = stream.read(chunk, exception_on_overflow=False)
                
                # 计算音频幅度
                audio_int16 = np.frombuffer(data, dtype=np.int16)
                max_amplitude = np.abs(audio_int16).max()
                avg_amplitude = np.abs(audio_int16).mean()
                
                # 显示音频级别
                level_bar = "█" * min(int(max_amplitude / 500), 60)
                print(f"\r最大: {max_amplitude:5d} 平均: {avg_amplitude:6.1f} |{level_bar:<60}|", end="", flush=True)
                
                time.sleep(0.05)
                
            except KeyboardInterrupt:
                print("\n停止监控")
                break
            except Exception as e:
                print(f"\n读取音频数据出错: {e}")
                break
        
        stream.stop_stream()
        stream.close()
        
    except Exception as e:
        print(f"打开音频流失败: {e}")
    finally:
        audio.terminate()

def main():
    """主函数"""
    print("音频设备调试工具")
    print("=" * 50)
    
    while True:
        print("\n选择操作:")
        print("1. 列出所有音频设备")
        print("2. 测试默认麦克风")
        print("3. 测试指定设备麦克风")
        print("4. 实时音频监控")
        print("5. 退出")
        
        choice = input("\n请选择 (1-5): ").strip()
        
        if choice == "1":
            list_audio_devices()
        elif choice == "2":
            test_microphone()
        elif choice == "3":
            device_index = input("请输入设备索引: ").strip()
            try:
                device_index = int(device_index)
                test_microphone(device_index)
            except ValueError:
                print("无效的设备索引")
        elif choice == "4":
            device_choice = input("设备索引 (回车使用默认): ").strip()
            device_index = None
            if device_choice:
                try:
                    device_index = int(device_choice)
                except ValueError:
                    print("无效的设备索引，使用默认设备")
            test_real_time_audio(device_index)
        elif choice == "5":
            print("退出")
            break
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
