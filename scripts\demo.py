#!/usr/bin/env python3
"""
Audio Agent 演示脚本

这个脚本展示了如何完整使用Audio Agent进行语音对话。
"""

import sys
import asyncio
import argparse
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.config import get_config, update_config
from audio_agent.core import SessionManager


def setup_logging(verbose: bool = False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )


def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🎤 Audio Agent - 实时语音对话演示")
    print("=" * 60)
    print("基于 uv + Ray 的现代化语音对话系统")
    print("支持实时语音输入和AI语音回复")
    print()


def check_requirements():
    """检查运行要求"""
    print("📋 检查运行要求...")
    
    # 检查Python版本
    if sys.version_info < (3, 10):
        print("❌ 需要Python 3.10或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的包
    try:
        import ray
        print(f"✅ Ray版本: {ray.__version__}")
    except ImportError:
        print("❌ Ray未安装，请运行: uv sync")
        sys.exit(1)
    
    try:
        import pyaudio
        print("✅ PyAudio已安装")
    except ImportError:
        print("❌ PyAudio未安装，请运行: uv sync")
        sys.exit(1)
    
    print()


def check_audio_devices():
    """检查音频设备"""
    print("🎵 检查音频设备...")
    
    try:
        import pyaudio
        p = pyaudio.PyAudio()
        
        input_devices = []
        output_devices = []
        
        for i in range(p.get_device_count()):
            info = p.get_device_info_by_index(i)
            if info['maxInputChannels'] > 0:
                input_devices.append(info['name'])
            if info['maxOutputChannels'] > 0:
                output_devices.append(info['name'])
        
        p.terminate()
        
        if input_devices:
            print(f"✅ 找到 {len(input_devices)} 个输入设备")
            for device in input_devices[:3]:  # 只显示前3个
                print(f"   📱 {device}")
        else:
            print("⚠️  未找到音频输入设备")
        
        if output_devices:
            print(f"✅ 找到 {len(output_devices)} 个输出设备")
            for device in output_devices[:3]:  # 只显示前3个
                print(f"   🔊 {device}")
        else:
            print("⚠️  未找到音频输出设备")
        
        print()
        
    except Exception as e:
        print(f"❌ 音频设备检查失败: {e}")
        print()


def configure_api_keys(app_id: str = None, access_key: str = None):
    """配置API密钥"""
    print("🔑 配置API密钥...")
    
    config = get_config()
    
    # 检查是否已配置
    if config.websocket.app_id and config.websocket.access_key:
        print("✅ API密钥已配置")
        print(f"   App ID: {config.websocket.app_id[:8]}...")
        print(f"   Access Key: {config.websocket.access_key[:8]}...")
        return True
    
    # 从参数获取
    if app_id and access_key:
        update_config(
            websocket={
                "app_id": app_id,
                "access_key": access_key
            }
        )
        print("✅ API密钥已从参数设置")
        return True
    
    # 提示用户设置
    print("❌ 未配置API密钥")
    print("请通过以下方式之一配置:")
    print("1. 环境变量:")
    print("   export AUDIO_AGENT_WEBSOCKET__APP_ID='your_app_id'")
    print("   export AUDIO_AGENT_WEBSOCKET__ACCESS_KEY='your_access_key'")
    print("2. 命令行参数:")
    print("   python scripts/demo.py --app-id 'your_app_id' --access-key 'your_access_key'")
    print()
    return False


async def run_demo(bot_name: str = "AI助手", duration: int = 60):
    """运行演示"""
    print(f"🚀 启动语音对话演示...")
    print(f"   机器人名称: {bot_name}")
    print(f"   演示时长: {duration}秒")
    print()
    
    # 获取配置
    config = get_config()
    
    # 创建会话管理器
    session_manager = SessionManager(config)
    
    try:
        # 启动会话
        print("⏳ 正在启动会话...")
        await session_manager.start()
        
        print("✅ 会话启动成功!")
        print(f"📊 Ray仪表板: http://localhost:{config.ray.dashboard_port}")
        print()
        print("🎤 开始说话，AI将实时回复...")
        print("💡 提示: 说话时保持清晰，等待AI回复完成后再继续")
        print("⏹️  按 Ctrl+C 可以随时退出")
        print()
        
        # 保持运行指定时间
        for remaining in range(duration, 0, -1):
            if not session_manager.is_running():
                break
            
            if remaining % 10 == 0:  # 每10秒显示一次剩余时间
                print(f"⏰ 剩余时间: {remaining}秒")
            
            await asyncio.sleep(1)
        
        print("\n⏰ 演示时间结束")
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在退出...")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        logging.exception("演示错误详情:")
    finally:
        # 停止会话
        print("⏳ 正在停止会话...")
        await session_manager.stop()
        print("✅ 会话已安全停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Audio Agent 语音对话演示",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python scripts/demo.py --app-id "your_app_id" --access-key "your_access_key"
  python scripts/demo.py --bot-name "技术助手" --duration 120 --verbose
  
环境变量:
  AUDIO_AGENT_WEBSOCKET__APP_ID     - API应用ID
  AUDIO_AGENT_WEBSOCKET__ACCESS_KEY - API访问密钥
        """
    )
    
    parser.add_argument(
        "--app-id",
        help="API应用ID"
    )
    parser.add_argument(
        "--access-key", 
        help="API访问密钥"
    )
    parser.add_argument(
        "--bot-name",
        default="AI助手",
        help="机器人名称 (默认: AI助手)"
    )
    parser.add_argument(
        "--duration",
        type=int,
        default=60,
        help="演示时长(秒) (默认: 60)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="显示详细日志"
    )
    parser.add_argument(
        "--skip-checks",
        action="store_true",
        help="跳过环境检查"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 打印横幅
    print_banner()
    
    # 环境检查
    if not args.skip_checks:
        check_requirements()
        check_audio_devices()
    
    # 配置API密钥
    if not configure_api_keys(args.app_id, args.access_key):
        sys.exit(1)
    
    # 更新机器人名称
    if args.bot_name != "AI助手":
        update_config(
            session={
                "dialog": {
                    "bot_name": args.bot_name
                }
            }
        )
    
    # 运行演示
    try:
        asyncio.run(run_demo(args.bot_name, args.duration))
    except KeyboardInterrupt:
        print("\n👋 演示已取消")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)
    
    print("\n🎉 演示完成，感谢使用Audio Agent!")


if __name__ == "__main__":
    main()
