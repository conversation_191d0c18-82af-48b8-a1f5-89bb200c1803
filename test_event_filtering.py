#!/usr/bin/env python3
"""
Test script to verify TTS event filtering works correctly.
"""

import asyncio
import logging
from audio_agent.events.event_handler import <PERSON>Handler
from audio_agent.events.event_processor import EventProcessor
from audio_agent.events.event_types import EventType, ServerEvent
from audio_agent.config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_tts_filtering():
    """Test TTS event filtering during tool calling."""
    print("🧪 Testing TTS Event Filtering")
    print("=" * 50)
    
    # Create event processor
    config = get_config()
    event_processor = EventProcessor('test-session', config, None)
    
    # Start event processing
    await event_processor.start_processing()
    
    # Simulate tool calling state
    event_processor.tool_calling_active = True
    event_processor.server_auto_response_started = False
    event_processor.current_tts_should_skip = False
    
    print("✅ Event processor started with tool calling active")
    
    # Test 1: Server auto-response (should be filtered)
    print("\n🧪 Test 1: Server auto-response (tts_type=default)")
    server_auto_message = {
        'message_type': 'SERVER_FULL_RESPONSE',
        'event': 350,  # TTS_SENTENCE_START
        'session_id': 'test-session',
        'payload': {
            'tts_type': 'default',
            'text': '现在是下午，具体时间你可以看看手机哦。',
            'tts_task_id': 'auto-response-123'
        }
    }
    
    await event_processor.process_websocket_message(server_auto_message)
    await asyncio.sleep(0.1)  # Let event process
    
    if hasattr(event_processor, 'current_tts_should_skip') and event_processor.current_tts_should_skip:
        print("✅ PASS: Server auto-response correctly marked for filtering")
    else:
        print("❌ FAIL: Server auto-response not filtered")
    
    # Test 2: Tool result response (should be allowed)
    print("\n🧪 Test 2: Tool result response (tts_type=chat_tts_text)")
    tool_result_message = {
        'message_type': 'SERVER_FULL_RESPONSE',
        'event': 350,  # TTS_SENTENCE_START
        'session_id': 'test-session',
        'payload': {
            'tts_type': 'chat_tts_text',
            'text': '现在是晚上10点58分。',
            'tts_task_id': 'tool-result-456'
        }
    }
    
    await event_processor.process_websocket_message(tool_result_message)
    await asyncio.sleep(0.1)  # Let event process
    
    if hasattr(event_processor, 'current_tts_should_skip') and not event_processor.current_tts_should_skip:
        print("✅ PASS: Tool result response correctly allowed")
    else:
        print("❌ FAIL: Tool result response incorrectly filtered")
    
    # Test 3: Normal mode (no tool calling)
    print("\n🧪 Test 3: Normal mode (no tool calling)")
    event_processor.tool_calling_active = False
    
    normal_message = {
        'message_type': 'SERVER_FULL_RESPONSE',
        'event': 350,  # TTS_SENTENCE_START
        'session_id': 'test-session',
        'payload': {
            'tts_type': 'default',
            'text': '你好！有什么可以帮助您的吗？',
            'tts_task_id': 'normal-789'
        }
    }
    
    await event_processor.process_websocket_message(normal_message)
    await asyncio.sleep(0.1)  # Let event process
    
    if hasattr(event_processor, 'current_tts_should_skip') and not event_processor.current_tts_should_skip:
        print("✅ PASS: Normal mode correctly allows all TTS")
    else:
        print("❌ FAIL: Normal mode incorrectly filters TTS")
    
    # Stop event processing
    event_processor.stop_processing()
    print("\n✅ Event processor stopped")
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    asyncio.run(test_tts_filtering())
