#!/usr/bin/env python3
"""
Audio Agent 简单使用示例

这个示例展示了如何用最少的代码使用Audio Agent。
"""

import sys
import asyncio
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.config import update_config
from audio_agent.core import SessionManager


async def simple_voice_chat():
    """简单的语音对话示例"""
    
    # 配置API密钥（请替换为您的真实密钥）
    update_config(
        websocket={
            "app_id": "your_app_id_here",  # 替换为您的App ID
            "access_key": "your_access_key_here"  # 替换为您的Access Key
        },
        session={
            "dialog": {
                "bot_name": "简单助手",
                "system_role": "你是一个友好的AI助手，回答简洁明了。"
            }
        }
    )
    
    # 获取配置
    from audio_agent.config import get_config
    config = get_config()
    
    # 创建会话管理器
    session_manager = SessionManager(config)
    
    print("🎤 启动简单语音对话...")
    print("说话时请保持清晰，AI会实时回复")
    print("按 Ctrl+C 退出")
    print()
    
    try:
        # 启动会话
        await session_manager.start()
        print("✅ 语音对话已启动，开始说话吧！")
        
        # 保持运行
        while session_manager.is_running():
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n👋 正在退出...")
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        # 停止会话
        await session_manager.stop()
        print("✅ 已安全退出")


if __name__ == "__main__":
    # 检查API密钥配置提醒
    print("⚠️  请确保已配置API密钥:")
    print("1. 修改此文件中的 app_id 和 access_key")
    print("2. 或设置环境变量:")
    print("   export AUDIO_AGENT_WEBSOCKET__APP_ID='your_app_id'")
    print("   export AUDIO_AGENT_WEBSOCKET__ACCESS_KEY='your_access_key'")
    print()
    
    # 运行示例
    asyncio.run(simple_voice_chat())
