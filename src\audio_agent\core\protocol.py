"""
Protocol definitions for the real-time dialog system.

This module contains protocol constants and utilities for WebSocket communication.
"""

import gzip
import json
from typing import Optional, Dict, Any
from enum import IntEnum


class ProtocolVersion(IntEnum):
    """Protocol version constants."""
    DEFAULT = 0b0001


class MessageType(IntEnum):
    """Message type constants."""
    CLIENT_FULL_REQUEST = 0b0001
    CLIENT_AUDIO_ONLY_REQUEST = 0b0010
    SERVER_FULL_RESPONSE = 0b1001
    SERVER_ACK = 0b1011
    SERVER_ERROR_RESPONSE = 0b1111


class MessageFlags(IntEnum):
    """Message type specific flags."""
    NO_SEQUENCE = 0b0000  # no check sequence
    POS_SEQUENCE = 0b0001
    NEG_SEQUENCE = 0b0010
    NEG_SEQUENCE_1 = 0b0011
    MSG_WITH_EVENT = 0b0100


class SerializationMethod(IntEnum):
    """Message serialization methods."""
    NO_SERIALIZATION = 0b0000
    JSON = 0b0001
    THRIFT = 0b0011
    CUSTOM_TYPE = 0b1111


class CompressionType(IntEnum):
    """Message compression types."""
    NO_COMPRESSION = 0b0000
    GZIP = 0b0001
    CUSTOM_COMPRESSION = 0b1111


# Protocol bit sizes
PROTOCOL_VERSION_BITS = 4
HEADER_BITS = 4
MESSAGE_TYPE_BITS = 4
MESSAGE_TYPE_SPECIFIC_FLAGS_BITS = 4
MESSAGE_SERIALIZATION_BITS = 4
MESSAGE_COMPRESSION_BITS = 4
RESERVED_BITS = 8

DEFAULT_HEADER_SIZE = 0b0001


def generate_header(
    version: int = ProtocolVersion.DEFAULT,
    message_type: int = MessageType.CLIENT_FULL_REQUEST,
    message_type_specific_flags: int = MessageFlags.MSG_WITH_EVENT,
    serial_method: int = SerializationMethod.JSON,
    compression_type: int = CompressionType.GZIP,
    reserved_data: int = 0x00,
    extension_header: bytes = bytes()
) -> bytes:
    """
    Generate protocol header using the same logic as working example.

    Args:
        version: Protocol version
        message_type: Message type
        message_type_specific_flags: Message flags
        serial_method: Serialization method
        compression_type: Compression type
        reserved_data: Reserved data
        extension_header: Extension header

    Returns:
        Generated header bytes
    """
    header = bytearray()
    header_size = int(len(extension_header) / 4) + 1
    header.append((version << 4) | header_size)
    header.append((message_type << 4) | message_type_specific_flags)
    header.append((serial_method << 4) | compression_type)
    header.append(reserved_data)
    header.extend(extension_header)

    return bytes(header)


def parse_header(header_bytes: bytes) -> Dict[str, Any]:
    """
    Parse protocol header.
    
    Args:
        header_bytes: Header bytes to parse
        
    Returns:
        Parsed header information
    """
    if len(header_bytes) < 4:
        raise ValueError("Header too short")
    
    first_byte = header_bytes[0]
    second_byte = header_bytes[1]
    third_byte = header_bytes[2]
    fourth_byte = header_bytes[3]
    
    version = (first_byte >> HEADER_BITS) & 0x0F
    header_size = first_byte & 0x0F
    
    message_type = (second_byte >> MESSAGE_TYPE_SPECIFIC_FLAGS_BITS) & 0x0F
    message_flags = second_byte & 0x0F
    
    serialization = (third_byte >> MESSAGE_COMPRESSION_BITS) & 0x0F
    compression = third_byte & 0x0F
    
    return {
        "version": version,
        "header_size": header_size,
        "message_type": message_type,
        "message_flags": message_flags,
        "serialization": serialization,
        "compression": compression,
        "reserved": fourth_byte,
        "extension_header": header_bytes[4:] if len(header_bytes) > 4 else b""
    }


def encode_message(
    data: Dict[str, Any],
    message_type: int = MessageType.CLIENT_FULL_REQUEST,
    compression: bool = True
) -> bytes:
    """
    Encode message with protocol header.
    
    Args:
        data: Message data to encode
        message_type: Message type
        compression: Whether to use compression
        
    Returns:
        Encoded message bytes
    """
    # Serialize data
    json_data = json.dumps(data, ensure_ascii=False).encode('utf-8')
    
    # Compress if requested
    if compression:
        json_data = gzip.compress(json_data)
        compression_type = CompressionType.GZIP
    else:
        compression_type = CompressionType.NO_COMPRESSION
    
    # Generate header
    header = generate_header(
        message_type=message_type,
        serial_method=SerializationMethod.JSON,
        compression_type=compression_type
    )
    
    # Combine header and data
    return header + json_data


def decode_message(message_bytes: bytes) -> Dict[str, Any]:
    """
    Decode message with protocol header.
    
    Args:
        message_bytes: Message bytes to decode
        
    Returns:
        Decoded message data
    """
    if len(message_bytes) < 4:
        raise ValueError("Message too short")
    
    # Parse header
    header_info = parse_header(message_bytes[:4])
    
    # Extract data
    data_bytes = message_bytes[4:]
    
    # Decompress if needed
    if header_info["compression"] == CompressionType.GZIP:
        data_bytes = gzip.decompress(data_bytes)
    
    # Deserialize
    if header_info["serialization"] == SerializationMethod.JSON:
        return json.loads(data_bytes.decode('utf-8'))
    else:
        raise ValueError(f"Unsupported serialization method: {header_info['serialization']}")


class ProtocolHandler:
    """Protocol handler for managing message encoding/decoding."""
    
    def __init__(self, use_compression: bool = True):
        """
        Initialize protocol handler.
        
        Args:
            use_compression: Whether to use compression by default
        """
        self.use_compression = use_compression
    
    def encode(self, data: Dict[str, Any], message_type: int = MessageType.CLIENT_FULL_REQUEST) -> bytes:
        """Encode message."""
        return encode_message(data, message_type, self.use_compression)
    
    def decode(self, message_bytes: bytes) -> Dict[str, Any]:
        """Decode message."""
        return decode_message(message_bytes)
