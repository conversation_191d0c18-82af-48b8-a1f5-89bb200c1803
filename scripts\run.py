#!/usr/bin/env python3
"""
Run script for the audio agent.

This script provides a convenient way to run the audio agent with various options.
"""

import argparse
import asyncio
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.main import main
from audio_agent.config import update_config


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Audio Agent - Real-time Voice Dialog System")
    
    parser.add_argument(
        "--app-id",
        type=str,
        help="API App ID for the voice service"
    )
    
    parser.add_argument(
        "--access-key", 
        type=str,
        help="API Access Key for the voice service"
    )
    
    parser.add_argument(
        "--bot-name",
        type=str,
        default="豆包",
        help="Bot name (default: 豆包)"
    )
    
    parser.add_argument(
        "--input-sample-rate",
        type=int,
        default=16000,
        help="Input audio sample rate (default: 16000)"
    )
    
    parser.add_argument(
        "--output-sample-rate",
        type=int,
        default=24000,
        help="Output audio sample rate (default: 24000)"
    )
    
    parser.add_argument(
        "--ray-dashboard-port",
        type=int,
        default=8265,
        help="Ray dashboard port (default: 8265)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    return parser.parse_args()


def main_cli():
    """Main CLI entry point."""
    args = parse_args()
    
    # Update configuration with command line arguments
    config_updates = {}
    
    if args.app_id:
        config_updates["websocket"] = {"app_id": args.app_id}
    
    if args.access_key:
        if "websocket" not in config_updates:
            config_updates["websocket"] = {}
        config_updates["websocket"]["access_key"] = args.access_key
    
    if args.bot_name != "豆包":
        config_updates["session"] = {"dialog": {"bot_name": args.bot_name}}
    
    if args.input_sample_rate != 16000:
        config_updates["input_audio"] = {"sample_rate": args.input_sample_rate}
    
    if args.output_sample_rate != 24000:
        config_updates["output_audio"] = {"sample_rate": args.output_sample_rate}
    
    if args.ray_dashboard_port != 8265:
        config_updates["ray"] = {"dashboard_port": args.ray_dashboard_port}
    
    if config_updates:
        update_config(**config_updates)
    
    # Set up logging level
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Run the main application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main_cli()
