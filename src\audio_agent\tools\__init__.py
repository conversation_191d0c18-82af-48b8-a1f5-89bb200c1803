"""
Tools module for the audio agent.

This module provides a comprehensive tool system that allows the AI agent
to call external functions and services to enhance its capabilities.

Based on Volcengine's Function Calling API.
"""

from .base import BaseTool, ToolResult, ToolError
from .registry import ToolRegistry, get_tool_registry
from .executor import ToolExecutor

__all__ = [
    "BaseTool",
    "ToolResult",
    "ToolError",
    "ToolRegistry",
    "get_tool_registry",
    "ToolExecutor",
]