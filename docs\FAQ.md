# 常见问题解答 (FAQ)

本文档收集了Audio Agent项目的常见问题和解决方案。

## 🚀 安装和环境

### Q: 如何安装uv包管理器？

**A**: 可以通过以下方式安装uv：

```bash
# 使用官方安装脚本（推荐）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用pip安装
pip install uv

# 或使用conda安装
conda install -c conda-forge uv
```

### Q: Python版本要求是什么？

**A**: 项目要求Python 3.10或更高版本。可以通过以下命令检查：

```bash
python --version
# 或
python3 --version
```

### Q: 为什么选择uv而不是pip？

**A**: uv相比pip有以下优势：
- **速度快**：依赖解析和安装速度比pip快10-100倍
- **内存效率**：更低的内存使用
- **更好的依赖解析**：更准确的依赖冲突检测
- **现代化**：支持最新的Python包管理标准

### Q: 如何在不同操作系统上安装？

**A**: 
- **Linux/macOS**: 使用上述curl命令
- **Windows**: 下载Windows安装包或使用PowerShell：
  ```powershell
  powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
  ```

## 🎵 音频相关

### Q: 遇到"ALSA lib"错误怎么办？

**A**: 这些错误通常出现在Linux系统上，是正常的警告信息，不影响功能：

```
ALSA lib confmisc.c:855:(parse_card) cannot find card '0'
```

解决方案：
1. **忽略警告**：这些警告不影响程序运行
2. **安装音频驱动**：
   ```bash
   sudo apt-get install alsa-utils pulseaudio
   ```
3. **配置音频设备**：
   ```bash
   # 列出音频设备
   aplay -l
   # 测试音频输出
   speaker-test -t wav
   ```

### Q: 如何解决"Cannot connect to server socket"错误？

**A**: 这个错误与JACK音频服务器相关：

```
Cannot connect to server socket err = No such file or directory
jack server is not running or cannot be started
```

解决方案：
1. **忽略错误**：如果不使用JACK，可以忽略
2. **安装JACK**：
   ```bash
   sudo apt-get install jackd2
   ```
3. **使用PulseAudio**：大多数Linux发行版默认使用PulseAudio

### Q: 音频设备权限问题怎么解决？

**A**: 在Linux上可能遇到音频设备权限问题：

```bash
# 将用户添加到audio组
sudo usermod -a -G audio $USER

# 重新登录或重启系统使更改生效
```

### Q: 如何测试音频设备是否正常？

**A**: 可以使用以下Python代码测试：

```python
import pyaudio

def test_audio_devices():
    p = pyaudio.PyAudio()
    print(f"音频设备数量: {p.get_device_count()}")
    
    for i in range(p.get_device_count()):
        info = p.get_device_info_by_index(i)
        print(f"设备 {i}: {info['name']}")
        print(f"  输入通道: {info['maxInputChannels']}")
        print(f"  输出通道: {info['maxOutputChannels']}")
    
    p.terminate()

test_audio_devices()
```

## 🔧 Ray相关

### Q: Ray初始化失败怎么办？

**A**: 常见的Ray初始化问题：

1. **端口冲突**：
   ```python
   # 通过配置修改端口
   from audio_agent.config import update_config
   update_config(
       ray={"dashboard_port": 8266}
   )

   # 或直接初始化
   ray.init(dashboard_port=8266)
   ```

2. **内存不足**：
   ```python
   # 通过配置限制内存
   update_config(
       ray={
           "object_store_memory": 1000000000,  # 1GB
           "num_cpus": 2  # 限制CPU使用
       }
   )
   ```

3. **清理之前的Ray进程**：
   ```bash
   ray stop  # 停止所有Ray进程
   ```

4. **连接模式问题**：
   ```python
   # 强制本地模式
   update_config(
       ray={"address": "local"}
   )

   # 或连接到现有集群
   update_config(
       ray={"address": "ray://head-node:10001"}
   )
   ```

### Q: 如何查看Ray仪表板？

**A**: Ray仪表板默认运行在8265端口：

1. 启动应用后访问：http://localhost:8265
2. 如果端口被占用，会自动使用其他端口，查看启动日志获取实际端口
3. 可以通过配置指定端口：
   ```python
   # 推荐方式：通过配置
   from audio_agent.config import update_config
   update_config(
       ray={
           "dashboard_host": "0.0.0.0",
           "dashboard_port": 8265
       }
   )

   # 或直接初始化
   ray.init(dashboard_host="0.0.0.0", dashboard_port=8265)
   ```

### Q: Ray Actor创建失败怎么办？

**A**: 检查以下几点：

1. **Ray是否正确初始化**：
   ```python
   if not ray.is_initialized():
       ray.init()
   ```

2. **资源是否足够**：
   ```python
   print(ray.cluster_resources())  # 查看可用资源
   ```

3. **Actor定义是否正确**：
   ```python
   @ray.remote
   class MyActor:
       pass
   ```

### Q: 如何连接到远程Ray集群？

**A**: 可以通过配置Ray地址连接到远程集群：

1. **连接到现有集群**：
   ```python
   from audio_agent.config import update_config

   update_config(
       ray={
           "address": "ray://head-node-ip:10001",  # 远程集群地址
           "dashboard_host": "0.0.0.0"            # 允许外部访问仪表板
       }
   )
   ```

2. **Kubernetes环境**：
   ```python
   update_config(
       ray={
           "address": "ray://ray-head-service:10001",
           "dashboard_host": "0.0.0.0",
           "dashboard_port": 8265
       }
   )
   ```

3. **测试连接**：
   ```python
   import socket
   try:
       sock = socket.create_connection(("head-node-ip", 10001), timeout=5)
       print("✅ 网络连接正常")
       sock.close()
   except Exception as e:
       print(f"❌ 网络连接失败: {e}")
   ```

### Q: Ray集群资源不足怎么办？

**A**: 可以通过以下方式解决：

1. **检查集群资源**：
   ```python
   import ray
   if ray.is_initialized():
       resources = ray.cluster_resources()
       print(f"可用资源: {resources}")
   ```

2. **调整资源需求**：
   ```python
   update_config(
       ray={
           "num_cpus": 2,                    # 减少CPU需求
           "object_store_memory": 1000000000 # 减少内存需求（1GB）
       }
   )
   ```

3. **扩展集群**：
   ```bash
   # 在新节点上启动Ray worker
   ray start --address='head-node-ip:10001'
   ```

## 🌐 网络和WebSocket

### Q: WebSocket连接失败怎么办？

**A**: 检查以下几点：

1. **网络连接**：确保能访问WebSocket服务器
2. **API密钥**：检查app_id和access_key是否正确
3. **防火墙设置**：确保WebSocket端口未被阻止
4. **SSL证书**：某些环境可能需要配置SSL证书

### Q: 如何调试WebSocket连接？

**A**: 可以使用以下方法：

1. **启用详细日志**：
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **使用wscat测试**：
   ```bash
   npm install -g wscat
   wscat -c "wss://your-websocket-url"
   ```

3. **检查网络连接**：
   ```bash
   ping your-server-domain
   telnet your-server-domain 443
   ```

## 🧪 测试相关

### Q: 测试运行失败怎么办？

**A**: 常见测试问题：

1. **依赖缺失**：
   ```bash
   uv sync --group dev  # 安装开发依赖
   ```

2. **Ray未正确清理**：
   ```bash
   ray stop  # 清理Ray进程
   ```

3. **音频设备问题**：测试会模拟音频设备，通常不需要真实设备

### Q: 如何跳过需要硬件的测试？

**A**: 使用pytest标记：

```python
import pytest

@pytest.mark.skipif(not has_audio_device(), reason="需要音频设备")
def test_real_audio():
    pass
```

运行时跳过：
```bash
uv run pytest -m "not hardware"
```

## 🔧 配置相关

### Q: 如何设置环境变量？

**A**: 支持多种方式：

1. **命令行设置**：
   ```bash
   export AUDIO_AGENT_WEBSOCKET__APP_ID="your_app_id"
   ```

2. **使用.env文件**：
   ```bash
   # .env文件
   AUDIO_AGENT_WEBSOCKET__APP_ID=your_app_id
   AUDIO_AGENT_WEBSOCKET__ACCESS_KEY=your_access_key
   ```

3. **代码中设置**：
   ```python
   from audio_agent.config import update_config
   update_config(websocket={"app_id": "your_app_id"})
   ```

### Q: 配置优先级是什么？

**A**: 配置优先级（从高到低）：
1. 代码中直接设置
2. 环境变量
3. 配置文件
4. 默认值

## 🚀 性能优化

### Q: 如何提高音频处理性能？

**A**: 几个优化建议：

1. **调整音频块大小**：
   ```python
   config.input_audio.chunk = 1600  # 较小的块大小降低延迟
   ```

2. **使用更多CPU核心**：
   ```python
   ray.init(num_cpus=8)  # 指定CPU核心数
   ```

3. **优化音频格式**：使用合适的采样率和位深度

### Q: 内存使用过高怎么办？

**A**: 内存优化方法：

1. **限制Ray对象存储**：
   ```python
   ray.init(object_store_memory=1000000000)  # 1GB
   ```

2. **及时清理音频数据**：避免长时间缓存大量音频数据

3. **监控内存使用**：
   ```python
   import psutil
   print(f"内存使用: {psutil.virtual_memory().percent}%")
   ```

## 📞 获取帮助

### Q: 在哪里报告问题？

**A**: 可以通过以下方式获取帮助：

1. **查看文档**：首先查看项目文档
2. **搜索Issues**：在GitHub Issues中搜索类似问题
3. **创建新Issue**：如果没有找到解决方案，创建新的Issue
4. **联系维护者**：通过邮件联系项目维护者

### Q: 如何贡献代码？

**A**: 贡献流程：

1. Fork项目
2. 创建特性分支
3. 编写代码和测试
4. 提交Pull Request
5. 等待代码审查

详细信息请参考[开发指南](DEVELOPMENT.md)。

---

**如果您的问题没有在这里找到答案，请创建一个[新的Issue](../../issues/new)。**
