#!/usr/bin/env python3
"""
Ray architecture verification script.

This script verifies that the Ray actors are working correctly.
"""

import sys
import asyncio
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

import ray
from audio_agent.config import get_config
from audio_agent.core.actors import AudioInputActor, AudioOutputActor, DialogClientActor


async def test_ray_initialization():
    """Test Ray initialization."""
    print("Testing Ray initialization...")
    
    if not ray.is_initialized():
        ray.init(
            ignore_reinit_error=True,
            runtime_env={"working_dir": None}
        )
        print("✓ Ray initialized successfully")
    else:
        print("✓ Ray already initialized")
    
    print(f"✓ Ray cluster resources: {ray.cluster_resources()}")
    print()


async def test_audio_actors():
    """Test audio input and output actors."""
    print("Testing audio actors...")
    
    config = get_config()
    
    with patch('pyaudio.PyAudio') as mock_pyaudio:
        # Mock PyAudio
        mock_audio = MagicMock()
        mock_stream = MagicMock()
        mock_pyaudio.return_value = mock_audio
        mock_audio.open.return_value = mock_stream
        mock_stream.read.return_value = b'test_audio_data'
        
        # Test AudioInputActor
        input_actor = AudioInputActor.remote(config.input_audio)
        
        # Test basic methods
        is_recording = await input_actor.is_recording_active.remote()
        assert is_recording is False
        print("✓ AudioInputActor created and basic methods work")
        
        # Test starting recording
        started = await input_actor.start_recording.remote()
        assert started is True
        print("✓ AudioInputActor recording started")
        
        # Test stopping recording
        stopped = await input_actor.stop_recording.remote()
        assert stopped is True
        print("✓ AudioInputActor recording stopped")
        
        # Test AudioOutputActor
        output_actor = AudioOutputActor.remote(config.output_audio)
        
        # Test basic methods
        is_playing = await output_actor.is_playback_active.remote()
        assert is_playing is False
        print("✓ AudioOutputActor created and basic methods work")
        
        # Test starting playback
        started = await output_actor.start_playback.remote()
        assert started is True
        print("✓ AudioOutputActor playback started")
        
        # Test queuing audio data
        queued = await output_actor.queue_audio_data.remote(b'test_audio_data')
        assert queued is True
        print("✓ AudioOutputActor audio data queued")
        
        # Test stopping playback
        stopped = await output_actor.stop_playback.remote()
        assert stopped is True
        print("✓ AudioOutputActor playback stopped")
    
    print("Audio actors test passed!\n")


async def test_dialog_actor():
    """Test dialog client actor."""
    print("Testing dialog client actor...")
    
    config = get_config()
    
    # Test DialogClientActor
    dialog_actor = DialogClientActor.remote(config)
    
    # Test basic methods
    is_connected = await dialog_actor.is_connection_active.remote()
    assert is_connected is False
    print("✓ DialogClientActor created and basic methods work")
    
    print("Dialog client actor test passed!\n")


async def test_multiple_actors():
    """Test creating multiple actors simultaneously."""
    print("Testing multiple actors...")
    
    config = get_config()
    
    with patch('pyaudio.PyAudio') as mock_pyaudio:
        # Mock PyAudio
        mock_audio = MagicMock()
        mock_stream = MagicMock()
        mock_pyaudio.return_value = mock_audio
        mock_audio.open.return_value = mock_stream
        mock_stream.read.return_value = b'test_audio_data'
        
        # Create multiple actors
        input_actor1 = AudioInputActor.remote(config.input_audio)
        input_actor2 = AudioInputActor.remote(config.input_audio)
        output_actor1 = AudioOutputActor.remote(config.output_audio)
        output_actor2 = AudioOutputActor.remote(config.output_audio)
        dialog_actor1 = DialogClientActor.remote(config)
        dialog_actor2 = DialogClientActor.remote(config)
        
        # Test that all actors work
        results = await asyncio.gather(
            input_actor1.is_recording_active.remote(),
            input_actor2.is_recording_active.remote(),
            output_actor1.is_playback_active.remote(),
            output_actor2.is_playback_active.remote(),
            dialog_actor1.is_connection_active.remote(),
            dialog_actor2.is_connection_active.remote(),
        )
        
        assert all(result is False for result in results)
        print("✓ Multiple actors created and working simultaneously")
    
    print("Multiple actors test passed!\n")


async def test_actor_communication():
    """Test communication between actors."""
    print("Testing actor communication...")
    
    config = get_config()
    
    with patch('pyaudio.PyAudio') as mock_pyaudio:
        # Mock PyAudio
        mock_audio = MagicMock()
        mock_stream = MagicMock()
        mock_pyaudio.return_value = mock_audio
        mock_audio.open.return_value = mock_stream
        mock_stream.read.return_value = b'test_audio_data'
        
        # Create actors
        input_actor = AudioInputActor.remote(config.input_audio)
        output_actor = AudioOutputActor.remote(config.output_audio)
        
        # Start both actors
        await input_actor.start_recording.remote()
        await output_actor.start_playback.remote()
        
        # Simulate data flow: input -> output
        # Get data from input (simulated)
        audio_data = await input_actor.get_audio_data.remote()
        
        # Send data to output
        if audio_data:
            queued = await output_actor.queue_audio_data.remote(audio_data)
            assert queued is True
            print("✓ Data successfully passed from input to output actor")
        else:
            print("✓ No audio data available (expected in test environment)")
        
        # Stop both actors
        await input_actor.stop_recording.remote()
        await output_actor.stop_playback.remote()
    
    print("Actor communication test passed!\n")


async def main():
    """Main verification function."""
    print("=== Ray Architecture Verification ===\n")
    
    try:
        # Test Ray initialization
        await test_ray_initialization()
        
        # Test audio actors
        await test_audio_actors()
        
        # Test dialog actor
        await test_dialog_actor()
        
        # Test multiple actors
        await test_multiple_actors()
        
        # Test actor communication
        await test_actor_communication()
        
        print("🎉 All Ray architecture tests passed!")
        print("The Ray-based audio agent architecture is working correctly.")
        
    except Exception as e:
        print(f"✗ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
