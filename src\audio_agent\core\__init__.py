"""
Core modules for the audio agent.

This package contains the core functionality for the real-time audio dialog system.
"""

from .protocol import (
    ProtocolVersion,
    MessageType,
    MessageFlags,
    SerializationMethod,
    CompressionType,
    generate_header,
    parse_header,
    encode_message,
    decode_message,
    ProtocolHandler,
)

from .actors import (
    AudioInputActor,
    AudioOutputActor,
    DialogClientActor,
)

from .session_manager import SessionManager

__version__ = "0.1.0"

__all__ = [
    "ProtocolVersion",
    "MessageType",
    "MessageFlags",
    "SerializationMethod",
    "CompressionType",
    "generate_header",
    "parse_header",
    "encode_message",
    "decode_message",
    "ProtocolHandler",
    "AudioInputActor",
    "AudioOutputActor",
    "DialogClientActor",
    "SessionManager",
]
