"""
DateTime tool for the audio agent.

This tool provides date and time related functionality.
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, Optional
from ..base import BaseTool, ToolResult, ToolError, ToolParameter, ParameterType

logger = logging.getLogger(__name__)


class DateTimeTool(BaseTool):
    """
    DateTime tool that provides current time, date calculations, and time zone conversions.
    
    This tool can get current time, calculate date differences, and perform
    various date/time operations.
    """
    
    def __init__(self):
        """Initialize the datetime tool."""
        super().__init__(
            name="datetime",
            description="Get current date/time, calculate date differences, and perform time-related operations."
        )
        
        # Add parameters
        self.add_parameter(ToolParameter(
            name="operation",
            type=ParameterType.STRING,
            description="Operation to perform: 'current_time', 'current_date', 'current_datetime', 'format_time', 'add_time', 'time_difference'",
            required=True,
            enum=["current_time", "current_date", "current_datetime", "format_time", "add_time", "time_difference"]
        ))
        
        self.add_parameter(ToolParameter(
            name="timezone",
            type=ParameterType.STRING,
            description="Timezone for the operation (e.g., 'UTC', 'Asia/Shanghai', 'America/New_York'). Defaults to local timezone.",
            required=False,
            default="local"
        ))
        
        self.add_parameter(ToolParameter(
            name="format",
            type=ParameterType.STRING,
            description="Format string for datetime formatting (e.g., '%Y-%m-%d %H:%M:%S')",
            required=False,
            default="%Y-%m-%d %H:%M:%S"
        ))
        
        self.add_parameter(ToolParameter(
            name="days",
            type=ParameterType.INTEGER,
            description="Number of days to add/subtract (for add_time operation)",
            required=False,
            default=0
        ))
        
        self.add_parameter(ToolParameter(
            name="hours",
            type=ParameterType.INTEGER,
            description="Number of hours to add/subtract (for add_time operation)",
            required=False,
            default=0
        ))
        
        self.add_parameter(ToolParameter(
            name="minutes",
            type=ParameterType.INTEGER,
            description="Number of minutes to add/subtract (for add_time operation)",
            required=False,
            default=0
        ))
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute the datetime operation.
        
        Args:
            operation: Type of operation to perform
            timezone: Timezone for the operation
            format: Format string for output
            days: Days to add/subtract
            hours: Hours to add/subtract
            minutes: Minutes to add/subtract
            
        Returns:
            ToolResult with datetime information
        """
        try:
            # Validate parameters
            validated_params = self.validate_parameters(**kwargs)
            operation = validated_params["operation"]
            tz_name = validated_params.get("timezone", "local")
            format_str = validated_params.get("format", "%Y-%m-%d %H:%M:%S")
            
            logger.info(f"DateTime operation: {operation}")
            
            # Get timezone
            tz = self._get_timezone(tz_name)
            
            # Perform the requested operation
            if operation == "current_time":
                result = self._get_current_time(tz, format_str)
            elif operation == "current_date":
                result = self._get_current_date(tz)
            elif operation == "current_datetime":
                result = self._get_current_datetime(tz, format_str)
            elif operation == "format_time":
                result = self._format_current_time(tz, format_str)
            elif operation == "add_time":
                days = validated_params.get("days", 0)
                hours = validated_params.get("hours", 0)
                minutes = validated_params.get("minutes", 0)
                result = self._add_time(tz, format_str, days, hours, minutes)
            elif operation == "time_difference":
                result = self._time_difference(tz)
            else:
                raise ToolError(
                    f"Unknown operation: {operation}",
                    tool_name=self.name,
                    error_code="UNKNOWN_OPERATION"
                )
            
            return ToolResult(
                success=True,
                data=result
            )
            
        except Exception as e:
            logger.error(f"DateTime operation error: {e}")
            return ToolResult(
                success=False,
                error=f"DateTime operation failed: {str(e)}"
            )
    
    def _get_timezone(self, tz_name: str) -> Optional[timezone]:
        """
        Get timezone object from name.
        
        Args:
            tz_name: Timezone name
            
        Returns:
            Timezone object or None for local time
        """
        if tz_name.lower() in ["local", "system"]:
            return None
        elif tz_name.upper() == "UTC":
            return timezone.utc
        else:
            # For simplicity, we'll support basic timezone offsets
            # In a full implementation, you'd use pytz or zoneinfo
            try:
                if tz_name.startswith("UTC"):
                    # Handle UTC+8, UTC-5, etc.
                    offset_str = tz_name[3:]
                    if offset_str:
                        offset_hours = int(offset_str)
                        return timezone(timedelta(hours=offset_hours))
                return timezone.utc  # Default to UTC for unknown timezones
            except:
                return timezone.utc
    
    def _get_current_time(self, tz: Optional[timezone], format_str: str) -> Dict[str, Any]:
        """Get current time."""
        now = datetime.now(tz)
        return {
            "operation": "current_time",
            "time": now.strftime("%H:%M:%S"),
            "formatted": now.strftime(format_str),
            "timezone": str(tz) if tz else "local",
            "timestamp": now.timestamp()
        }
    
    def _get_current_date(self, tz: Optional[timezone]) -> Dict[str, Any]:
        """Get current date."""
        now = datetime.now(tz)
        return {
            "operation": "current_date",
            "date": now.strftime("%Y-%m-%d"),
            "year": now.year,
            "month": now.month,
            "day": now.day,
            "weekday": now.strftime("%A"),
            "timezone": str(tz) if tz else "local"
        }
    
    def _get_current_datetime(self, tz: Optional[timezone], format_str: str) -> Dict[str, Any]:
        """Get current datetime."""
        now = datetime.now(tz)
        return {
            "operation": "current_datetime",
            "datetime": now.strftime(format_str),
            "iso_format": now.isoformat(),
            "timestamp": now.timestamp(),
            "timezone": str(tz) if tz else "local"
        }
    
    def _format_current_time(self, tz: Optional[timezone], format_str: str) -> Dict[str, Any]:
        """Format current time with custom format."""
        now = datetime.now(tz)
        return {
            "operation": "format_time",
            "formatted": now.strftime(format_str),
            "format_used": format_str,
            "timezone": str(tz) if tz else "local"
        }
    
    def _add_time(self, tz: Optional[timezone], format_str: str, days: int, hours: int, minutes: int) -> Dict[str, Any]:
        """Add time to current datetime."""
        now = datetime.now(tz)
        delta = timedelta(days=days, hours=hours, minutes=minutes)
        new_time = now + delta
        
        return {
            "operation": "add_time",
            "original": now.strftime(format_str),
            "added": f"{days} days, {hours} hours, {minutes} minutes",
            "result": new_time.strftime(format_str),
            "timezone": str(tz) if tz else "local"
        }
    
    def _time_difference(self, tz: Optional[timezone]) -> Dict[str, Any]:
        """Get time difference information."""
        now = datetime.now(tz)
        utc_now = datetime.now(timezone.utc)
        
        return {
            "operation": "time_difference",
            "local_time": now.strftime("%Y-%m-%d %H:%M:%S"),
            "utc_time": utc_now.strftime("%Y-%m-%d %H:%M:%S"),
            "timezone": str(tz) if tz else "local",
            "utc_offset_seconds": (now - utc_now.replace(tzinfo=None)).total_seconds() if tz is None else 0
        }
