#!/usr/bin/env python3
"""
二进制日志清理测试

测试AudioAgent的日志清理功能，确保不会输出二进制数据到日志中。
"""

import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.utils.logging_utils import (
    clean_data_for_logging,
    log_response_summary,
    log_websocket_message,
    log_audio_data
)


def test_binary_data_cleaning():
    """测试二进制数据清理"""
    print("🧹 1. 二进制数据清理测试")
    print("=" * 50)
    
    # 模拟包含二进制数据的响应
    binary_audio_data = b'\xba\xfc\xffo\xba\xfd\xff\x87\xba\xfd\xff\x87\xba\xfb\xff\x7f'
    
    response_with_binary = {
        'message_type': 'SERVER_ACK',
        'event': 352,
        'session_id': 'test-session-123',
        'payload_msg': binary_audio_data,
        'payload': {
            'audio_data': binary_audio_data,
            'text': '这是正常的文本',
            'metadata': {
                'nested_binary': binary_audio_data,
                'normal_field': 'normal_value'
            }
        }
    }
    
    print("❌ 原始数据（包含二进制）:")
    print(f"  payload_msg: {response_with_binary['payload_msg'][:50]}...")
    print(f"  payload.audio_data: {response_with_binary['payload']['audio_data'][:50]}...")
    print()
    
    # 清理数据
    cleaned_data = clean_data_for_logging(response_with_binary)
    
    print("✅ 清理后的数据:")
    print(f"  payload_msg: {cleaned_data['payload_msg']}")
    print(f"  payload.audio_data: {cleaned_data['payload']['audio_data']}")
    print(f"  payload.text: {cleaned_data['payload']['text']}")
    print(f"  payload.metadata.nested_binary: {cleaned_data['payload']['metadata']['nested_binary']}")
    print(f"  payload.metadata.normal_field: {cleaned_data['payload']['metadata']['normal_field']}")
    print()


def test_websocket_message_logging():
    """测试WebSocket消息日志"""
    print("📡 2. WebSocket消息日志测试")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        force=True
    )
    
    logger = logging.getLogger("websocket_test")
    
    # 模拟包含二进制数据的WebSocket消息
    websocket_message = {
        'message_type': 'SERVER_ACK',
        'event': 352,
        'session_id': 'test-session-123',
        'payload_msg': b'\xba\xfc\xffo\xba\xfd\xff\x87' * 1000,  # 大量二进制数据
        'payload': {
            'text': '用户说话内容',
            'confidence': 0.95
        }
    }
    
    print("使用新的日志工具:")
    log_websocket_message(logger, websocket_message, "received")
    print()


def test_audio_data_logging():
    """测试音频数据日志"""
    print("🎵 3. 音频数据日志测试")
    print("=" * 50)
    
    logger = logging.getLogger("audio_test")
    
    # 模拟音频数据
    audio_chunk = b'\xba\xfc\xffo\xba\xfd\xff\x87' * 500  # 大量音频数据
    
    print("使用新的音频日志工具:")
    log_audio_data(logger, audio_chunk, "Audio chunk received")
    print()


def test_response_summary_logging():
    """测试响应摘要日志"""
    print("📋 4. 响应摘要日志测试")
    print("=" * 50)
    
    logger = logging.getLogger("response_test")
    
    # 模拟复杂的响应数据
    complex_response = {
        'message_type': 'SERVER_ACK',
        'event': 352,
        'session_id': 'test-session-123',
        'payload_msg': b'\xba\xfc\xffo\xba\xfd\xff\x87' * 2000,  # 大量二进制数据
        'payload': {
            'results': [
                {
                    'text': '今天天气怎么样？',
                    'is_interim': False,
                    'confidence': 0.98
                }
            ],
            'audio_metadata': {
                'sample_rate': 16000,
                'channels': 1,
                'raw_audio': b'\xba\xfc\xffo\xba\xfd\xff\x87' * 1000
            }
        }
    }
    
    print("使用新的响应摘要日志工具:")
    log_response_summary(logger, complex_response)
    print()


def test_long_string_truncation():
    """测试长字符串截断"""
    print("✂️ 5. 长字符串截断测试")
    print("=" * 50)
    
    # 模拟超长字符串
    very_long_string = "这是一个非常长的字符串，" * 100
    
    data_with_long_string = {
        'message': very_long_string,
        'short_message': '这是正常长度的消息'
    }
    
    print(f"原始字符串长度: {len(very_long_string)}")
    
    cleaned_data = clean_data_for_logging(data_with_long_string, max_str_length=100)
    
    print(f"清理后的消息: {cleaned_data['message']}")
    print(f"短消息保持不变: {cleaned_data['short_message']}")
    print()


def demonstrate_before_after():
    """演示优化前后的对比"""
    print("🔄 6. 优化前后对比")
    print("=" * 50)
    
    # 模拟优化前的日志输出
    binary_data = b'\xba\xfc\xffo\xba\xfd\xff\x87\xba\xfd\xff\x87\xba\xfb\xff\x7f' * 100
    
    print("❌ 优化前的日志输出:")
    print(f"Received response: {{'payload_msg': {binary_data[:100]}...}}")
    print("（会输出大量不可读的二进制数据）")
    print()
    
    print("✅ 优化后的日志输出:")
    cleaned = clean_data_for_logging({'payload_msg': binary_data})
    print(f"Response: {cleaned}")
    print("（清爽易读的日志信息）")
    print()


def main():
    """主测试函数"""
    print("🧪 AudioAgent 二进制日志清理测试")
    print("=" * 60)
    print("测试日志清理功能，确保不会输出二进制数据")
    print()
    
    test_binary_data_cleaning()
    test_websocket_message_logging()
    test_audio_data_logging()
    test_response_summary_logging()
    test_long_string_truncation()
    demonstrate_before_after()
    
    print("🎉 所有测试完成！")
    print()
    print("💡 总结:")
    print("  ✅ 二进制数据被替换为大小信息")
    print("  ✅ 长字符串被适当截断")
    print("  ✅ 嵌套数据结构正确处理")
    print("  ✅ 正常数据保持不变")
    print("  ✅ 日志输出清爽易读")
    print()
    print("🚀 现在AudioAgent的日志不会再有二进制数据污染了！")


if __name__ == "__main__":
    main()
