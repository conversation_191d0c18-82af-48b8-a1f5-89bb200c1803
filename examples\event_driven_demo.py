"""
Demonstration of the event-driven architecture following Volcengine's official practices.

This example shows how the AudioAgent now properly implements the event-driven
flow as described in the Volcengine documentation.
"""

import asyncio
import logging
import uuid
from datetime import datetime

from audio_agent.events import (
    EventType, ServerEvent, LLMEvent, EventHandler, 
    EventProcessor, LLMEnhancer
)
from audio_agent.config import get_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def demo_event_system():
    """Demonstrate the complete event-driven flow."""
    
    print("🎯 AudioAgent Event-Driven Architecture Demo")
    print("=" * 50)
    print("Following Volcengine's official practices:")
    print("https://www.volcengine.com/docs/6561/1594356")
    print()
    
    # Initialize components
    config = get_config()
    session_id = str(uuid.uuid4())
    
    print(f"📱 Session ID: {session_id}")
    print()
    
    # Create event processor
    event_processor = EventProcessor(session_id, config)
    print("✅ Event processor initialized")
    
    # Demo 1: ASR Event → Intent Recognition → Tool Calling
    print("\n🎤 Demo 1: ASR → Intent Recognition → Tool Calling")
    print("-" * 40)
    
    # Simulate ASR response for weather query
    asr_message = {
        "message_type": "SERVER_ACK",
        "event": 451,  # ASR_RESPONSE
        "results": [
            {
                "text": "What's the weather like in New York?",
                "is_interim": False
            }
        ]
    }
    
    print(f"📥 Simulating ASR result: '{asr_message['results'][0]['text']}'")

    # Process through event system
    event = await event_processor.process_websocket_message(asr_message)
    print(f"✅ Event processed: {event.event_type}")
    
    # Give time for async processing
    await asyncio.sleep(2)
    
    # Demo 2: Different Intent Types
    print("\n🧠 Demo 2: Different Intent Recognition")
    print("-" * 40)
    
    test_inputs = [
        "Search for Python programming tutorials",
        "Remind me to call mom in 30 minutes", 
        "Play some jazz music",
        "Turn on the living room lights",
        "Hello, how are you today?"
    ]
    
    llm_enhancer = LLMEnhancer(config)
    
    for user_input in test_inputs:
        print(f"\n📝 Input: '{user_input}'")
        
        intent_result = await llm_enhancer.recognize_intent(user_input, [])
        
        print(f"🎯 Intent: {intent_result['intent']}")
        print(f"🔧 Requires tools: {intent_result['requires_tools']}")
        if intent_result['requires_tools']:
            print(f"⚙️  Tools needed: {intent_result['tools']}")
            
            # Execute tools
            tool_results = await llm_enhancer.execute_tools(
                user_input, 
                intent_result['tools'], 
                []
            )
            
            print(f"✅ Tool execution completed: {len(tool_results)} results")
            
            # Generate enhanced response
            response = await llm_enhancer.generate_enhanced_response(
                user_input,
                tool_results,
                []
            )
            
            print(f"🤖 AI Response: '{response}'")
        else:
            # Generate direct response
            response = await llm_enhancer.generate_response(
                user_input,
                [],
                intent_result
            )
            
            print(f"🤖 AI Response: '{response}'")
    
    # Demo 3: Event Handler Registration
    print("\n📡 Demo 3: Custom Event Handlers")
    print("-" * 40)
    
    event_handler = EventHandler(session_id)
    
    # Register custom handlers
    async def custom_connection_handler(event):
        print(f"🔗 Custom connection handler called: {event.event_type}")

    async def custom_llm_handler(event):
        print(f"🧠 Custom LLM handler called: {event.event_type}")

    event_handler.register_handler(EventType.CONNECTION_STARTED, custom_connection_handler)
    event_handler.register_handler(EventType.LLM_TOOL_CALL, custom_llm_handler)
    
    # Create and process test events
    test_events = [
        LLMEvent(
            event_type=EventType.CONNECTION_STARTED,
            timestamp=datetime.now(),
            session_id=session_id,
            event_id=str(uuid.uuid4()),
            data={"action": "connection_established"}
        ),
        LLMEvent(
            event_type=EventType.LLM_TOOL_CALL,
            timestamp=datetime.now(),
            session_id=session_id,
            event_id=str(uuid.uuid4()),
            data={"tool": "get_weather"},
            tool_calls=["get_weather"]
        )
    ]
    
    for event in test_events:
        print(f"\n📤 Processing event: {event.event_type}")
        await event_handler._process_single_event(event)
    
    # Demo 4: Event Flow Simulation
    print("\n🔄 Demo 4: Complete Event Flow Simulation")
    print("-" * 40)
    
    print("Simulating the complete Volcengine event flow:")
    print("1. Audio Input → ASR")
    print("2. ASR Result → Intent Recognition")
    print("3. Intent → Tool Selection")
    print("4. Tool Execution → Enhanced Response")
    print("5. Response → TTS → Audio Output")
    print()
    
    # Simulate complete flow
    flow_steps = [
        ("🎤 Audio Input", "User speaks: 'What's the weather in Tokyo?'"),
        ("🔤 ASR Processing", "Converting speech to text..."),
        ("📝 ASR Result", "Text: 'What's the weather in Tokyo?'"),
        ("🧠 Intent Recognition", "Intent: weather_query, Confidence: 0.9"),
        ("🔧 Tool Selection", "Selected tool: get_weather"),
        ("⚙️  Tool Execution", "Calling weather API for Tokyo..."),
        ("📊 Tool Result", "Weather: 18°C, Cloudy, Humidity: 70%"),
        ("🤖 Response Generation", "Enhanced response with weather data"),
        ("🔊 TTS Processing", "Converting response to speech..."),
        ("🎵 Audio Output", "Playing synthesized speech to user")
    ]
    
    for step, description in flow_steps:
        print(f"{step} {description}")
        await asyncio.sleep(0.5)  # Simulate processing time
    
    print("\n✅ Event-driven architecture demonstration completed!")
    print("\n📋 Key Features Demonstrated:")
    print("• Event-driven message processing")
    print("• Intent recognition with confidence scoring")
    print("• Dynamic tool selection and execution")
    print("• Enhanced response generation")
    print("• Custom event handler registration")
    print("• Complete conversation flow simulation")
    print("\n🎯 This implementation now follows Volcengine's official practices!")


async def demo_event_types():
    """Demonstrate different event types and their usage."""
    
    print("\n📊 Event Types Reference")
    print("=" * 30)
    
    event_categories = {
        "Connection Events (50-59)": [
            (EventType.CONNECTION_STARTED, "Connection established"),
            (EventType.CONNECTION_FAILED, "Connection failed"),
            (EventType.CONNECTION_FINISHED, "Connection ended")
        ],
        "Session Events (150-159)": [
            (EventType.SESSION_STARTED, "Session started with dialog_id"),
            (EventType.SESSION_FINISHED, "Session ended"),
            (EventType.SESSION_FAILED, "Session failed")
        ],
        "TTS Events (350-359)": [
            (EventType.TTS_SENTENCE_START, "TTS sentence start"),
            (EventType.TTS_SENTENCE_END, "TTS sentence end"),
            (EventType.TTS_RESPONSE, "TTS audio data response"),
            (EventType.TTS_ENDED, "TTS synthesis completed")
        ],
        "ASR Events (450-459)": [
            (EventType.ASR_INFO, "First word detected (interrupt signal)"),
            (EventType.ASR_RESPONSE, "Speech recognition result"),
            (EventType.ASR_ENDED, "User finished speaking")
        ],
        "Chat Events (550-559)": [
            (EventType.CHAT_RESPONSE, "Model text response"),
            (EventType.CHAT_ENDED, "Model finished responding")
        ],
        "Custom LLM Events (1000+)": [
            (EventType.LLM_START, "LLM processing started"),
            (EventType.LLM_TOOL_CALL, "Tool calling initiated"),
            (EventType.LLM_TOOL_RESULT, "Tool execution result"),
            (EventType.LLM_FINAL_RESULT, "LLM processing completed"),
            (EventType.INTENT_RECOGNITION, "Intent recognition")
        ]
    }
    
    for category, events in event_categories.items():
        print(f"\n{category}")
        for event_type, description in events:
            print(f"  {event_type.value:3d} - {event_type.name}: {description}")


async def main():
    """Main demo function."""
    try:
        await demo_event_system()
        await demo_event_types()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        logger.exception("Demo failed")


if __name__ == "__main__":
    asyncio.run(main())
