#!/usr/bin/env python3
"""
LLM增强功能测试

测试AudioAgent的LLM增强功能，包括意图识别、工具调用等。
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.events import LLMEnhancer
from audio_agent.config import get_config


async def test_intent_recognition():
    """测试意图识别功能"""
    print("🧠 1. 意图识别测试")
    print("=" * 50)
    
    config = get_config()
    llm_enhancer = LLMEnhancer(config)
    
    test_inputs = [
        "今天北京的天气怎么样？",
        "帮我计算 2 + 3 * 4",
        "搜索一下人工智能的最新发展",
        "现在几点了？",
        "你好，我想聊天"
    ]
    
    for user_input in test_inputs:
        print(f"\n输入: '{user_input}'")
        
        try:
            intent_result = await llm_enhancer.recognize_intent(user_input, [])
            print(f"意图: {intent_result['intent']}")
            print(f"置信度: {intent_result['confidence']}")
            print(f"需要工具: {intent_result['requires_tools']}")
            if intent_result['requires_tools']:
                print(f"工具列表: {intent_result['tools']}")
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print()


async def test_tool_execution():
    """测试工具执行功能"""
    print("🔧 2. 工具执行测试")
    print("=" * 50)
    
    config = get_config()
    llm_enhancer = LLMEnhancer(config)
    
    test_cases = [
        {
            "input": "计算 15 + 27 * 3",
            "tools": ["calculator"]
        },
        {
            "input": "现在是什么时间？",
            "tools": ["get_datetime"]
        },
        {
            "input": "搜索Python编程教程",
            "tools": ["search_web"]
        }
    ]
    
    for test_case in test_cases:
        user_input = test_case["input"]
        tools = test_case["tools"]
        
        print(f"\n输入: '{user_input}'")
        print(f"工具: {tools}")
        
        try:
            results = await llm_enhancer.execute_tools(user_input, tools, [])
            
            for result in results:
                tool_name = result["tool"]
                success = result["success"]
                
                if success:
                    print(f"✅ {tool_name}: {result['result']}")
                else:
                    print(f"❌ {tool_name}: {result['error']}")
                    
        except Exception as e:
            print(f"❌ 工具执行错误: {e}")
    
    print()


async def test_full_enhancement_flow():
    """测试完整的LLM增强流程"""
    print("🚀 3. 完整增强流程测试")
    print("=" * 50)
    
    config = get_config()
    llm_enhancer = LLMEnhancer(config)
    
    test_inputs = [
        "帮我算一下 sqrt(144) + 5 * 3",
        "今天是几号？",
        "查找一下机器学习的基础知识"
    ]
    
    for user_input in test_inputs:
        print(f"\n🎯 处理: '{user_input}'")
        print("-" * 30)
        
        try:
            # 步骤1: 意图识别
            print("1️⃣ 意图识别...")
            intent_result = await llm_enhancer.recognize_intent(user_input, [])
            print(f"   意图: {intent_result['intent']}")
            print(f"   需要工具: {intent_result['requires_tools']}")
            
            if intent_result['requires_tools']:
                # 步骤2: 工具执行
                print("2️⃣ 执行工具...")
                tool_results = await llm_enhancer.execute_tools(
                    user_input, 
                    intent_result['tools'], 
                    []
                )
                
                for result in tool_results:
                    if result['success']:
                        print(f"   ✅ {result['tool']}: 成功")
                    else:
                        print(f"   ❌ {result['tool']}: {result['error']}")
                
                # 步骤3: 生成增强响应
                print("3️⃣ 生成响应...")
                response = await llm_enhancer.generate_enhanced_response(
                    user_input,
                    tool_results,
                    []
                )
                print(f"   📝 响应: {response}")
            else:
                # 生成普通响应
                print("2️⃣ 生成普通响应...")
                response = await llm_enhancer.generate_response(
                    user_input,
                    [],
                    intent_result
                )
                print(f"   📝 响应: {response}")
                
        except Exception as e:
            print(f"❌ 流程错误: {e}")
    
    print()


async def test_openai_integration():
    """测试OpenAI集成"""
    print("🤖 4. OpenAI集成测试")
    print("=" * 50)
    
    import os
    
    if os.getenv('OPENAI_API_KEY'):
        print("✅ 检测到OPENAI_API_KEY")
        
        config = get_config()
        llm_enhancer = LLMEnhancer(config)
        
        if llm_enhancer.use_openai:
            print("✅ OpenAI客户端已初始化")
            
            # 测试OpenAI意图识别
            test_input = "我想知道明天上海的天气预报"
            print(f"\n测试输入: '{test_input}'")
            
            try:
                intent_result = await llm_enhancer.recognize_intent(test_input, [])
                print(f"OpenAI意图识别结果: {intent_result}")
            except Exception as e:
                print(f"❌ OpenAI调用失败: {e}")
        else:
            print("❌ OpenAI客户端初始化失败")
    else:
        print("⚠️  未设置OPENAI_API_KEY，使用规则引擎")
        print("如需测试OpenAI功能，请设置环境变量:")
        print("export OPENAI_API_KEY=your_api_key_here")
    
    print()


async def test_tool_registry():
    """测试工具注册表"""
    print("📋 5. 工具注册表测试")
    print("=" * 50)
    
    config = get_config()
    llm_enhancer = LLMEnhancer(config)
    
    # 检查可用工具
    available_tools = llm_enhancer.tool_registry.list_tools()
    print(f"可用工具数量: {len(available_tools)}")
    
    for tool_name in available_tools:
        tool = llm_enhancer.tool_registry.get_tool(tool_name)
        print(f"  📦 {tool_name}: {tool.description}")
    
    # 测试工具执行器
    print(f"\n工具执行器状态: {'✅ 正常' if llm_enhancer.tool_executor else '❌ 未初始化'}")
    
    print()


def show_configuration():
    """显示配置信息"""
    print("⚙️ 6. 配置信息")
    print("=" * 50)
    
    config = get_config()
    
    print(f"LLM增强功能: {'✅ 启用' if config.intelligent_agent.enabled else '❌ 禁用'}")
    print(f"启用的工具: {config.tools.enabled_tools}")
    print(f"Web搜索: {'✅ 启用' if config.tools.web_search_enabled else '❌ 禁用'}")
    print(f"计算器: {'✅ 启用' if config.tools.calculator_enabled else '❌ 禁用'}")
    print(f"日期时间: {'✅ 启用' if config.tools.datetime_enabled else '❌ 禁用'}")
    
    print()


async def main():
    """主测试函数"""
    print("🧪 AudioAgent LLM增强功能测试")
    print("=" * 60)
    print("测试意图识别、工具调用、OpenAI集成等功能")
    print()
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    show_configuration()
    await test_intent_recognition()
    await test_tool_execution()
    await test_full_enhancement_flow()
    await test_openai_integration()
    await test_tool_registry()
    
    print("🎉 所有测试完成！")
    print()
    print("💡 使用建议:")
    print("  - 设置OPENAI_API_KEY以启用OpenAI功能")
    print("  - 在.env中配置启用的工具")
    print("  - 运行AudioAgent时会自动使用LLM增强功能")


if __name__ == "__main__":
    asyncio.run(main())
