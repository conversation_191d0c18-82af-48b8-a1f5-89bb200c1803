#!/usr/bin/env python3
"""
调试AudioInputActor的具体问题
"""

import ray
import logging
import time
from audio_agent.config import get_config
from audio_agent.core.actors import AudioInputActor

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_actor_creation():
    """测试Actor创建"""
    print("=== 测试AudioInputActor创建 ===")
    
    config = get_config()
    
    # 初始化Ray
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    
    try:
        print("创建AudioInputActor...")
        audio_actor = AudioInputActor.remote(config.input_audio)
        print("✅ AudioInputActor创建成功")
        
        print("测试start_recording方法...")
        try:
            result = ray.get(audio_actor.start_recording.remote(), timeout=10)
            print(f"start_recording结果: {result}")
            
            if result:
                print("✅ 录音启动成功")
                
                # 测试获取音频数据
                print("测试获取音频数据...")
                for i in range(5):
                    try:
                        audio_data = ray.get(audio_actor.get_audio_data.remote(), timeout=5)
                        if audio_data:
                            print(f"收到音频数据: {len(audio_data)} 字节")
                        else:
                            print("没有音频数据")
                        time.sleep(1)
                    except Exception as e:
                        print(f"获取音频数据失败: {e}")
                
                # 停止录音
                print("停止录音...")
                ray.get(audio_actor.stop_recording.remote())
                print("✅ 录音已停止")
                
            else:
                print("❌ 录音启动失败")
                
        except ray.exceptions.RayTaskError as e:
            print(f"❌ Ray任务错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
            
    except Exception as e:
        print(f"❌ 创建AudioInputActor失败: {e}")
    finally:
        if ray.is_initialized():
            ray.shutdown()

def test_local_audio_input_class():
    """测试本地AudioInputActor类（不使用Ray）"""
    print("\n=== 测试本地AudioInputActor类 ===")
    
    config = get_config()
    
    try:
        # 直接创建类实例（不使用Ray）
        from audio_agent.core.actors import AudioInputActor
        
        # 创建一个普通的类实例
        class LocalAudioInputActor:
            def __init__(self, config):
                self.config = config
                self.audio = None
                self.stream = None
                self.is_recording = False
                self.record_thread = None
                self.audio_queue = []
                
            def start_recording(self):
                import pyaudio
                import threading
                
                try:
                    self.audio = pyaudio.PyAudio()
                    
                    self.stream = self.audio.open(
                        format=self.config.bit_size,
                        channels=self.config.channels,
                        rate=self.config.sample_rate,
                        input=True,
                        frames_per_buffer=self.config.chunk
                    )
                    
                    self.is_recording = True
                    self.record_thread = threading.Thread(target=self._record_audio)
                    self.record_thread.start()
                    
                    print("✅ 本地录音启动成功")
                    return True
                    
                except Exception as e:
                    print(f"❌ 本地录音启动失败: {e}")
                    return False
            
            def _record_audio(self):
                import numpy as np
                
                audio_count = 0
                while self.is_recording:
                    try:
                        data = self.stream.read(self.config.chunk, exception_on_overflow=False)
                        
                        if len(data) > 0:
                            audio_count += 1
                            self.audio_queue.append(data)
                            
                            # 分析音频
                            audio_int16 = np.frombuffer(data, dtype=np.int16)
                            max_amplitude = np.abs(audio_int16).max()
                            
                            if audio_count % 10 == 0:
                                print(f"录音块 #{audio_count}, 最大幅度: {max_amplitude}")
                            
                            # 限制队列大小
                            if len(self.audio_queue) > 100:
                                self.audio_queue.pop(0)
                                
                    except Exception as e:
                        print(f"录音线程错误: {e}")
                        break
            
            def get_audio_data(self):
                if self.audio_queue:
                    return self.audio_queue.pop(0)
                return None
            
            def stop_recording(self):
                self.is_recording = False
                if self.record_thread:
                    self.record_thread.join()
                if self.stream:
                    self.stream.stop_stream()
                    self.stream.close()
                if self.audio:
                    self.audio.terminate()
                print("✅ 本地录音已停止")
        
        # 测试本地实现
        local_actor = LocalAudioInputActor(config.input_audio)
        
        if local_actor.start_recording():
            print("监控5秒...")
            for i in range(50):
                audio_data = local_actor.get_audio_data()
                if audio_data:
                    print(f"收到音频数据: {len(audio_data)} 字节")
                time.sleep(0.1)
            
            local_actor.stop_recording()
        
    except Exception as e:
        print(f"❌ 本地测试失败: {e}")

if __name__ == "__main__":
    test_actor_creation()
    test_local_audio_input_class()
