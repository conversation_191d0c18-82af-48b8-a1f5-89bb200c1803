"""
Test the event-driven architecture following <PERSON>cengine's official practices.
"""

import pytest
import asyncio
import uuid
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from audio_agent.events import (
    EventType, ServerEvent, LLMEvent, EventHandler, 
    EventProcessor, LLMEnhancer
)
from audio_agent.config import get_config


class TestEventTypes:
    """Test event type definitions."""
    
    def test_event_type_values(self):
        """Test that event types have correct values according to <PERSON><PERSON><PERSON>ine docs."""
        assert EventType.CONNECTION_STARTED == 50
        assert EventType.SESSION_STARTED == 150
        assert EventType.TTS_RESPONSE == 352
        assert EventType.ASR_RESPONSE == 451
        assert EventType.CHAT_RESPONSE == 550
    
    def test_server_event_creation(self):
        """Test ServerEvent creation from WebSocket message."""
        message = {
            "message_type": "SERVER_ACK",
            "event": 352,
            "payload": b"audio_data"
        }

        event = ServerEvent.from_websocket_message(message, "test_session")

        assert event.event_type == EventType.TTS_RESPONSE
        assert event.session_id == "test_session"
        assert event.message_type == "SERVER_ACK"
        assert event.payload == b"audio_data"
    
    def test_llm_event_creation(self):
        """Test LLMEvent creation."""
        event = LLMEvent(
            event_type=EventType.LLM_START,
            timestamp=datetime.now(),
            session_id="test_session",
            event_id=str(uuid.uuid4()),
            data={"user_input": "Hello"},
            user_message="Hello"
        )
        
        assert event.event_type == EventType.LLM_START
        assert event.user_message == "Hello"


class TestEventHandler:
    """Test event handler functionality."""
    
    @pytest.fixture
    def event_handler(self):
        """Create event handler for testing."""
        return EventHandler("test_session")
    
    def test_event_handler_initialization(self, event_handler):
        """Test event handler initialization."""
        assert event_handler.session_id == "test_session"
        assert not event_handler.is_processing
        assert len(event_handler.event_handlers) > 0
    
    def test_register_handler(self, event_handler):
        """Test registering custom event handler."""
        mock_handler = Mock()
        event_handler.register_handler(EventType.CONNECTION_STARTED, mock_handler)

        handlers = event_handler.event_handlers[EventType.CONNECTION_STARTED]
        assert mock_handler in handlers
    
    @pytest.mark.asyncio
    async def test_handle_server_event(self, event_handler):
        """Test handling server events."""
        message = {
            "message_type": "SERVER_ACK",
            "event": 352,
            "payload": b"test_audio"
        }

        event = await event_handler.handle_server_event(message)

        assert event is not None
        assert event.event_type == EventType.TTS_RESPONSE
        assert event.session_id == "test_session"


class TestLLMEnhancer:
    """Test LLM enhancer functionality."""
    
    @pytest.fixture
    def llm_enhancer(self):
        """Create LLM enhancer for testing."""
        config = get_config()
        return LLMEnhancer(config)
    
    def test_llm_enhancer_initialization(self, llm_enhancer):
        """Test LLM enhancer initialization."""
        assert llm_enhancer.available_tools is not None
        assert "get_weather" in llm_enhancer.available_tools
        assert "search_web" in llm_enhancer.available_tools
    
    @pytest.mark.asyncio
    async def test_intent_recognition_weather(self, llm_enhancer):
        """Test intent recognition for weather queries."""
        result = await llm_enhancer.recognize_intent(
            "What's the weather like today?",
            []
        )
        
        assert result["intent"] == "weather_query"
        assert result["requires_tools"] is True
        assert "get_weather" in result["tools"]
        assert result["confidence"] > 0.8
    
    @pytest.mark.asyncio
    async def test_intent_recognition_search(self, llm_enhancer):
        """Test intent recognition for search queries."""
        result = await llm_enhancer.recognize_intent(
            "Search for information about Python programming",
            []
        )
        
        assert result["intent"] == "web_search"
        assert result["requires_tools"] is True
        assert "search_web" in result["tools"]
    
    @pytest.mark.asyncio
    async def test_intent_recognition_general(self, llm_enhancer):
        """Test intent recognition for general conversation."""
        result = await llm_enhancer.recognize_intent(
            "Hello, how are you?",
            []
        )
        
        assert result["intent"] == "general_conversation"
        assert result["requires_tools"] is False
        assert len(result["tools"]) == 0
    
    @pytest.mark.asyncio
    async def test_tool_execution(self, llm_enhancer):
        """Test tool execution."""
        results = await llm_enhancer.execute_tools(
            "What's the weather?",
            ["get_weather"],
            []
        )
        
        assert len(results) == 1
        assert results[0]["tool"] == "get_weather"
        assert results[0]["success"] is True
        assert "temperature" in results[0]["result"]
    
    @pytest.mark.asyncio
    async def test_response_generation(self, llm_enhancer):
        """Test response generation."""
        intent_result = {
            "intent": "general_conversation",
            "confidence": 0.8
        }
        
        response = await llm_enhancer.generate_response(
            "Hello",
            [],
            intent_result
        )
        
        assert isinstance(response, str)
        assert len(response) > 0
    
    @pytest.mark.asyncio
    async def test_enhanced_response_generation(self, llm_enhancer):
        """Test enhanced response generation with tool results."""
        tool_results = [{
            "tool": "get_weather",
            "success": True,
            "result": {
                "location": "New York",
                "temperature": "22°C",
                "condition": "Sunny"
            }
        }]
        
        response = await llm_enhancer.generate_enhanced_response(
            "What's the weather?",
            tool_results,
            []
        )
        
        assert isinstance(response, str)
        assert "22°C" in response
        assert "New York" in response


class TestEventProcessor:
    """Test event processor functionality."""
    
    @pytest.fixture
    def event_processor(self):
        """Create event processor for testing."""
        config = get_config()
        return EventProcessor("test_session", config)
    
    def test_event_processor_initialization(self, event_processor):
        """Test event processor initialization."""
        assert event_processor.session_id == "test_session"
        assert event_processor.event_handler is not None
        assert event_processor.llm_enhancer is not None
        assert not event_processor.is_processing_llm
    
    @pytest.mark.asyncio
    async def test_process_websocket_message(self, event_processor):
        """Test processing WebSocket messages."""
        message = {
            "message_type": "SERVER_ACK",
            "event": 451,  # ASR_RESPONSE
            "results": [
                {
                    "text": "Hello world",
                    "is_interim": False
                }
            ]
        }

        event = await event_processor.process_websocket_message(message)

        assert event is not None
        assert event.event_type == EventType.ASR_RESPONSE


class TestEventIntegration:
    """Test end-to-end event system integration."""
    
    @pytest.mark.asyncio
    async def test_full_event_flow(self):
        """Test complete event flow from ASR to response."""
        config = get_config()
        processor = EventProcessor("test_session", config)
        
        # Simulate ASR response
        asr_message = {
            "message_type": "SERVER_ACK",
            "event": 451,  # ASR_RESPONSE
            "results": [
                {
                    "text": "What's the weather like?",
                    "is_interim": False
                }
            ]
        }

        # Process the message
        event = await processor.process_websocket_message(asr_message)

        assert event is not None
        assert event.event_type == EventType.ASR_RESPONSE
        
        # Verify that the processor can handle the event
        # (In a real test, we'd mock the LLM calls and verify the flow)
    
    @pytest.mark.asyncio
    async def test_event_handler_registration(self):
        """Test that custom handlers can be registered and called."""
        handler = EventHandler("test_session")
        
        # Register a custom handler
        custom_handler_called = False
        
        async def custom_handler(event):
            nonlocal custom_handler_called
            custom_handler_called = True
        
        handler.register_handler(EventType.CONNECTION_STARTED, custom_handler)
        
        # Create and process an event
        event = LLMEvent(
            event_type=EventType.CONNECTION_STARTED,
            timestamp=datetime.now(),
            session_id="test_session",
            event_id=str(uuid.uuid4()),
            data={}
        )
        
        await handler._process_single_event(event)
        
        assert custom_handler_called


if __name__ == "__main__":
    pytest.main([__file__])
