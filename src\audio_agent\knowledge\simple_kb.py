"""
Simple knowledge base implementation.

This module provides a simple in-memory knowledge base with
basic text search capabilities.
"""

import json
import logging
import os
from typing import Dict, List, Optional
from .base import BaseKnowledgeBase, KnowledgeItem

logger = logging.getLogger(__name__)


class SimpleKnowledgeBase(BaseKnowledgeBase):
    """
    Simple in-memory knowledge base with file persistence.
    
    This implementation stores knowledge items in memory and
    provides basic text-based search functionality.
    """
    
    def __init__(self, name: str, storage_path: Optional[str] = None):
        """
        Initialize the simple knowledge base.
        
        Args:
            name: Name of the knowledge base
            storage_path: Optional path to store knowledge data
        """
        super().__init__(name)
        self.storage_path = storage_path
        self.items: Dict[str, KnowledgeItem] = {}
        
        # Load existing data if storage path is provided
        if self.storage_path and os.path.exists(self.storage_path):
            self._load_from_file()
    
    async def add_item(self, item: KnowledgeItem) -> bool:
        """Add a knowledge item to the base."""
        try:
            self.items[item.id] = item
            await self._save_to_file()
            logger.info(f"Added knowledge item: {item.id}")
            return True
        except Exception as e:
            logger.error(f"Error adding knowledge item: {e}")
            return False
    
    async def get_item(self, item_id: str) -> Optional[KnowledgeItem]:
        """Get a knowledge item by ID."""
        return self.items.get(item_id)
    
    async def search(self, query: str, max_results: int = 5) -> List[KnowledgeItem]:
        """
        Search for knowledge items using simple text matching.
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            List of matching knowledge items
        """
        try:
            query_lower = query.lower()
            results = []
            
            for item in self.items.values():
                score = 0
                
                # Search in title (higher weight)
                if query_lower in item.title.lower():
                    score += 3
                
                # Search in content
                if query_lower in item.content.lower():
                    score += 2
                
                # Search in tags
                for tag in item.tags:
                    if query_lower in tag.lower():
                        score += 1
                
                # Search in category
                if query_lower in item.category.lower():
                    score += 1
                
                if score > 0:
                    results.append((item, score))
            
            # Sort by score (descending) and return top results
            results.sort(key=lambda x: x[1], reverse=True)
            return [item for item, score in results[:max_results]]
            
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return []
    
    async def update_item(self, item: KnowledgeItem) -> bool:
        """Update a knowledge item."""
        try:
            if item.id in self.items:
                from datetime import datetime
                item.updated_at = datetime.now()
                self.items[item.id] = item
                await self._save_to_file()
                logger.info(f"Updated knowledge item: {item.id}")
                return True
            else:
                logger.warning(f"Knowledge item not found for update: {item.id}")
                return False
        except Exception as e:
            logger.error(f"Error updating knowledge item: {e}")
            return False
    
    async def delete_item(self, item_id: str) -> bool:
        """Delete a knowledge item."""
        try:
            if item_id in self.items:
                del self.items[item_id]
                await self._save_to_file()
                logger.info(f"Deleted knowledge item: {item_id}")
                return True
            else:
                logger.warning(f"Knowledge item not found for deletion: {item_id}")
                return False
        except Exception as e:
            logger.error(f"Error deleting knowledge item: {e}")
            return False
    
    async def list_items(self, category: Optional[str] = None) -> List[KnowledgeItem]:
        """List all knowledge items."""
        try:
            if category:
                return [item for item in self.items.values() if item.category == category]
            else:
                return list(self.items.values())
        except Exception as e:
            logger.error(f"Error listing knowledge items: {e}")
            return []
    
    async def get_categories(self) -> List[str]:
        """Get all available categories."""
        try:
            categories = set(item.category for item in self.items.values())
            return sorted(list(categories))
        except Exception as e:
            logger.error(f"Error getting categories: {e}")
            return []
    
    async def clear(self) -> bool:
        """Clear all knowledge items."""
        try:
            self.items.clear()
            await self._save_to_file()
            logger.info("Cleared all knowledge items")
            return True
        except Exception as e:
            logger.error(f"Error clearing knowledge base: {e}")
            return False
    
    def _load_from_file(self) -> None:
        """Load knowledge items from file."""
        try:
            with open(self.storage_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            for item_data in data.get('items', []):
                item = KnowledgeItem.from_dict(item_data)
                self.items[item.id] = item
                
            logger.info(f"Loaded {len(self.items)} knowledge items from {self.storage_path}")
            
        except Exception as e:
            logger.error(f"Error loading knowledge base from file: {e}")
    
    async def _save_to_file(self) -> None:
        """Save knowledge items to file."""
        if not self.storage_path:
            return
            
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
            
            data = {
                'name': self.name,
                'items': [item.to_dict() for item in self.items.values()]
            }
            
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.debug(f"Saved {len(self.items)} knowledge items to {self.storage_path}")
            
        except Exception as e:
            logger.error(f"Error saving knowledge base to file: {e}")
    
    def get_stats(self) -> Dict[str, int]:
        """Get knowledge base statistics."""
        categories = {}
        for item in self.items.values():
            categories[item.category] = categories.get(item.category, 0) + 1
        
        return {
            'total_items': len(self.items),
            'categories': len(categories),
            'category_breakdown': categories
        }
