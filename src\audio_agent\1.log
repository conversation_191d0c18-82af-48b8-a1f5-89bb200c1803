aved: '你好现在是什么'
2025-07-26 23:42:39,469 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.9392253160476685, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.172021, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么'}], 'end_time': 10.172021, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什 么'}]}}
2025-07-26 23:42:39,470 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:39,470 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:39,470 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:39,470 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:39,471 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:39,471 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么', interim: True
2025-07-26 23:42:39,471 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么'
2025-07-26 23:42:39,475 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.9392253160476685, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.242023, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么'}], 'end_time': 10.242023, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什 么'}]}}
2025-07-26 23:42:39,476 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:39,476 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:39,476 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:39,476 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:39,477 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:39,477 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么', interim: True
2025-07-26 23:42:39,477 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么'
2025-07-26 23:42:39,665 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.9392253160476685, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.332025, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么'}], 'end_time': 10.332025, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什 么'}]}}
2025-07-26 23:42:39,665 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:39,665 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:39,665 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:39,665 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:39,665 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:39,665 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么', interim: True
2025-07-26 23:42:39,665 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么'
2025-07-26 23:42:39,671 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.9392253160476685, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.402026, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么'}], 'end_time': 10.402026, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什 么'}]}}
2025-07-26 23:42:39,671 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:39,671 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:39,671 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:39,671 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:39,671 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:39,671 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么', interim: True
2025-07-26 23:42:39,671 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么'
2025-07-26 23:42:39,884 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.9222171306610107, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.492028, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 10.492028, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好 现在是什么时间'}]}}
2025-07-26 23:42:39,884 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:39,884 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:39,884 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:39,884 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:39,884 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:39,884 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:39,884 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:39,884 - audio_agent.events.event_processor - INFO - 🔧 Early tool detection: '你好现在是什么时间' likely needs tools
2025-07-26 23:42:39,886 - audio_agent.events.event_processor - INFO - ✅ Early placeholder ChatTTSText sent during ASR interim
2025-07-26 23:42:39,888 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.56203, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 10.56203, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现 在是什么时间'}]}}
2025-07-26 23:42:39,888 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:39,888 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:39,888 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:39,888 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:39,888 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:39,890 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:39,890 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:39,897 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'prefetch': True, 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 39.994898498534326}, 'results': [{'alternatives': [{'end_time': 10.652032, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好，现在是什么时间？'}], 'end_time': 10.652032, 'index': 0, 'is_interim': True, 'is_soft_finished': True, 'is_vad_timeout': False, 'soft_vad_type': 999, 'start_time': 8.772, 'text': '你好，现在是什么时间？'}]}}
2025-07-26 23:42:39,897 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:39,897 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:39,897 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:39,897 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:39,897 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:39,897 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好，现 在是什么时间？', interim: True
2025-07-26 23:42:39,897 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好，现在是什么时间？'
2025-07-26 23:42:39,898 - audio_agent.events.event_processor - INFO - 🚨 AGGRESSIVE: Sending placeholder during ASR interim for: '你好，现在是什么时间？'
2025-07-26 23:42:39,899 - audio_agent.events.event_processor - INFO - 🚨 AGGRESSIVE placeholder sent during ASR interim
2025-07-26 23:42:40,001 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 109.9974543457023}, 'results': [{'alternatives': [{'end_time': 10.722034, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 10.722034, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,001 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,001 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,001 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,001 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,001 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,001 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,001 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,006 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 199.99856060790933}, 'results': [{'alternatives': [{'end_time': 10.812036, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 10.812036, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,006 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,006 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,006 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,006 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,006 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,006 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,008 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,013 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 270.0001627807609}, 'results': [{'alternatives': [{'end_time': 10.882037, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 10.882037, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,014 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,014 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,014 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,014 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,014 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,014 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,014 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,087 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 360.0022227172843}, 'results': [{'alternatives': [{'end_time': 10.972039, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 10.972039, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,087 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,087 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,087 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,087 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,087 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,087 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,087 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,093 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 430.0038248901359}, 'results': [{'alternatives': [{'end_time': 11.042041, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 11.042041, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,093 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,093 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,093 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,093 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,093 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,093 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,093 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,297 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 520.0058848266593}, 'results': [{'alternatives': [{'end_time': 11.132043, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 11.132043, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,297 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,297 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,297 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,297 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,297 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,297 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,297 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,302 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 590.0065333251945}, 'results': [{'alternatives': [{'end_time': 11.202044, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 11.202044, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,302 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,302 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,302 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,302 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,302 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,302 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,302 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,310 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 680.0095469360343}, 'results': [{'alternatives': [{'end_time': 11.292047, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 11.292047, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,310 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,310 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,310 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,310 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,311 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,311 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,311 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,317 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'interrupt_score': 0.3440989553928375, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 750.0111491088859}, 'results': [{'alternatives': [{'end_time': 11.362048, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好现在是什么时间'}], 'end_time': 11.362048, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好现在是什么时间'}]}}
2025-07-26 23:42:40,317 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,317 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,318 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,318 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,318 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,318 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好现在 是什么时间', interim: True
2025-07-26 23:42:40,318 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好现在是什么时间'
2025-07-26 23:42:40,566 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.18291141092777252, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '你好，现在是什么时间？', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 829.9982090454101}, 'results': [{'alternatives': [{'end_time': 11.45205, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.772, 'text': '你好，现在是什么时间？'}], 'end_time': 11.45205, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 8.772, 'text': '你好，现在是 什么时间？'}]}}
2025-07-26 23:42:40,566 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,566 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,566 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:42:40,566 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:42:40,566 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:42:40,566 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好，现 在是什么时间？', interim: False
2025-07-26 23:42:40,566 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '你好，现在是什么时间？'
2025-07-26 23:42:40,867 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 376, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 10623}}
2025-07-26 23:42:40,867 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:40,867 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:40,867 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-26 23:42:40,867 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-26 23:42:40,867 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '你好，现在是什么时间？'
2025-07-26 23:42:40,867 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '你好，现在是什么时间？'
2025-07-26 23:42:40,867 - audio_agent.events.event_processor - INFO - 🔧 Tool calling activated - will filter server auto-responses
2025-07-26 23:42:40,881 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
(DialogClientActor pid=24684) OPENAI_API_KEY not found, using rule-based fallback
2025-07-26 23:42:41,181 - audio_agent.events.llm_enhancer - WARNING - OPENAI_API_KEY not found, using rule-based fallback
2025-07-26 23:42:41,188 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'datetime_query', 'confidence': 0.9, 'requires_tools': True, 'tools': ['datetime'], 'parameters': {}}
2025-07-26 23:42:41,188 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'datetime_query', 'confidence': 0.9, 'requires_tools': True, 'tools': ['datetime'], 'parameters': {}}
2025-07-26 23:42:41,188 - audio_agent.events.event_processor - INFO - 🔧 Tools needed, sending placeholder first: ['datetime']
2025-07-26 23:42:41,188 - audio_agent.events.event_processor - INFO - ✅ Immediate placeholder already sent at ASR end, proceeding with tool execution
2025-07-26 23:42:41,188 - audio_agent.events.event_processor - INFO - 🔧 Executing tools: ['datetime']
2025-07-26 23:42:41,188 - audio_agent.tools.executor - INFO - Executing tool: datetime with parameters: {'operation': 'current_time'}
2025-07-26 23:42:41,188 - audio_agent.tools.builtin.datetime_tool - INFO - DateTime operation: current_time
2025-07-26 23:42:41,188 - audio_agent.tools.executor - INFO - Tool datetime executed successfully
2025-07-26 23:42:41,188 - audio_agent.events.llm_enhancer - INFO - Tool datetime executed successfully
2025-07-26 23:42:41,191 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da_2_1', 'tts_type': 'default', 'v3_loundness_params': ''}}        
2025-07-26 23:42:41,191 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,191 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,211 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-26 23:42:41,211 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:41,211 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:41,211 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-26 23:42:41,213 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-26 23:42:41,227 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:42:41,231 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-26 23:42:41,231 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:41,231 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:41,231 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-26 23:42:41,232 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38396 bytes)
2025-07-26 23:42:41,250 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': ''}}
2025-07-26 23:42:41,250 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,250 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,266 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '现在'}}
2025-07-26 23:42:41,267 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,267 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,284 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '是'}}
2025-07-26 23:42:41,284 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,284 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,302 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '下午'}}
2025-07-26 23:42:41,302 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,302 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,319 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '，'}}
2025-07-26 23:42:41,319 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,319 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,336 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '具体'}}
2025-07-26 23:42:41,336 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,337 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,354 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '时间'}}
2025-07-26 23:42:41,354 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,354 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,375 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '是'}}
2025-07-26 23:42:41,375 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,375 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
(DialogClientActor pid=24684) 🚫 FILTERING server auto-response (tts_type=default): '' - audio will be DISCARDED
2025-07-26 23:42:41,526 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:42:41,680 - audio_agent.events.event_processor - INFO - 🔧 Tool result ready to send to server:
2025-07-26 23:42:41,680 - audio_agent.events.event_processor - INFO -    User input: 你好，现在是什么时间？
2025-07-26 23:42:41,680 - audio_agent.events.event_processor - INFO -    Tool results: 1 tools executed
2025-07-26 23:42:41,680 - audio_agent.events.event_processor - INFO -    AI response: 现在是 晚上11点42分
2025-07-26 23:42:41,683 - audio_agent.events.event_processor - INFO - ✅ Tool result sent to server successfully using streaming ChatTTSText
2025-07-26 23:42:41,684 - audio_agent.events.event_processor - INFO - 🔧 Tool processing complete, but keeping tool_calling_active=True to filter server responses
2025-07-26 23:42:41,684 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-26 23:42:41,684 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: b7fb1611-4cc7-4ae6-937e-4c306af963da_2_1
2025-07-26 23:42:41,684 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-26 23:42:41,684 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-26 23:42:41,684 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: True
2025-07-26 23:42:41,684 - audio_agent.events.event_handler - WARNING - 🚫 FILTERING server auto-response (tts_type=default): '' - audio will be DISCARDED
2025-07-26 23:42:41,684 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-26 23:42:41,685 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-26 23:42:41,685 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-26 23:42:41,685 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,685 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,685 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,685 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,685 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,685 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,686 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,686 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,709 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38156bytes>', 'payload_msg': '<binary_data:38156bytes>'}
2025-07-26 23:42:41,709 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:41,709 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:41,709 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38156 bytes
2025-07-26 23:42:41,710 - audio_agent.events.event_handler - INFO - TTS audio response: 38156 bytes
2025-07-26 23:42:41,710 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38156 bytes)
2025-07-26 23:42:41,728 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '，嗯'}}
2025-07-26 23:42:41,728 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,728 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,729 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,735 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:39000bytes>', 'payload_msg': '<binary_data:39000bytes>'}
2025-07-26 23:42:41,735 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:41,735 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:41,735 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 39000 bytes
2025-07-26 23:42:41,736 - audio_agent.events.event_handler - INFO - TTS audio response: 39000 bytes
2025-07-26 23:42:41,737 - audio_agent.core.session_manager - INFO - Audio response queued for playback (39000 bytes)
2025-07-26 23:42:41,755 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '，让我看一下'}}
2025-07-26 23:42:41,755 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,755 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,755 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,761 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '啊'}}
2025-07-26 23:42:41,762 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,762 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,762 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,768 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '，'}}
2025-07-26 23:42:41,768 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,769 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,769 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,959 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:37948bytes>', 'payload_msg': '<binary_data:37948bytes>'}
2025-07-26 23:42:41,959 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:41,959 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:41,960 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37948 bytes
2025-07-26 23:42:41,960 - audio_agent.events.event_handler - INFO - TTS audio response: 37948 bytes
2025-07-26 23:42:41,961 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37948 bytes)
2025-07-26 23:42:41,979 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '是'}}
2025-07-26 23:42:41,979 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,979 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,979 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,986 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '十五'}}
2025-07-26 23:42:41,986 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:41,986 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:41,986 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:41,993 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38204bytes>', 'payload_msg': '<binary_data:38204bytes>'}
2025-07-26 23:42:41,993 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:41,993 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:41,993 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38204 bytes
2025-07-26 23:42:41,993 - audio_agent.events.event_handler - INFO - TTS audio response: 38204 bytes
2025-07-26 23:42:41,995 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38204 bytes)
2025-07-26 23:42:42,012 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '点'}}
2025-07-26 23:42:42,012 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:42,013 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:42,013 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:42,018 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '三十二'}}
2025-07-26 23:42:42,018 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:42,018 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:42,018 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:42,385 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38648bytes>', 'payload_msg': '<binary_data:38648bytes>'}
2025-07-26 23:42:42,385 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:42,385 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:42,385 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38648 bytes
2025-07-26 23:42:42,386 - audio_agent.events.event_handler - INFO - TTS audio response: 38648 bytes
2025-07-26 23:42:42,386 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38648 bytes)
2025-07-26 23:42:42,405 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '分'}}
2025-07-26 23:42:42,405 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:42,405 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:42,405 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:42,412 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'content': '。'}}
2025-07-26 23:42:42,412 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:42,412 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:42,412 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:42:42,419 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:37932bytes>', 'payload_msg': '<binary_data:37932bytes>'}
2025-07-26 23:42:42,419 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:42,419 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:42,419 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37932 bytes
2025-07-26 23:42:42,419 - audio_agent.events.event_handler - INFO - TTS audio response: 37932 bytes
2025-07-26 23:42:42,420 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37932 bytes)
2025-07-26 23:42:42,581 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38152bytes>', 'payload_msg': '<binary_data:38152bytes>'}
2025-07-26 23:42:42,581 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:42,581 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:42,582 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38152 bytes
2025-07-26 23:42:42,582 - audio_agent.events.event_handler - INFO - TTS audio response: 38152 bytes
2025-07-26 23:42:42,583 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38152 bytes)
2025-07-26 23:42:42,675 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38812bytes>', 'payload_msg': '<binary_data:38812bytes>'}
2025-07-26 23:42:42,675 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:42,675 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:42,675 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38812 bytes
2025-07-26 23:42:42,676 - audio_agent.events.event_handler - INFO - TTS audio response: 38812 bytes
2025-07-26 23:42:42,677 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38812 bytes)
2025-07-26 23:42:42,947 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-26 23:42:42,947 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:42,947 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:42,948 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-26 23:42:42,948 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-26 23:42:42,949 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38396 bytes)
2025-07-26 23:42:43,265 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38952bytes>', 'payload_msg': '<binary_data:38952bytes>'}
2025-07-26 23:42:43,265 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:43,265 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:43,265 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38952 bytes
2025-07-26 23:42:43,266 - audio_agent.events.event_handler - INFO - TTS audio response: 38952 bytes
2025-07-26 23:42:43,267 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38952 bytes)
2025-07-26 23:42:43,284 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38440bytes>', 'payload_msg': '<binary_data:38440bytes>'}
2025-07-26 23:42:43,285 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:43,285 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:43,285 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38440 bytes
2025-07-26 23:42:43,286 - audio_agent.events.event_handler - INFO - TTS audio response: 38440 bytes
2025-07-26 23:42:43,286 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38440 bytes)
2025-07-26 23:42:43,851 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38856bytes>', 'payload_msg': '<binary_data:38856bytes>'}
2025-07-26 23:42:43,852 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:43,852 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:43,852 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38856 bytes
2025-07-26 23:42:43,852 - audio_agent.events.event_handler - INFO - TTS audio response: 38856 bytes
2025-07-26 23:42:43,853 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38856 bytes)
2025-07-26 23:42:43,871 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38828bytes>', 'payload_msg': '<binary_data:38828bytes>'}
2025-07-26 23:42:43,871 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:43,871 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:43,872 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38828 bytes
2025-07-26 23:42:43,872 - audio_agent.events.event_handler - INFO - TTS audio response: 38828 bytes
2025-07-26 23:42:43,874 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38828 bytes)
2025-07-26 23:42:43,891 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38012bytes>', 'payload_msg': '<binary_data:38012bytes>'}
2025-07-26 23:42:43,891 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:43,891 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:43,891 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38012 bytes
2025-07-26 23:42:43,892 - audio_agent.events.event_handler - INFO - TTS audio response: 38012 bytes
2025-07-26 23:42:43,893 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38012 bytes)
2025-07-26 23:42:43,911 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38380bytes>', 'payload_msg': '<binary_data:38380bytes>'}
2025-07-26 23:42:43,911 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:43,911 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:43,911 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38380 bytes
2025-07-26 23:42:43,911 - audio_agent.events.event_handler - INFO - TTS audio response: 38380 bytes
2025-07-26 23:42:43,912 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38380 bytes)
2025-07-26 23:42:43,929 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:10200bytes>', 'payload_msg': '<binary_data:10200bytes>'}
2025-07-26 23:42:43,930 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:43,930 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:43,930 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 10200 bytes
2025-07-26 23:42:43,930 - audio_agent.events.event_handler - INFO - TTS audio response: 10200 bytes
2025-07-26 23:42:43,931 - audio_agent.core.session_manager - INFO - Audio response queued for playback (10200 bytes)
2025-07-26 23:42:43,948 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {}}
2025-07-26 23:42:43,948 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:43,949 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:43,949 - audio_agent.events.event_handler - INFO - TTS sentence ended       
2025-07-26 23:42:43,954 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'enable_v3_loudness_balance': True, 'gta': True, 'model_type': 'v3', 'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":45,"flushed":true,"markdown":{"prev_parsed_text":"现在是晚上11点42分\\n","prev_t...<truncated:509chars>', 'text': '现在是晚上11点42分。', 'tts_task_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da_3_0', 'tts_type': 'chat_tts_text', 'v3_loundness_params': ''}}
2025-07-26 23:42:43,954 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:43,954 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:43,955 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-26 23:42:43,955 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: chat_tts_text, text: '现在是晚上11点42分。', task_id: b7fb1611-4cc7-4ae6-937e-4c306af963da_3_0    
2025-07-26 23:42:43,955 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-26 23:42:43,955 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-26 23:42:43,955 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: True
2025-07-26 23:42:43,955 - audio_agent.events.event_handler - INFO - ✅ ALLOWING tool result TTS (tts_type=chat_tts_text): '现在是晚上11点42分。' (task_id: b7fb1611-4cc7-4ae6-937e-4c306af963da_3_0)
2025-07-26 23:42:43,955 - audio_agent.events.event_handler - INFO - TTS sentence start - type: chat_tts_text, text: '现在是晚上11点42分。'
2025-07-26 23:42:44,252 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-26 23:42:44,253 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:44,253 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:44,253 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-26 23:42:44,253 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-26 23:42:44,255 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-26 23:42:44,412 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:37960bytes>', 'payload_msg': '<binary_data:37960bytes>'}
2025-07-26 23:42:44,412 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:44,412 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:44,413 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37960 bytes
2025-07-26 23:42:44,413 - audio_agent.events.event_handler - INFO - TTS audio response: 37960 bytes
2025-07-26 23:42:44,414 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37960 bytes)
2025-07-26 23:42:44,847 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38732bytes>', 'payload_msg': '<binary_data:38732bytes>'}
2025-07-26 23:42:44,848 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:44,848 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:44,848 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38732 bytes
2025-07-26 23:42:44,848 - audio_agent.events.event_handler - INFO - TTS audio response: 38732 bytes
2025-07-26 23:42:44,848 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38732 bytes)
2025-07-26 23:42:44,866 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38696bytes>', 'payload_msg': '<binary_data:38696bytes>'}
2025-07-26 23:42:44,866 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:44,866 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:44,866 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38696 bytes
2025-07-26 23:42:44,866 - audio_agent.events.event_handler - INFO - TTS audio response: 38696 bytes
2025-07-26 23:42:44,867 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38696 bytes)
2025-07-26 23:42:44,885 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38428bytes>', 'payload_msg': '<binary_data:38428bytes>'}
2025-07-26 23:42:44,885 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:44,886 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:44,886 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38428 bytes
2025-07-26 23:42:44,887 - audio_agent.events.event_handler - INFO - TTS audio response: 38428 bytes
2025-07-26 23:42:44,888 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38428 bytes)
2025-07-26 23:42:44,905 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-26 23:42:44,906 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:44,906 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:44,906 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-26 23:42:44,907 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-26 23:42:44,908 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38396 bytes)
2025-07-26 23:42:44,926 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': '<binary_data:30972bytes>', 'payload_msg': '<binary_data:30972bytes>'}
2025-07-26 23:42:44,927 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:42:44,927 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:42:44,927 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 30972 bytes
2025-07-26 23:42:44,927 - audio_agent.events.event_handler - INFO - TTS audio response: 30972 bytes
2025-07-26 23:42:44,928 - audio_agent.core.session_manager - INFO - Audio response queued for playback (30972 bytes)
2025-07-26 23:42:44,946 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":45,"flushed":true,"markdown":{"prev_parsed_text":"现在是晚上11点42分\\n","prev_t...<truncated:509chars>', 'speech_alignment_result': '', 'text': '现在是晚上11点42分。'}}
2025-07-26 23:42:44,946 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:44,948 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:44,948 - audio_agent.events.event_handler - INFO - TTS sentence ended       
2025-07-26 23:42:44,955 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': 'b7fb1611-4cc7-4ae6-937e-4c306af963da', 'payload': {'no_content': False}}
2025-07-26 23:42:44,955 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:42:44,955 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:42:44,955 - audio_agent.events.event_handler - INFO - TTS synthesis ended      
2025-07-26 23:42:44,955 - audio_agent.events.event_handler - INFO - 🔧 TTS synthesis ended, resetting tool calling state
2025-07-26 23:42:50,399 - audio_agent.core.session_manager - INFO - AI finished speaking