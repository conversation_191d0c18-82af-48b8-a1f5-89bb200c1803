2025-07-26 23:31:08,146 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.8234209418296814, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 8.252014, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在'}], 'end_time': 8.252014, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在'}]}}
2025-07-26 23:31:08,146 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,146 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,146 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,146 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,146 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,146 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在', interim: True
2025-07-26 23:31:08,146 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在'
2025-07-26 23:31:08,231 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.932508111000061, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 8.352016, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么'}], 'end_time': 8.352016, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么'}]}}
2025-07-26 23:31:08,231 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,231 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,231 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,231 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,231 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,231 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么', interim: True
2025-07-26 23:31:08,231 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么'
2025-07-26 23:31:08,487 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.932508111000061, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 8.412018, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么'}], 'end_time': 8.412018, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么'}]}}
2025-07-26 23:31:08,487 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,487 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,487 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,487 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,487 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,487 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么', interim: True
2025-07-26 23:31:08,487 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么'
2025-07-26 23:31:08,493 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.932508111000061, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 8.51202, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么'}], 'end_time': 8.51202, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么'}]}}
2025-07-26 23:31:08,493 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,493 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,493 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,493 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,493 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,493 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么', interim: True
2025-07-26 23:31:08,493 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么'
2025-07-26 23:31:08,843 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.932508111000061, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 8.572021, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么'}], 'end_time': 8.572021, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么'}]}}
2025-07-26 23:31:08,843 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,843 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,843 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,843 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,843 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,843 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么', interim: True
2025-07-26 23:31:08,843 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么'
2025-07-26 23:31:08,848 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 8.672024, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 8.672024, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:08,848 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,848 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,848 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,848 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,848 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,848 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:08,848 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:08,855 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'prefetch': True, 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 8.732025, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间？'}], 'end_time': 8.732025, 'index': 0, 'is_interim': True, 'is_soft_finished': True, 'is_vad_timeout': False, 'soft_vad_type': 999, 'start_time': 7.112, 'text': '现在是什么时间？'}]}}
2025-07-26 23:31:08,855 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,855 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,855 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,855 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,856 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,856 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间？', interim: True
2025-07-26 23:31:08,856 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间？'
2025-07-26 23:31:08,861 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 90.00043530273416}, 'results': [{'alternatives': [{'end_time': 8.832027, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 8.832027, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:08,861 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,861 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,861 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,861 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,861 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,861 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:08,861 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:08,867 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 150.0018085937498}, 'results': [{'alternatives': [{'end_time': 8.892029, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 8.892029, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:08,867 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,867 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,868 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,868 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,868 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,868 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:08,868 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:08,873 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 239.99909741210956}, 'results': [{'alternatives': [{'end_time': 8.992031, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 8.992031, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:08,873 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,873 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,873 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,873 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,873 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,873 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:08,875 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:08,878 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 300.0004707031252}, 'results': [{'alternatives': [{'end_time': 9.052032, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.052032, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:08,879 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,879 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,879 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,879 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,880 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,880 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:08,880 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:08,923 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 390.0007595214845}, 'results': [{'alternatives': [{'end_time': 9.152035, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.152035, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:08,923 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:08,923 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:08,923 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:08,923 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:08,923 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:08,923 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:08,923 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,250 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 450.0021328125001}, 'results': [{'alternatives': [{'end_time': 9.212036, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.212036, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,250 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,250 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,250 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,250 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,250 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,250 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,250 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,255 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 550.0044216308595}, 'results': [{'alternatives': [{'end_time': 9.312038, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.312038, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,255 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,255 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,255 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,255 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,255 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,255 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,255 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,406 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 610.0057949218751}, 'results': [{'alternatives': [{'end_time': 9.37204, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.37204, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,406 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,406 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,406 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,406 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,406 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,406 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,406 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,411 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 710.0080837402345}, 'results': [{'alternatives': [{'end_time': 9.472042, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.472042, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,411 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,411 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,411 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,411 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,411 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,411 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,411 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,418 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 770.0094570312501}, 'results': [{'alternatives': [{'end_time': 9.532043, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.532043, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,418 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,418 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,418 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,419 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,419 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,420 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,420 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,424 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 870.0117458496095}, 'results': [{'alternatives': [{'end_time': 9.632046, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.632046, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,424 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,424 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,425 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,425 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,425 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,425 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,425 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,430 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 919.9981191406259}, 'results': [{'alternatives': [{'end_time': 9.692047, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.692047, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,430 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,430 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,430 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,431 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,431 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,431 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,431 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,436 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1020.0004079589853}, 'results': [{'alternatives': [{'end_time': 9.792049, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.792049, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,436 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,436 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,436 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,436 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,436 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,436 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,436 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,595 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'interrupt_score': 0.9214032292366028, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1080.001781250001}, 'results': [{'alternatives': [{'end_time': 9.852051, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间'}], 'end_time': 9.852051, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间'}]}}
2025-07-26 23:31:09,595 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,595 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,595 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,595 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,595 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,595 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间', interim: True
2025-07-26 23:31:09,595 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '现在是什么时间'
2025-07-26 23:31:09,749 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.06951010227203369, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '现在是什么时间？', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 1180.0040700683603}, 'results': [{'alternatives': [{'end_time': 9.952053, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 7.112, 'text': '现在是什么时间？'}], 'end_time': 9.952053, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 7.112, 'text': '现在是什么时间？'}]}}       
2025-07-26 23:31:09,750 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,750 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,750 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:31:09,750 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:31:09,750 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:31:09,750 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '现在是什 么时间？', interim: False
2025-07-26 23:31:09,750 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '现在是什么时间？'
2025-07-26 23:31:09,756 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 964, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 8772}}
2025-07-26 23:31:09,756 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,756 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:09,756 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-26 23:31:09,756 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-26 23:31:09,756 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '现在是什么时间？'
2025-07-26 23:31:09,757 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '现在是什么时间？'
2025-07-26 23:31:09,757 - audio_agent.events.event_processor - INFO - 🔧 Tool calling activated - will filter server auto-responses
2025-07-26 23:31:09,760 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
2025-07-26 23:31:09,760 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'datetime_query', 'confidence': 0.9, 'requires_tools': True, 'tools': ['datetime'], 'parameters': {}}
2025-07-26 23:31:09,760 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'datetime_query', 'confidence': 0.9, 'requires_tools': True, 'tools': ['datetime'], 'parameters': {}}
2025-07-26 23:31:09,760 - audio_agent.events.event_processor - INFO - 🔧 Tools needed, sending placeholder first: ['datetime']
2025-07-26 23:31:09,760 - audio_agent.events.event_processor - INFO - ✅ Immediate placeholder already sent at ASR end, proceeding with tool execution
2025-07-26 23:31:09,760 - audio_agent.events.event_processor - INFO - 🔧 Executing tools: ['datetime']
2025-07-26 23:31:09,760 - audio_agent.tools.executor - INFO - Executing tool: datetime with parameters: {'operation': 'current_time'}
2025-07-26 23:31:09,760 - audio_agent.tools.builtin.datetime_tool - INFO - DateTime operation: current_time
2025-07-26 23:31:09,761 - audio_agent.tools.executor - INFO - Tool datetime executed successfully
2025-07-26 23:31:09,761 - audio_agent.events.llm_enhancer - INFO - Tool datetime executed successfully
2025-07-26 23:31:09,764 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': '31db3560-1e0c-4288-985c-e9491b84bbc9_4_1', 'tts_type': 'default', 'v3_loundness_params': ''}}        
2025-07-26 23:31:09,764 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:09,764 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:10,249 - audio_agent.events.event_processor - INFO - 🔧 Tool result ready to send to server:
2025-07-26 23:31:10,249 - audio_agent.events.event_processor - INFO -    User input: 现在是什么时间？
2025-07-26 23:31:10,249 - audio_agent.events.event_processor - INFO -    Tool results: 1 tools executed
2025-07-26 23:31:10,249 - audio_agent.events.event_processor - INFO -    AI response: 现在是 晚上11点31分
2025-07-26 23:31:10,256 - audio_agent.events.event_processor - INFO - ✅ Tool result sent to server successfully using streaming ChatTTSText
2025-07-26 23:31:10,256 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-26 23:31:10,256 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: 31db3560-1e0c-4288-985c-e9491b84bbc9_4_1
2025-07-26 23:31:10,256 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-26 23:31:10,806 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-26 23:31:10,806 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:10,806 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:10,808 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-26 23:31:10,808 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-26 23:31:10,808 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-26 23:31:10,823 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:31:10,958 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38168bytes>', 'payload_msg': '<binary_data:38168bytes>'}
2025-07-26 23:31:10,959 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:10,959 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:10,959 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38168 bytes
2025-07-26 23:31:10,959 - audio_agent.events.event_handler - INFO - TTS audio response: 38168 bytes
2025-07-26 23:31:10,961 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38168 bytes)
2025-07-26 23:31:11,114 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:31:11,571 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38636bytes>', 'payload_msg': '<binary_data:38636bytes>'}
2025-07-26 23:31:11,571 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:11,571 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:11,571 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38636 bytes
2025-07-26 23:31:11,571 - audio_agent.events.event_handler - INFO - TTS audio response: 38636 bytes
2025-07-26 23:31:11,572 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38636 bytes)
2025-07-26 23:31:11,586 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:31:11,981 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38428bytes>', 'payload_msg': '<binary_data:38428bytes>'}
2025-07-26 23:31:11,981 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:11,981 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:11,981 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38428 bytes
2025-07-26 23:31:11,982 - audio_agent.events.event_handler - INFO - TTS audio response: 38428 bytes
2025-07-26 23:31:11,984 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38428 bytes)
2025-07-26 23:31:11,999 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:31:12,033 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:37804bytes>', 'payload_msg': '<binary_data:37804bytes>'}
2025-07-26 23:31:12,033 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:12,033 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:12,033 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37804 bytes
2025-07-26 23:31:12,034 - audio_agent.events.event_handler - INFO - TTS audio response: 37804 bytes
2025-07-26 23:31:12,035 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37804 bytes)
2025-07-26 23:31:12,052 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': ''}}
2025-07-26 23:31:12,052 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,052 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,053 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,059 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '现在'}}
2025-07-26 23:31:12,060 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,060 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,060 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,066 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '是'}}
2025-07-26 23:31:12,066 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,066 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,067 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,073 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '下午'}}
2025-07-26 23:31:12,073 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,073 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,073 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,079 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '，'}}
2025-07-26 23:31:12,079 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,080 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,080 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,086 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '嗯'}}
2025-07-26 23:31:12,086 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,086 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,086 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,092 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '，快到傍晚了'}}
2025-07-26 23:31:12,092 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,092 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,093 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,099 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '吧'}}
2025-07-26 23:31:12,099 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,099 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,100 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,106 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '，'}}
2025-07-26 23:31:12,106 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,106 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,106 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,114 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '你'}}
2025-07-26 23:31:12,114 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,114 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,114 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,120 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '那边'}}
2025-07-26 23:31:12,120 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,120 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,120 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,126 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '天气'}}
2025-07-26 23:31:12,126 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,126 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,126 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,132 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '怎么样'}}
2025-07-26 23:31:12,132 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,132 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,133 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,140 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '啊'}}
2025-07-26 23:31:12,140 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,140 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,140 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,334 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:31:12,363 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38392bytes>', 'payload_msg': '<binary_data:38392bytes>'}
2025-07-26 23:31:12,364 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:12,364 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:12,364 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38392 bytes
2025-07-26 23:31:12,364 - audio_agent.events.event_handler - INFO - TTS audio response: 38392 bytes
2025-07-26 23:31:12,365 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38392 bytes)
2025-07-26 23:31:12,382 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'content': '？'}}
2025-07-26 23:31:12,382 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:12,382 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:12,382 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:31:12,571 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-26 23:31:12,572 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:12,572 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:12,572 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-26 23:31:12,572 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-26 23:31:12,573 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38396 bytes)
2025-07-26 23:31:12,911 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38716bytes>', 'payload_msg': '<binary_data:38716bytes>'}
2025-07-26 23:31:12,911 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:12,911 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:12,912 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38716 bytes
2025-07-26 23:31:12,912 - audio_agent.events.event_handler - INFO - TTS audio response: 38716 bytes
2025-07-26 23:31:12,913 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38716 bytes)
2025-07-26 23:31:13,240 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38924bytes>', 'payload_msg': '<binary_data:38924bytes>'}
2025-07-26 23:31:13,240 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:13,240 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:13,240 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38924 bytes
2025-07-26 23:31:13,241 - audio_agent.events.event_handler - INFO - TTS audio response: 38924 bytes
2025-07-26 23:31:13,241 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38924 bytes)
2025-07-26 23:31:13,833 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38668bytes>', 'payload_msg': '<binary_data:38668bytes>'}
2025-07-26 23:31:13,833 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:13,833 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:13,834 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38668 bytes
2025-07-26 23:31:13,834 - audio_agent.events.event_handler - INFO - TTS audio response: 38668 bytes
2025-07-26 23:31:13,835 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38668 bytes)
2025-07-26 23:31:13,852 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38040bytes>', 'payload_msg': '<binary_data:38040bytes>'}
2025-07-26 23:31:13,852 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:13,852 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:13,853 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38040 bytes
2025-07-26 23:31:13,853 - audio_agent.events.event_handler - INFO - TTS audio response: 38040 bytes
2025-07-26 23:31:13,854 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38040 bytes)
2025-07-26 23:31:14,013 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:22104bytes>', 'payload_msg': '<binary_data:22104bytes>'}
2025-07-26 23:31:14,013 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:14,013 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:14,013 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 22104 bytes
2025-07-26 23:31:14,013 - audio_agent.events.event_handler - INFO - TTS audio response: 22104 bytes
2025-07-26 23:31:14,014 - audio_agent.core.session_manager - INFO - Audio response queued for playback (22104 bytes)
2025-07-26 23:31:14,032 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {}}
2025-07-26 23:31:14,032 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:14,032 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:14,032 - audio_agent.events.event_handler - INFO - TTS sentence ended       
2025-07-26 23:31:14,038 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'enable_v3_loudness_balance': True, 'gta': True, 'model_type': 'v3', 'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":45,"flushed":true,"markdown":{"prev_parsed_text":"现在是晚上11点31分\\n","prev_t...<truncated:509chars>', 'text': '现在是晚上11点31分。', 'tts_task_id': '31db3560-1e0c-4288-985c-e9491b84bbc9_5_0', 'tts_type': 'chat_tts_text', 'v3_loundness_params': ''}}
2025-07-26 23:31:14,038 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:14,038 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:14,038 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-26 23:31:14,039 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: chat_tts_text, text: '现在是晚上11点31分。', task_id: 31db3560-1e0c-4288-985c-e9491b84bbc9_5_0    
2025-07-26 23:31:14,039 - audio_agent.events.event_handler - INFO - TTS sentence start - type: chat_tts_text, text: '现在是晚上11点31分。'
2025-07-26 23:31:14,154 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-26 23:31:14,154 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:14,154 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:14,154 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-26 23:31:14,155 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-26 23:31:14,156 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-26 23:31:14,731 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-26 23:31:14,731 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:14,731 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:14,731 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-26 23:31:14,731 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-26 23:31:14,732 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38396 bytes)
2025-07-26 23:31:14,750 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:37916bytes>', 'payload_msg': '<binary_data:37916bytes>'}
2025-07-26 23:31:14,750 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:14,750 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:14,751 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37916 bytes
2025-07-26 23:31:14,751 - audio_agent.events.event_handler - INFO - TTS audio response: 37916 bytes
2025-07-26 23:31:14,752 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37916 bytes)
2025-07-26 23:31:14,855 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38892bytes>', 'payload_msg': '<binary_data:38892bytes>'}
2025-07-26 23:31:14,855 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:14,855 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:14,855 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38892 bytes
2025-07-26 23:31:14,856 - audio_agent.events.event_handler - INFO - TTS audio response: 38892 bytes
2025-07-26 23:31:14,857 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38892 bytes)
2025-07-26 23:31:15,130 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38780bytes>', 'payload_msg': '<binary_data:38780bytes>'}
2025-07-26 23:31:15,131 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:15,131 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:15,131 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38780 bytes
2025-07-26 23:31:15,132 - audio_agent.events.event_handler - INFO - TTS audio response: 38780 bytes
2025-07-26 23:31:15,133 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38780 bytes)
2025-07-26 23:31:15,581 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38780bytes>', 'payload_msg': '<binary_data:38780bytes>'}
2025-07-26 23:31:15,581 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:15,582 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:15,582 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38780 bytes
2025-07-26 23:31:15,582 - audio_agent.events.event_handler - INFO - TTS audio response: 38780 bytes
2025-07-26 23:31:15,582 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38780 bytes)
2025-07-26 23:31:15,793 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38252bytes>', 'payload_msg': '<binary_data:38252bytes>'}
2025-07-26 23:31:15,793 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:15,793 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:15,793 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38252 bytes
2025-07-26 23:31:15,794 - audio_agent.events.event_handler - INFO - TTS audio response: 38252 bytes
2025-07-26 23:31:15,794 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38252 bytes)
2025-07-26 23:31:15,842 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:38712bytes>', 'payload_msg': '<binary_data:38712bytes>'}
2025-07-26 23:31:15,842 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:15,842 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:15,842 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38712 bytes
2025-07-26 23:31:15,842 - audio_agent.events.event_handler - INFO - TTS audio response: 38712 bytes
2025-07-26 23:31:15,843 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38712 bytes)
2025-07-26 23:31:15,861 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': '<binary_data:9004bytes>', 'payload_msg': '<binary_data:9004bytes>'}
2025-07-26 23:31:15,861 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:31:15,861 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:31:15,861 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 9004 bytes
2025-07-26 23:31:15,862 - audio_agent.events.event_handler - INFO - TTS audio response: 9004 bytes
2025-07-26 23:31:15,863 - audio_agent.core.session_manager - INFO - Audio response queued for playback (9004 bytes)
2025-07-26 23:31:15,880 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":45,"flushed":true,"markdown":{"prev_parsed_text":"现在是晚上11点31分\\n","prev_t...<truncated:509chars>', 'speech_alignment_result': '', 'text': '现在是晚上11点31分。'}}
2025-07-26 23:31:15,880 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:15,880 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:15,880 - audio_agent.events.event_handler - INFO - TTS sentence ended       
2025-07-26 23:31:15,884 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': '31db3560-1e0c-4288-985c-e9491b84bbc9', 'payload': {'no_content': False}}
2025-07-26 23:31:15,885 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:31:15,885 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:31:15,885 - audio_agent.events.event_handler - INFO - TTS synthesis ended      
2025-07-26 23:31:18,537 - audio_agent.core.session_manager - INFO - AI finished speaking