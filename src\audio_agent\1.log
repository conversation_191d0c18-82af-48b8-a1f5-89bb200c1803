2025-07-26 23:47:02,697 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:02,697 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:02,697 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:02,697 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:02,697 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小', interim: True
2025-07-26 23:47:02,699 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小'
2025-07-26 23:47:02,804 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9350257515907288, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.242015, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小'}], 'end_time': 9.242015, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小'}]}}
2025-07-26 23:47:02,804 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:02,804 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:02,804 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:02,804 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:02,804 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:02,804 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小', interim: True
2025-07-26 23:47:02,804 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小'
2025-07-26 23:47:02,809 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9632236957550049, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.342017, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯'}], 'end_time': 9.342017, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯'}]}}
2025-07-26 23:47:02,809 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:02,809 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:02,809 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:02,809 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:02,809 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:02,809 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-26 23:47:02,809 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-26 23:47:02,815 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9632236957550049, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.402019, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯'}], 'end_time': 9.402019, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯'}]}}
2025-07-26 23:47:02,816 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:02,816 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:02,816 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:02,817 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:02,817 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:02,817 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-26 23:47:02,817 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-26 23:47:03,063 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9632236957550049, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.502021, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯'}], 'end_time': 9.502021, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯'}]}}
2025-07-26 23:47:03,063 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,063 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,063 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,063 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,063 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,063 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-26 23:47:03,063 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-26 23:47:03,095 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9352171421051025, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.562022, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在'}], 'end_time': 9.562022, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在'}]}}  
2025-07-26 23:47:03,095 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,095 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,095 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,095 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,095 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,095 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在', interim: True
2025-07-26 23:47:03,097 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在'
2025-07-26 23:47:03,137 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9352171421051025, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.662024, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在'}], 'end_time': 9.662024, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在'}]}}  
2025-07-26 23:47:03,137 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,137 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,137 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,137 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,137 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,137 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在', interim: True
2025-07-26 23:47:03,137 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在'
2025-07-26 23:47:03,178 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9352171421051025, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.722026, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在'}], 'end_time': 9.722026, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在'}]}}  
2025-07-26 23:47:03,178 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,178 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,178 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,178 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,178 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,178 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在', interim: True
2025-07-26 23:47:03,178 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在'
2025-07-26 23:47:03,213 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9352171421051025, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.822028, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在'}], 'end_time': 9.822028, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在'}]}}  
2025-07-26 23:47:03,213 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,213 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,213 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,213 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,213 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,213 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在', interim: True
2025-07-26 23:47:03,213 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在'
2025-07-26 23:47:03,256 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9543463587760925, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.88203, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么'}], 'end_time': 9.88203, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯 现在是什么'}]}}
2025-07-26 23:47:03,256 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,256 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,256 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,256 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,256 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,256 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么', interim: True
2025-07-26 23:47:03,256 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么'
2025-07-26 23:47:03,256 - audio_agent.events.event_processor - INFO - 🔧 Early tool detection: '你好小凯现在是什么' likely needs tools
2025-07-26 23:47:03,258 - audio_agent.events.event_processor - INFO - ✅ Early placeholder ChatTTSText sent during ASR interim
2025-07-26 23:47:03,571 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9543463587760925, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 9.982032, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么'}], 'end_time': 9.982032, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小 凯现在是什么'}]}}
2025-07-26 23:47:03,571 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,571 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,571 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,571 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,571 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,571 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么', interim: True
2025-07-26 23:47:03,571 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么'
2025-07-26 23:47:03,576 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9543463587760925, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.042033, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么'}], 'end_time': 10.042033, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好 小凯现在是什么'}]}}
2025-07-26 23:47:03,576 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,576 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,576 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,576 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,576 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,576 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么', interim: True
2025-07-26 23:47:03,576 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么'
2025-07-26 23:47:03,583 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9543463587760925, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.142035, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么'}], 'end_time': 10.142035, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好 小凯现在是什么'}]}}
2025-07-26 23:47:03,583 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,583 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,583 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,584 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,584 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,584 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么', interim: True
2025-07-26 23:47:03,584 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么'
2025-07-26 23:47:03,588 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9543463587760925, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.202037, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么'}], 'end_time': 10.202037, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好 小凯现在是什么'}]}}
2025-07-26 23:47:03,588 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,588 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,588 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,589 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,589 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,589 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么', interim: True
2025-07-26 23:47:03,589 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么'
2025-07-26 23:47:03,595 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 10.302039, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 10.302039, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:03,595 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,595 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,595 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,595 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,595 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,595 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:03,596 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:03,596 - audio_agent.events.event_processor - INFO - 🚨 AGGRESSIVE: Sending placeholder during ASR interim for: '你好小凯现在是什么时间'
2025-07-26 23:47:03,598 - audio_agent.events.event_processor - INFO - 🚨 AGGRESSIVE placeholder sent during ASR interim
2025-07-26 23:47:03,777 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'prefetch': True, 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 39.9954733886716}, 'results': [{'alternatives': [{'end_time': 10.362041, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好，小凯，现在是什么时间？'}], 'end_time': 10.362041, 'index': 0, 'is_interim': True, 'is_soft_finished': True, 'is_vad_timeout': False, 'soft_vad_type': 999, 'start_time': 8.102, 'text': '你好，小凯，现在是什么时间？'}]}}
2025-07-26 23:47:03,777 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,777 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,777 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,777 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,777 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,777 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好，小 凯，现在是什么时间？', interim: True
2025-07-26 23:47:03,777 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好，小凯，现在是什么时间？'
2025-07-26 23:47:03,846 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 139.99680853271457}, 'results': [{'alternatives': [{'end_time': 10.462043, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 10.462043, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:03,846 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,846 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,846 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,846 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,846 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,846 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:03,846 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:03,852 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 189.99418182372983}, 'results': [{'alternatives': [{'end_time': 10.522044, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 10.522044, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:03,852 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:03,852 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:03,852 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:03,852 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:03,852 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:03,852 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:03,852 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:04,111 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 289.9964706420892}, 'results': [{'alternatives': [{'end_time': 10.622046, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 10.622046, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:04,111 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,111 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,111 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:04,111 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:04,111 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:04,111 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:04,111 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:04,115 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 339.9958439331066}, 'results': [{'alternatives': [{'end_time': 10.682048, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 10.682048, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:04,115 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,115 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,115 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:04,115 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:04,115 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:04,115 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:04,115 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:04,122 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 439.99813275146596}, 'results': [{'alternatives': [{'end_time': 10.78205, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 10.78205, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:04,122 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,122 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,122 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:04,122 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:04,122 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:04,123 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:04,123 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:04,128 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 500.000459716798}, 'results': [{'alternatives': [{'end_time': 10.842052, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 10.842052, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:04,128 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,128 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,128 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:04,128 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:04,128 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:04,128 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:04,128 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:04,135 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 600.001794860841}, 'results': [{'alternatives': [{'end_time': 10.942054, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 10.942054, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:04,135 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,135 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,135 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:04,136 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:04,136 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:04,136 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:04,136 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:04,140 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 660.0031681518566}, 'results': [{'alternatives': [{'end_time': 11.002055, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 11.002055, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:04,140 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,141 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,141 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:04,141 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:04,141 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:04,141 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:04,141 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:04,146 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'interrupt_score': 0.9292702674865723, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 760.005456970216}, 'results': [{'alternatives': [{'end_time': 11.102057, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}], 'end_time': 11.102057, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好小凯现在是什么时间'}]}}
2025-07-26 23:47:04,146 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,146 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,146 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:04,147 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:04,147 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:04,147 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯 现在是什么时间', interim: True
2025-07-26 23:47:04,147 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯现在是什么时间'
2025-07-26 23:47:04,151 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.32946792244911194, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯现在是什么时间', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '你好，小凯，现在是什么时间？', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 820.0068302612316}, 'results': [{'alternatives': [{'end_time': 11.162059, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 8.102, 'text': '你好，小凯，现在是什么时间？'}], 'end_time': 11.162059, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 8.102, 'text': '你好，小凯，现在是什么时间？'}]}}
2025-07-26 23:47:04,151 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,151 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,151 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-26 23:47:04,151 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-26 23:47:04,151 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-26 23:47:04,151 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好，小 凯，现在是什么时间？', interim: False
2025-07-26 23:47:04,151 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '你好，小凯，现在是什么时间？'
2025-07-26 23:47:04,469 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 1609, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 10342}}
2025-07-26 23:47:04,469 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,469 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,469 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-26 23:47:04,469 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-26 23:47:04,469 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '你好，小凯，现在是什么时间？'
2025-07-26 23:47:04,469 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '你好，小凯，现在是什么时间？'
2025-07-26 23:47:04,469 - audio_agent.events.event_processor - INFO - 🔧 Tool calling activated - will filter server auto-responses
2025-07-26 23:47:04,472 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
(DialogClientActor pid=13772) OPENAI_API_KEY not found, using rule-based fallback
2025-07-26 23:47:04,770 - audio_agent.events.llm_enhancer - WARNING - OPENAI_API_KEY not found, using rule-based fallback
2025-07-26 23:47:04,770 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'datetime_query', 'confidence': 0.9, 'requires_tools': True, 'tools': ['datetime'], 'parameters': {}}
2025-07-26 23:47:04,770 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'datetime_query', 'confidence': 0.9, 'requires_tools': True, 'tools': ['datetime'], 'parameters': {}}
2025-07-26 23:47:04,770 - audio_agent.events.event_processor - INFO - 🔧 Tools needed, sending placeholder first: ['datetime']
2025-07-26 23:47:04,770 - audio_agent.events.event_processor - INFO - ✅ Immediate placeholder already sent at ASR end, proceeding with tool execution
2025-07-26 23:47:04,770 - audio_agent.events.event_processor - INFO - 🔧 Executing tools: ['datetime']
2025-07-26 23:47:04,771 - audio_agent.tools.executor - INFO - Executing tool: datetime with parameters: {'operation': 'current_time'}
2025-07-26 23:47:04,771 - audio_agent.tools.builtin.datetime_tool - INFO - DateTime operation: current_time
2025-07-26 23:47:04,771 - audio_agent.tools.executor - INFO - Tool datetime executed successfully
2025-07-26 23:47:04,771 - audio_agent.events.llm_enhancer - INFO - Tool datetime executed successfully
2025-07-26 23:47:04,774 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': '729e6051-74a0-44ab-af81-71017f7e34ff_2_1', 'tts_type': 'default', 'v3_loundness_params': ''}}        
2025-07-26 23:47:04,774 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,774 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,802 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'content': ''}}
2025-07-26 23:47:04,803 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,803 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,832 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'content': '现在'}}
2025-07-26 23:47:04,832 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,832 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,860 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'content': '是'}}
2025-07-26 23:47:04,860 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,860 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,887 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'content': '下午'}}
2025-07-26 23:47:04,887 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,887 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,914 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'content': '，'}}
2025-07-26 23:47:04,915 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,915 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,936 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'content': '嗯'}}
2025-07-26 23:47:04,936 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:04,936 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:04,965 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-26 23:47:04,965 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:04,965 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:04,965 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-26 23:47:04,969 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
(DialogClientActor pid=13772) 🚫 FILTERING server auto-response (tts_type=default): '' - audio will be DISCARDED
2025-07-26 23:47:04,983 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:47:04,987 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38120bytes>', 'payload_msg': '<binary_data:38120bytes>'}
2025-07-26 23:47:04,987 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:04,987 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:04,988 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38120 bytes
2025-07-26 23:47:04,989 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38120 bytes)
2025-07-26 23:47:05,005 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'content': '，具体时间是'}}
2025-07-26 23:47:05,006 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:05,006 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:05,023 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38444bytes>', 'payload_msg': '<binary_data:38444bytes>'}
2025-07-26 23:47:05,023 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,023 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,023 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38444 bytes
2025-07-26 23:47:05,024 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38444 bytes)
2025-07-26 23:47:05,281 - audio_agent.events.event_processor - INFO - 🔧 Tool result ready to send to server:
2025-07-26 23:47:05,281 - audio_agent.events.event_processor - INFO -    User input: 你好，小凯，现在是什么时间？
2025-07-26 23:47:05,281 - audio_agent.events.event_processor - INFO -    Tool results: 1 tools executed
2025-07-26 23:47:05,281 - audio_agent.events.event_processor - INFO -    AI response: 现在是 晚上11点47分
2025-07-26 23:47:05,284 - audio_agent.events.event_processor - INFO - ✅ Tool result sent to server successfully using streaming ChatTTSText
2025-07-26 23:47:05,284 - audio_agent.events.event_processor - INFO - 🔧 Tool processing complete, but keeping tool_calling_active=True to filter server responses
2025-07-26 23:47:05,284 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-26 23:47:05,285 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: 729e6051-74a0-44ab-af81-71017f7e34ff_2_1
2025-07-26 23:47:05,285 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-26 23:47:05,285 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-26 23:47:05,285 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: True
2025-07-26 23:47:05,285 - audio_agent.events.event_handler - WARNING - 🚫 FILTERING server auto-response (tts_type=default): '' - audio will be DISCARDED
2025-07-26 23:47:05,285 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-26 23:47:05,285 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:47:05,286 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:47:05,286 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:47:05,286 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:47:05,286 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:47:05,286 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:47:05,286 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-26 23:47:05,286 - audio_agent.events.event_handler - INFO - TTS audio response: 38120 bytes
2025-07-26 23:47:05,286 - audio_agent.events.event_handler - INFO - Chat response: ''        
2025-07-26 23:47:05,288 - audio_agent.events.event_handler - INFO - TTS audio response: 38444 bytes
2025-07-26 23:47:05,401 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38824bytes>', 'payload_msg': '<binary_data:38824bytes>'}
2025-07-26 23:47:05,401 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,401 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,401 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38824 bytes
2025-07-26 23:47:05,401 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38824 bytes) - filtered by TTS type (server auto-response)
2025-07-26 23:47:05,402 - audio_agent.events.event_handler - INFO - TTS audio response: 38824 bytes
2025-07-26 23:47:05,409 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38104bytes>', 'payload_msg': '<binary_data:38104bytes>'}
2025-07-26 23:47:05,409 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,410 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,410 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38104 bytes
2025-07-26 23:47:05,410 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38104 bytes) - filtered by TTS type (server auto-response)
2025-07-26 23:47:05,411 - audio_agent.events.event_handler - INFO - TTS audio response: 38104 bytes
2025-07-26 23:47:05,427 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38860bytes>', 'payload_msg': '<binary_data:38860bytes>'}
2025-07-26 23:47:05,427 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,427 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,428 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38860 bytes
2025-07-26 23:47:05,428 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38860 bytes) - filtered by TTS type (server auto-response)
2025-07-26 23:47:05,428 - audio_agent.events.event_handler - INFO - TTS audio response: 38860 bytes
2025-07-26 23:47:05,434 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38428bytes>', 'payload_msg': '<binary_data:38428bytes>'}
2025-07-26 23:47:05,435 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,435 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,435 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38428 bytes
2025-07-26 23:47:05,435 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38428 bytes) - filtered by TTS type (server auto-response)
2025-07-26 23:47:05,435 - audio_agent.events.event_handler - INFO - TTS audio response: 38428 bytes
2025-07-26 23:47:05,441 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38716bytes>', 'payload_msg': '<binary_data:38716bytes>'}
2025-07-26 23:47:05,441 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,441 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,441 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38716 bytes
2025-07-26 23:47:05,441 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38716 bytes) - filtered by TTS type (server auto-response)
2025-07-26 23:47:05,441 - audio_agent.events.event_handler - INFO - TTS audio response: 38716 bytes
2025-07-26 23:47:05,474 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:37964bytes>', 'payload_msg': '<binary_data:37964bytes>'}
2025-07-26 23:47:05,475 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,475 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,475 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37964 bytes
2025-07-26 23:47:05,475 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (37964 bytes) - filtered by TTS type (server auto-response)
2025-07-26 23:47:05,475 - audio_agent.events.event_handler - INFO - TTS audio response: 37964 bytes
2025-07-26 23:47:05,664 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:47:05,800 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38776bytes>', 'payload_msg': '<binary_data:38776bytes>'}
2025-07-26 23:47:05,800 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,800 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,800 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38776 bytes
2025-07-26 23:47:05,800 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38776 bytes) - filtered by TTS type (server auto-response)
2025-07-26 23:47:05,800 - audio_agent.events.event_handler - INFO - TTS audio response: 38776 bytes
2025-07-26 23:47:05,831 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-26 23:47:05,831 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:05,831 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:05,831 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-26 23:47:05,831 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38396 bytes) - filtered by TTS type (server auto-response)
2025-07-26 23:47:05,832 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-26 23:47:05,837 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'enable_v3_loudness_balance': True, 'gta': True, 'model_type': 'v3', 'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":45,"flushed":true,"markdown":{"prev_parsed_text":"现在是晚上11点47分\\n","prev_t...<truncated:509chars>', 'text': '现在是晚上11点47分。', 'tts_task_id': '729e6051-74a0-44ab-af81-71017f7e34ff_3_0', 'tts_type': 'chat_tts_text', 'v3_loundness_params': ''}}
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: chat_tts_text, text: '现在是晚上11点47分。', task_id: 729e6051-74a0-44ab-af81-71017f7e34ff_3_0    
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: True
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - ✅ ALLOWING tool result TTS (tts_type=chat_tts_text): '现在是晚上11点47分。' (task_id: 729e6051-74a0-44ab-af81-71017f7e34ff_3_0)
2025-07-26 23:47:05,838 - audio_agent.events.event_handler - INFO - TTS sentence start - type: chat_tts_text, text: '现在是晚上11点47分。'
2025-07-26 23:47:06,739 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-26 23:47:06,739 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:06,739 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:06,739 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-26 23:47:06,739 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-26 23:47:06,740 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-26 23:47:06,753 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-26 23:47:06,758 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38956bytes>', 'payload_msg': '<binary_data:38956bytes>'}
2025-07-26 23:47:06,758 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:06,758 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:06,758 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38956 bytes
2025-07-26 23:47:06,759 - audio_agent.events.event_handler - INFO - TTS audio response: 38956 bytes
2025-07-26 23:47:06,759 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38956 bytes)
2025-07-26 23:47:06,776 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38412bytes>', 'payload_msg': '<binary_data:38412bytes>'}
2025-07-26 23:47:06,776 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:06,776 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:06,777 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38412 bytes
2025-07-26 23:47:06,777 - audio_agent.events.event_handler - INFO - TTS audio response: 38412 bytes
2025-07-26 23:47:06,778 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38412 bytes)
2025-07-26 23:47:07,072 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38504bytes>', 'payload_msg': '<binary_data:38504bytes>'}
2025-07-26 23:47:07,073 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:07,073 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:07,073 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38504 bytes
2025-07-26 23:47:07,073 - audio_agent.events.event_handler - INFO - TTS audio response: 38504 bytes
2025-07-26 23:47:07,074 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38504 bytes)
2025-07-26 23:47:07,092 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:37852bytes>', 'payload_msg': '<binary_data:37852bytes>'}
2025-07-26 23:47:07,092 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:07,092 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:07,092 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37852 bytes
2025-07-26 23:47:07,092 - audio_agent.events.event_handler - INFO - TTS audio response: 37852 bytes
2025-07-26 23:47:07,093 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37852 bytes)
2025-07-26 23:47:07,109 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:38156bytes>', 'payload_msg': '<binary_data:38156bytes>'}
2025-07-26 23:47:07,110 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:07,110 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:07,110 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38156 bytes
2025-07-26 23:47:07,110 - audio_agent.events.event_handler - INFO - TTS audio response: 38156 bytes
2025-07-26 23:47:07,111 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38156 bytes)
2025-07-26 23:47:07,128 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': '<binary_data:16968bytes>', 'payload_msg': '<binary_data:16968bytes>'}
2025-07-26 23:47:07,129 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-26 23:47:07,129 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-26 23:47:07,129 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 16968 bytes
2025-07-26 23:47:07,129 - audio_agent.events.event_handler - INFO - TTS audio response: 16968 bytes
2025-07-26 23:47:07,130 - audio_agent.core.session_manager - INFO - Audio response queued for playback (16968 bytes)
2025-07-26 23:47:07,148 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":45,"flushed":true,"markdown":{"prev_parsed_text":"现在是晚上11点47分\\n","prev_t...<truncated:509chars>', 'speech_alignment_result': '', 'text': '现在是晚上11点47分。'}}
2025-07-26 23:47:07,148 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:07,148 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:07,148 - audio_agent.events.event_handler - INFO - TTS sentence ended       
2025-07-26 23:47:07,154 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': '729e6051-74a0-44ab-af81-71017f7e34ff', 'payload': {'no_content': False}}
2025-07-26 23:47:07,154 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-26 23:47:07,154 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-26 23:47:07,155 - audio_agent.events.event_handler - INFO - TTS synthesis ended      
2025-07-26 23:47:07,155 - audio_agent.events.event_handler - INFO - 🔧 TTS synthesis ended, resetting tool calling state
2025-07-26 23:47:09,059 - audio_agent.core.session_manager - INFO - AI finished speaking