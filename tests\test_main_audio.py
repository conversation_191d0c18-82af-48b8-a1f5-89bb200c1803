#!/usr/bin/env python3
"""
测试主程序的音频检测
"""

import asyncio
import logging
import time
from audio_agent.config import get_config
from audio_agent.core.session_manager import SessionManager

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_session_manager():
    """测试SessionManager的音频处理"""
    print("=== 测试SessionManager音频处理 ===")
    
    config = get_config()
    
    try:
        # 创建SessionManager
        session_manager = SessionManager(config)
        
        print("启动SessionManager...")
        await session_manager.start()
        
        print("✅ SessionManager启动成功")
        print("监控10秒，请说话...")
        
        # 监控10秒
        for i in range(100):  # 10秒，每次0.1秒
            await asyncio.sleep(0.1)
            
            # 每秒显示一次状态
            if i % 10 == 0:
                print(f"监控中... {i//10}/10秒")
        
        print("停止SessionManager...")
        await session_manager.stop()
        print("✅ SessionManager已停止")
        
    except Exception as e:
        print(f"❌ SessionManager测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_audio_input_only():
    """只测试音频输入部分"""
    print("\n=== 只测试音频输入 ===")
    
    import ray
    from audio_agent.core.actors import AudioInputActor
    
    config = get_config()
    
    # 初始化Ray
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    
    try:
        # 创建音频输入Actor
        audio_input_actor = AudioInputActor.remote(config.input_audio)
        
        # 启动录音
        started = await audio_input_actor.start_recording.remote()
        
        if started:
            print("✅ 音频输入启动成功")
            print("监控10秒，观察音频检测日志...")
            
            # 监控10秒
            audio_count = 0
            for i in range(100):
                try:
                    audio_data = await audio_input_actor.get_audio_data.remote()
                    
                    if audio_data:
                        audio_count += 1
                        
                        # 分析音频数据
                        import numpy as np
                        audio_int16 = np.frombuffer(audio_data, dtype=np.int16)
                        max_amplitude = np.abs(audio_int16).max()
                        avg_amplitude = np.abs(audio_int16).mean()
                        
                        # 每秒显示一次
                        if audio_count % 10 == 0:
                            print(f"音频数据 #{audio_count}: 最大幅度={max_amplitude}, 平均幅度={avg_amplitude:.1f}")
                    
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    print(f"获取音频数据出错: {e}")
                    break
            
            # 停止录音
            await audio_input_actor.stop_recording.remote()
            print(f"总共处理了 {audio_count} 个音频块")
            
        else:
            print("❌ 音频输入启动失败")
            
    except Exception as e:
        print(f"❌ 音频输入测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if ray.is_initialized():
            ray.shutdown()

if __name__ == "__main__":
    asyncio.run(test_audio_input_only())
    # asyncio.run(test_session_manager())
