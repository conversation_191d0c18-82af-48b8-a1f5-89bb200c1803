#!/usr/bin/env python3
"""
测试WebSocket音频发送
"""

import asyncio
import logging
import time
from audio_agent.config import get_config
from audio_agent.core.session_manager import SessionManager

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_full_session():
    """测试完整的会话流程"""
    print("=== 测试完整会话流程 ===")
    
    config = get_config()
    
    try:
        # 创建SessionManager
        session_manager = SessionManager(config)
        
        print("启动SessionManager...")
        await session_manager.start()
        
        print("✅ SessionManager启动成功")
        print("现在开始说话，观察日志...")
        print("程序将运行30秒，然后自动停止")
        
        # 运行30秒
        await asyncio.sleep(30)
        
        print("停止SessionManager...")
        await session_manager.stop()
        print("✅ SessionManager已停止")
        
    except Exception as e:
        print(f"❌ 完整会话测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_dialog_client_only():
    """只测试DialogClient连接"""
    print("\n=== 测试DialogClient连接 ===")
    
    import ray
    from audio_agent.core.actors import DialogClientActor
    
    config = get_config()
    
    # 初始化Ray
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    
    try:
        # 创建DialogClient Actor
        dialog_client_actor = DialogClientActor.remote(config.websocket)
        
        # 连接到服务
        session_id = "test_session_" + str(int(time.time()))
        connected = await dialog_client_actor.connect.remote(session_id)
        
        print(f"连接结果: {connected}")

        if connected:
            print("✅ DialogClient连接成功")
            
            # 测试发送一些音频数据
            print("发送测试音频数据...")
            
            # 创建一些测试音频数据（静音）
            import numpy as np
            test_audio = np.zeros(3200, dtype=np.int16).tobytes()
            
            for i in range(5):
                sent = await dialog_client_actor.send_audio_data.remote(test_audio)
                if sent:
                    print(f"✅ 测试音频数据 #{i+1} 发送成功")
                else:
                    print(f"❌ 测试音频数据 #{i+1} 发送失败")
                
                await asyncio.sleep(1)
            
            # 检查是否有响应
            print("检查服务器响应...")
            for i in range(10):
                response = await dialog_client_actor.get_response.remote()
                if response:
                    print(f"收到服务器响应: {response}")
                else:
                    print(f"检查响应 #{i+1}: 无响应")
                
                await asyncio.sleep(0.5)
            
            # 断开连接
            await dialog_client_actor.disconnect.remote()
            print("✅ DialogClient已断开连接")
            
        else:
            print("❌ DialogClient连接失败")
            
    except Exception as e:
        print(f"❌ DialogClient测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if ray.is_initialized():
            ray.shutdown()

if __name__ == "__main__":
    # 先测试DialogClient连接
    asyncio.run(test_dialog_client_only())
    
    print("\n" + "="*50)
    
    # 再测试完整会话
    # asyncio.run(test_full_session())
