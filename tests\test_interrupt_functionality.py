#!/usr/bin/env python3
"""
Test script for audio playback interrupt functionality.

This script tests the new interrupt features:
1. Voice Activity Detection (VAD)
2. Audio playback interruption
3. Queue clearing functionality
"""

import asyncio
import logging
import time
import numpy as np
from audio_agent.config import get_config
from audio_agent.core import SessionManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class InterruptFunctionalityTest:
    """Test class for interrupt functionality."""
    
    def __init__(self):
        """Initialize the test."""
        self.config = get_config()
        self.session_manager = None
        
        # Test configuration
        self.config.interrupt.enabled = True
        self.config.interrupt.response_delay_ms = 50
        self.config.input_audio.vad_enabled = True
        self.config.input_audio.vad_threshold = 100
        self.config.input_audio.vad_window_size = 3
        
        logger.info("Interrupt functionality test initialized")
    
    async def test_vad_detection(self):
        """Test Voice Activity Detection functionality."""
        logger.info("=== Testing VAD Detection ===")
        
        try:
            # Start session manager
            self.session_manager = SessionManager(self.config)
            await self.session_manager.start()
            
            logger.info("Monitoring voice activity for 10 seconds...")
            logger.info("Please speak to test VAD detection")
            
            for i in range(100):  # 10 seconds
                voice_active = await self.session_manager.audio_input_actor.get_voice_activity.remote()
                
                if voice_active:
                    logger.info(f"✅ Voice activity detected at {i/10:.1f}s")
                elif i % 20 == 0:  # Log every 2 seconds
                    logger.info(f"🔇 No voice activity at {i/10:.1f}s")
                
                await asyncio.sleep(0.1)
            
            logger.info("VAD test completed")
            return True
            
        except Exception as e:
            logger.error(f"VAD test failed: {e}")
            return False
        finally:
            if self.session_manager:
                await self.session_manager.stop()
    
    async def test_queue_operations(self):
        """Test audio queue operations."""
        logger.info("=== Testing Queue Operations ===")
        
        try:
            # Start session manager
            self.session_manager = SessionManager(self.config)
            await self.session_manager.start()
            
            # Test queue empty check
            is_empty = await self.session_manager.audio_output_actor.is_queue_empty.remote()
            logger.info(f"Initial queue empty: {is_empty}")
            
            # Add some dummy audio data
            dummy_audio = np.random.randint(-1000, 1000, 1024, dtype=np.int16).tobytes()
            
            for i in range(5):
                queued = await self.session_manager.audio_output_actor.queue_audio_data.remote(dummy_audio)
                if queued:
                    logger.info(f"✅ Queued audio chunk {i+1}")
                else:
                    logger.error(f"❌ Failed to queue audio chunk {i+1}")
            
            # Check queue size
            queue_size = await self.session_manager.audio_output_actor.get_queue_size.remote()
            logger.info(f"Queue size after adding: {queue_size}")
            
            # Test queue clearing
            cleared = await self.session_manager.audio_output_actor.clear_audio_queue.remote()
            if cleared:
                logger.info("✅ Queue cleared successfully")
            else:
                logger.error("❌ Failed to clear queue")
            
            # Check queue size after clearing
            queue_size = await self.session_manager.audio_output_actor.get_queue_size.remote()
            is_empty = await self.session_manager.audio_output_actor.is_queue_empty.remote()
            logger.info(f"Queue size after clearing: {queue_size}, empty: {is_empty}")
            
            return True
            
        except Exception as e:
            logger.error(f"Queue operations test failed: {e}")
            return False
        finally:
            if self.session_manager:
                await self.session_manager.stop()
    
    async def test_interrupt_functionality(self):
        """Test interrupt functionality."""
        logger.info("=== Testing Interrupt Functionality ===")
        
        try:
            # Start session manager
            self.session_manager = SessionManager(self.config)
            await self.session_manager.start()
            
            # Simulate AI speaking by adding audio to queue
            dummy_audio = np.random.randint(-1000, 1000, 1024, dtype=np.int16).tobytes()
            
            # Add multiple chunks to simulate ongoing speech
            for i in range(10):
                await self.session_manager.audio_output_actor.queue_audio_data.remote(dummy_audio)
            
            # Mark AI as speaking
            self.session_manager.ai_is_speaking = True
            logger.info("Simulated AI speaking with 10 audio chunks queued")
            
            # Check initial queue size
            queue_size = await self.session_manager.audio_output_actor.get_queue_size.remote()
            logger.info(f"Initial queue size: {queue_size}")
            
            # Test interrupt
            logger.info("Testing interrupt...")
            interrupted = await self.session_manager.audio_output_actor.interrupt_playback.remote()
            
            if interrupted:
                logger.info("✅ Interrupt signal sent successfully")
                
                # Wait a moment for interrupt to process
                await asyncio.sleep(0.2)
                
                # Check queue size after interrupt
                queue_size = await self.session_manager.audio_output_actor.get_queue_size.remote()
                is_empty = await self.session_manager.audio_output_actor.is_queue_empty.remote()
                logger.info(f"Queue size after interrupt: {queue_size}, empty: {is_empty}")
                
                if is_empty:
                    logger.info("✅ Queue successfully cleared by interrupt")
                else:
                    logger.warning("⚠️ Queue not empty after interrupt")
            else:
                logger.error("❌ Failed to send interrupt signal")
            
            return True
            
        except Exception as e:
            logger.error(f"Interrupt functionality test failed: {e}")
            return False
        finally:
            if self.session_manager:
                await self.session_manager.stop()
    
    async def run_all_tests(self):
        """Run all interrupt functionality tests."""
        logger.info("🚀 Starting interrupt functionality tests")
        
        tests = [
            ("VAD Detection", self.test_vad_detection),
            ("Queue Operations", self.test_queue_operations),
            ("Interrupt Functionality", self.test_interrupt_functionality),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*50}")
            
            try:
                result = await test_func()
                results[test_name] = result
                
                if result:
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED with exception: {e}")
                results[test_name] = False
            
            # Wait between tests
            await asyncio.sleep(2)
        
        # Print summary
        logger.info(f"\n{'='*50}")
        logger.info("TEST SUMMARY")
        logger.info(f"{'='*50}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed!")
        else:
            logger.warning(f"⚠️ {total - passed} test(s) failed")
        
        return passed == total


async def main():
    """Main test function."""
    test = InterruptFunctionalityTest()
    success = await test.run_all_tests()
    
    if success:
        logger.info("🎉 All interrupt functionality tests completed successfully!")
    else:
        logger.error("❌ Some tests failed. Please check the logs above.")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
