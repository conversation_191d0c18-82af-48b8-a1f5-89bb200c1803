"""
Tests for the tool system.

This module contains tests for the tool calling functionality
including tool registration, execution, and integration.
"""

import pytest
import asyncio
from audio_agent.tools.base import BaseTool, ToolResult, ToolParameter, ParameterType
from audio_agent.tools.registry import ToolRegistry
from audio_agent.tools.executor import ToolExecutor
from audio_agent.tools.builtin import WebSearchTool, CalculatorTool, DateTimeTool


class MockTool(BaseTool):
    """Mock tool for testing."""
    
    def __init__(self):
        super().__init__("mock_tool", "A mock tool for testing")
        self.add_parameter(ToolParameter(
            name="test_param",
            type=ParameterType.STRING,
            description="Test parameter",
            required=True
        ))
    
    async def execute(self, **kwargs) -> ToolResult:
        validated_params = self.validate_parameters(**kwargs)
        return ToolResult(
            success=True,
            data={"input": validated_params["test_param"], "output": "mock_result"}
        )


@pytest.fixture
def tool_registry():
    """Create a fresh tool registry for testing."""
    return ToolRegistry()


@pytest.fixture
def tool_executor(tool_registry):
    """Create a tool executor with the test registry."""
    return ToolExecutor(tool_registry)


class TestBaseTool:
    """Test the BaseTool class."""
    
    def test_tool_creation(self):
        """Test tool creation and basic properties."""
        tool = MockTool()
        assert tool.name == "mock_tool"
        assert tool.description == "A mock tool for testing"
        assert len(tool.get_parameters()) == 1
    
    def test_function_schema(self):
        """Test function schema generation."""
        tool = MockTool()
        schema = tool.to_function_schema()
        
        assert schema["type"] == "function"
        assert schema["function"]["name"] == "mock_tool"
        assert "test_param" in schema["function"]["parameters"]["properties"]
        assert "test_param" in schema["function"]["parameters"]["required"]
    
    @pytest.mark.asyncio
    async def test_tool_execution(self):
        """Test tool execution."""
        tool = MockTool()
        result = await tool.execute(test_param="hello")
        
        assert result.success
        assert result.data["input"] == "hello"
        assert result.data["output"] == "mock_result"
    
    def test_parameter_validation(self):
        """Test parameter validation."""
        tool = MockTool()
        
        # Valid parameters
        validated = tool.validate_parameters(test_param="valid")
        assert validated["test_param"] == "valid"
        
        # Missing required parameter
        with pytest.raises(Exception):
            tool.validate_parameters()


class TestToolRegistry:
    """Test the ToolRegistry class."""
    
    def test_registry_creation(self, tool_registry):
        """Test registry creation."""
        assert len(tool_registry) == 0
        assert tool_registry.list_tools() == []
    
    def test_tool_registration(self, tool_registry):
        """Test tool registration."""
        tool = MockTool()
        tool_registry.register_tool(tool)
        
        assert len(tool_registry) == 1
        assert "mock_tool" in tool_registry
        assert tool_registry.get_tool("mock_tool") == tool
    
    def test_duplicate_registration(self, tool_registry):
        """Test duplicate tool registration."""
        tool1 = MockTool()
        tool2 = MockTool()
        
        tool_registry.register_tool(tool1)
        
        with pytest.raises(ValueError):
            tool_registry.register_tool(tool2)
    
    def test_tool_unregistration(self, tool_registry):
        """Test tool unregistration."""
        tool = MockTool()
        tool_registry.register_tool(tool)
        
        assert tool_registry.unregister_tool("mock_tool")
        assert len(tool_registry) == 0
        assert not tool_registry.unregister_tool("nonexistent")
    
    def test_function_schemas(self, tool_registry):
        """Test function schema generation."""
        tool = MockTool()
        tool_registry.register_tool(tool)
        
        schemas = tool_registry.get_function_schemas()
        assert len(schemas) == 1
        assert schemas[0]["function"]["name"] == "mock_tool"


class TestToolExecutor:
    """Test the ToolExecutor class."""
    
    @pytest.mark.asyncio
    async def test_tool_execution(self, tool_executor, tool_registry):
        """Test tool execution through executor."""
        tool = MockTool()
        tool_registry.register_tool(tool)
        
        result = await tool_executor.execute_tool_call("mock_tool", {"test_param": "test"})
        
        assert result.success
        assert result.data["input"] == "test"
    
    @pytest.mark.asyncio
    async def test_nonexistent_tool(self, tool_executor):
        """Test execution of nonexistent tool."""
        result = await tool_executor.execute_tool_call("nonexistent", {})
        
        assert not result.success
        assert "not found" in result.error
    
    @pytest.mark.asyncio
    async def test_tool_calls_format(self, tool_executor, tool_registry):
        """Test execution with Volcengine tool call format."""
        tool = MockTool()
        tool_registry.register_tool(tool)
        
        tool_calls = [{
            "type": "function",
            "function": {
                "name": "mock_tool",
                "arguments": '{"test_param": "volcengine_test"}'
            }
        }]
        
        results = await tool_executor.execute_tool_calls(tool_calls)
        
        assert len(results) == 1
        assert results[0].success
        assert results[0].data["input"] == "volcengine_test"


class TestBuiltinTools:
    """Test the built-in tools."""
    
    @pytest.mark.asyncio
    async def test_calculator_tool(self):
        """Test calculator tool."""
        tool = CalculatorTool()
        
        # Test basic arithmetic
        result = await tool.execute(expression="2 + 3 * 4")
        assert result.success
        assert result.data["result"] == 14
        
        # Test mathematical functions
        result = await tool.execute(expression="sqrt(16)")
        assert result.success
        assert result.data["result"] == 4.0
    
    @pytest.mark.asyncio
    async def test_datetime_tool(self):
        """Test datetime tool."""
        tool = DateTimeTool()
        
        # Test current time
        result = await tool.execute(operation="current_time")
        assert result.success
        assert "time" in result.data
        
        # Test current date
        result = await tool.execute(operation="current_date")
        assert result.success
        assert "date" in result.data
        assert "weekday" in result.data
    
    @pytest.mark.asyncio
    async def test_web_search_tool(self):
        """Test web search tool."""
        tool = WebSearchTool()
        
        # Test search (may fail due to network issues in CI)
        try:
            result = await tool.execute(query="Python programming", max_results=2)
            # If successful, check structure
            if result.success:
                assert "results" in result.data
                assert "query" in result.data
                assert result.data["query"] == "Python programming"
        except Exception:
            # Network issues are acceptable in tests
            pass


@pytest.mark.asyncio
async def test_integration():
    """Test full integration of tool system."""
    from audio_agent.tools.builtin import register_builtin_tools
    from audio_agent.tools import get_tool_registry
    
    # Register built-in tools
    register_builtin_tools()
    
    registry = get_tool_registry()
    executor = ToolExecutor(registry)
    
    # Test that tools are registered
    tools = registry.list_tools()
    assert "calculator" in tools
    assert "datetime" in tools
    assert "web_search" in tools
    
    # Test calculator
    result = await executor.execute_tool_call("calculator", {"expression": "5 * 6"})
    assert result.success
    assert result.data["result"] == 30
    
    # Test datetime
    result = await executor.execute_tool_call("datetime", {"operation": "current_datetime"})
    assert result.success
    assert "datetime" in result.data


if __name__ == "__main__":
    pytest.main([__file__])
