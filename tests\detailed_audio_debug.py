#!/usr/bin/env python3
"""
详细的音频调试脚本
"""

import asyncio
import logging
import numpy as np
import pyaudio
import time
from audio_agent.config import get_config

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_direct_audio_with_config():
    """直接使用配置测试音频"""
    config = get_config()
    
    print("=== 直接音频测试（使用配置） ===")
    print(f"配置:")
    print(f"  - 采样率: {config.input_audio.sample_rate}Hz")
    print(f"  - 通道数: {config.input_audio.channels}")
    print(f"  - 块大小: {config.input_audio.chunk}")
    print(f"  - 位深度: {config.input_audio.bit_size}")
    
    audio = pyaudio.PyAudio()
    
    try:
        # 尝试打开音频流
        stream = audio.open(
            format=config.input_audio.bit_size,
            channels=config.input_audio.channels,
            rate=config.input_audio.sample_rate,
            input=True,
            frames_per_buffer=config.input_audio.chunk
        )
        
        print("✅ 音频流打开成功")
        print("开始录音5秒，请说话...")
        
        max_amplitudes = []
        for i in range(50):  # 5秒，每次0.1秒
            try:
                data = stream.read(config.input_audio.chunk, exception_on_overflow=False)
                
                # 分析音频数据
                if len(data) > 0:
                    audio_int16 = np.frombuffer(data, dtype=np.int16)
                    max_amplitude = np.abs(audio_int16).max()
                    avg_amplitude = np.abs(audio_int16).mean()
                    max_amplitudes.append(max_amplitude)
                    
                    # 显示音频级别
                    if i % 5 == 0:  # 每0.5秒显示一次
                        level_bar = "█" * min(int(max_amplitude / 1000), 30)
                        print(f"音频级别: {max_amplitude:5d} |{level_bar:<30}| 平均: {avg_amplitude:.1f}")
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"读取音频数据出错: {e}")
                break
        
        stream.stop_stream()
        stream.close()
        
        # 分析结果
        if max_amplitudes:
            overall_max = max(max_amplitudes)
            overall_avg = np.mean(max_amplitudes)
            
            print(f"\n录音分析:")
            print(f"最大音频幅度: {overall_max}")
            print(f"平均音频幅度: {overall_avg:.2f}")
            
            if overall_max < 10:
                print("❌ 音频幅度很低，可能麦克风没有工作")
                return False
            elif overall_max < 100:
                print("⚠️  音频幅度较低，但可能正常")
                return True
            else:
                print("✅ 音频幅度正常")
                return True
        else:
            print("❌ 没有收到音频数据")
            return False
            
    except Exception as e:
        print(f"❌ 打开音频流失败: {e}")
        return False
    finally:
        audio.terminate()

def test_audio_input_actor_directly():
    """直接测试AudioInputActor"""
    print("\n=== 测试AudioInputActor ===")
    
    import ray
    from audio_agent.core.actors import AudioInputActor
    
    config = get_config()
    
    # 初始化Ray
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    
    try:
        # 创建Actor
        audio_actor = AudioInputActor.remote(config.input_audio)
        
        # 启动录音
        started = ray.get(audio_actor.start_recording.remote())
        if not started:
            print("❌ AudioInputActor启动失败")
            return False
        
        print("✅ AudioInputActor启动成功")
        print("监控5秒...")
        
        audio_count = 0
        for i in range(50):  # 5秒
            try:
                audio_data = ray.get(audio_actor.get_audio_data.remote())
                
                if audio_data:
                    audio_count += 1
                    
                    # 分析音频数据
                    audio_int16 = np.frombuffer(audio_data, dtype=np.int16)
                    max_amplitude = np.abs(audio_int16).max()
                    
                    if i % 10 == 0:  # 每秒显示一次
                        print(f"收到音频数据 #{audio_count}: {len(audio_data)} 字节, 最大幅度: {max_amplitude}")
                
                time.sleep(0.1)
                
            except Exception as e:
                print(f"获取音频数据出错: {e}")
                break
        
        # 停止录音
        ray.get(audio_actor.stop_recording.remote())
        print(f"总共收到 {audio_count} 个音频块")
        
        return audio_count > 0
        
    except Exception as e:
        print(f"AudioInputActor测试失败: {e}")
        return False
    finally:
        if ray.is_initialized():
            ray.shutdown()

def check_audio_permissions():
    """检查音频权限"""
    print("\n=== 检查音频权限 ===")
    
    try:
        audio = pyaudio.PyAudio()
        
        # 尝试获取默认输入设备
        default_input = audio.get_default_input_device_info()
        print(f"默认输入设备: {default_input['name']}")
        
        # 尝试打开一个简单的音频流
        test_stream = audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=44100,
            input=True,
            frames_per_buffer=1024
        )
        
        # 读取一小块数据
        data = test_stream.read(1024, exception_on_overflow=False)
        
        test_stream.close()
        audio.terminate()
        
        print("✅ 音频权限正常")
        return True
        
    except Exception as e:
        print(f"❌ 音频权限问题: {e}")
        return False

def main():
    """主函数"""
    print("详细音频调试工具")
    print("=" * 50)
    
    # 1. 检查音频权限
    permissions_ok = check_audio_permissions()
    
    # 2. 直接音频测试
    direct_test_ok = test_direct_audio_with_config()
    
    # 3. AudioInputActor测试
    actor_test_ok = test_audio_input_actor_directly()
    
    print("\n" + "=" * 50)
    print("诊断结果:")
    print(f"音频权限: {'✅' if permissions_ok else '❌'}")
    print(f"直接音频测试: {'✅' if direct_test_ok else '❌'}")
    print(f"AudioInputActor测试: {'✅' if actor_test_ok else '❌'}")
    
    if not permissions_ok:
        print("\n建议:")
        print("1. 检查Windows隐私设置中的麦克风权限")
        print("2. 确保Python有访问麦克风的权限")
    elif not direct_test_ok:
        print("\n建议:")
        print("1. 检查麦克风是否被其他应用占用")
        print("2. 尝试调整麦克风音量")
        print("3. 检查音频驱动程序")
    elif not actor_test_ok:
        print("\n建议:")
        print("1. 检查Ray配置")
        print("2. 检查AudioInputActor实现")
    else:
        print("\n✅ 所有测试通过！音频输入应该正常工作。")

if __name__ == "__main__":
    main()
