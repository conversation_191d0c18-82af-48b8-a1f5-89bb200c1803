"""
Web search tool for the audio agent.

This tool provides web search capabilities using various search engines.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional
import aiohttp
from ..base import BaseTool, ToolResult, ToolError, ToolParameter, ParameterType

logger = logging.getLogger(__name__)


class WebSearchTool(BaseTool):
    """
    Web search tool that can search the internet for information.
    
    This tool uses DuckDuckGo search API to provide web search results
    without requiring API keys.
    """
    
    def __init__(self):
        """Initialize the web search tool."""
        super().__init__(
            name="web_search",
            description="Search the web for current information. Use this when you need to find recent news, facts, or information that might not be in your training data."
        )
        
        # Add parameters
        self.add_parameter(ToolParameter(
            name="query",
            type=ParameterType.STRING,
            description="The search query to look for",
            required=True
        ))
        
        self.add_parameter(ToolParameter(
            name="max_results",
            type=ParameterType.INTEGER,
            description="Maximum number of search results to return (1-10)",
            required=False,
            default=5
        ))
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute the web search.
        
        Args:
            query: Search query string
            max_results: Maximum number of results (default: 5)
            
        Returns:
            ToolResult with search results
        """
        try:
            # Validate parameters
            validated_params = self.validate_parameters(**kwargs)
            query = validated_params["query"]
            max_results = min(validated_params.get("max_results", 5), 10)
            
            logger.info(f"Searching web for: {query}")
            
            # Perform the search
            search_results = await self._search_duckduckgo(query, max_results)
            
            if not search_results:
                return ToolResult(
                    success=True,
                    data={
                        "query": query,
                        "results": [],
                        "message": "No search results found"
                    }
                )
            
            return ToolResult(
                success=True,
                data={
                    "query": query,
                    "results": search_results,
                    "count": len(search_results)
                }
            )
            
        except Exception as e:
            logger.error(f"Web search error: {e}")
            return ToolResult(
                success=False,
                error=f"Search failed: {str(e)}"
            )
    
    async def _search_duckduckgo(self, query: str, max_results: int) -> List[Dict[str, Any]]:
        """
        Search using DuckDuckGo instant answer API.
        
        Args:
            query: Search query
            max_results: Maximum results to return
            
        Returns:
            List of search results
        """
        try:
            # DuckDuckGo instant answer API
            url = "https://api.duckduckgo.com/"
            params = {
                "q": query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._parse_duckduckgo_results(data, max_results)
                    else:
                        logger.warning(f"DuckDuckGo API returned status {response.status}")
                        return []
                        
        except asyncio.TimeoutError:
            logger.error("Search request timed out")
            return []
        except Exception as e:
            logger.error(f"Error searching DuckDuckGo: {e}")
            return []
    
    def _parse_duckduckgo_results(self, data: Dict[str, Any], max_results: int) -> List[Dict[str, Any]]:
        """
        Parse DuckDuckGo API response.
        
        Args:
            data: API response data
            max_results: Maximum results to return
            
        Returns:
            List of parsed search results
        """
        results = []
        
        # Check for instant answer
        if data.get("Abstract"):
            results.append({
                "title": data.get("Heading", "Instant Answer"),
                "snippet": data.get("Abstract", ""),
                "url": data.get("AbstractURL", ""),
                "source": data.get("AbstractSource", "DuckDuckGo")
            })
        
        # Check for related topics
        for topic in data.get("RelatedTopics", [])[:max_results-len(results)]:
            if isinstance(topic, dict) and topic.get("Text"):
                results.append({
                    "title": topic.get("Text", "").split(" - ")[0] if " - " in topic.get("Text", "") else "Related Topic",
                    "snippet": topic.get("Text", ""),
                    "url": topic.get("FirstURL", ""),
                    "source": "DuckDuckGo"
                })
        
        # If no results, create a simple response
        if not results and data.get("Answer"):
            results.append({
                "title": "Answer",
                "snippet": data.get("Answer", ""),
                "url": "",
                "source": "DuckDuckGo"
            })
        
        return results[:max_results]
