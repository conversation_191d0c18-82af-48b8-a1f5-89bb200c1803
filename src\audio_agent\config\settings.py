"""
Configuration settings for the audio agent.

This module provides configuration classes using Pydantic for type safety and validation.
"""

import uuid
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
import pyaudio


class AudioConfig(BaseSettings):
    """Audio configuration settings."""
    chunk: int = Field(default=3200, description="Audio chunk size")
    format: str = Field(default="pcm", description="Audio format")
    channels: int = Field(default=1, description="Number of audio channels")
    sample_rate: int = Field(description="Audio sample rate")
    bit_size: int = Field(description="Audio bit size")


class InputAudioConfig(BaseSettings):
    """Input audio configuration."""
    chunk: int = Field(default=3200, description="Audio chunk size")
    format: str = Field(default="pcm", description="Audio format")
    channels: int = Field(default=1, description="Number of audio channels")
    sample_rate: int = Field(default=16000, description="Input audio sample rate")
    bit_size: int = Field(default=pyaudio.paInt16, description="Input audio bit size")

    # Voice Activity Detection (VAD) settings - using professional VAD
    vad_enabled: bool = Field(default=True, description="Enable voice activity detection")
    vad_model: str = Field(default="silero", description="VAD model to use: 'silero', 'webrtc', or 'simple'")
    vad_threshold: float = Field(default=0.5, description="VAD confidence threshold (0.0-1.0)")
    vad_window_size_ms: int = Field(default=30, description="VAD window size in milliseconds")
    vad_min_speech_duration_ms: int = Field(default=100, description="Minimum speech duration to trigger interrupt")

    model_config = {
        "env_prefix": "AUDIO_AGENT_INPUT_AUDIO_",
        "case_sensitive": False,
    }


class OutputAudioConfig(BaseSettings):
    """Output audio configuration."""
    chunk: int = Field(default=3200, description="Audio chunk size")
    format: str = Field(default="pcm", description="Audio format")
    channels: int = Field(default=1, description="Number of audio channels")
    sample_rate: int = Field(default=24000, description="Output audio sample rate")
    bit_size: int = Field(default=pyaudio.paFloat32, description="Output audio bit size")

    model_config = {
        "env_prefix": "AUDIO_AGENT_OUTPUT_AUDIO_",
        "case_sensitive": False,
    }


class InterruptConfig(BaseSettings):
    """Audio playback interrupt configuration."""
    enabled: bool = Field(default=True, description="Enable audio playback interruption")
    response_delay_ms: int = Field(default=50, description="Interrupt response delay in milliseconds")
    min_speech_duration_ms: int = Field(default=200, description="Minimum speech duration to trigger interrupt")
    debounce_time_ms: int = Field(default=100, description="Debounce time to avoid false interrupts")

    model_config = {
        "env_prefix": "AUDIO_AGENT_INTERRUPT_",
        "case_sensitive": False,
    }


class WebSocketConfig(BaseSettings):
    """WebSocket connection configuration."""
    base_url: str = Field(
        default="wss://openspeech.bytedance.com/api/v3/realtime/dialogue",
        description="WebSocket base URL"
    )
    app_id: str = Field(default="", description="API App ID")
    access_key: str = Field(default="", description="API Access Key")
    resource_id: str = Field(default="volc.speech.dialog", description="API Resource ID")
    app_key: str = Field(default="PlgvMymc7f3tQnJ6", description="API App Key")
    connect_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Connection ID")

    model_config = {
        "env_prefix": "AUDIO_AGENT_WEBSOCKET_",
        "case_sensitive": False,
    }

    @property
    def headers(self) -> Dict[str, str]:
        """Get WebSocket headers."""
        return {
            "X-Api-App-ID": self.app_id,
            "X-Api-Access-Key": self.access_key,
            "X-Api-Resource-Id": self.resource_id,
            "X-Api-App-Key": self.app_key,
            "X-Api-Connect-Id": self.connect_id,
        }


class DialogConfig(BaseSettings):
    """Dialog configuration settings."""
    bot_name: str = Field(default="豆包", description="Bot name")
    system_role: str = Field(
        default="你使用活泼灵动的女声，性格开朗，热爱生活。",
        description="System role description"
    )
    speaking_style: str = Field(
        default="你的说话风格简洁明了，语速适中，语调自然。",
        description="Speaking style description"
    )
    strict_audit: bool = Field(default=False, description="Enable strict audit")
    audit_response: str = Field(
        default="支持客户自定义安全审核回复话术。",
        description="Audit response message"
    )
    greeting_message: str = Field(
        default="你好，我是豆包，有什么可以帮助你的？",
        description="Initial greeting message sent to user"
    )

    model_config = {
        "env_prefix": "AUDIO_AGENT_SESSION_DIALOG_",
        "case_sensitive": False,
    }


class TTSConfig(BaseSettings):
    """Text-to-Speech configuration."""
    audio_config: OutputAudioConfig = Field(default_factory=OutputAudioConfig)


class SessionConfig(BaseSettings):
    """Session configuration."""
    tts: TTSConfig = Field(default_factory=TTSConfig)
    dialog: DialogConfig = Field(default_factory=DialogConfig)


class RayConfig(BaseSettings):
    """Ray configuration settings."""
    address: str = Field(default="auto", description="Ray address")
    num_cpus: Optional[int] = Field(default=None, description="Number of CPUs for Ray")
    num_gpus: Optional[int] = Field(default=None, description="Number of GPUs for Ray")
    object_store_memory: Optional[int] = Field(default=None, description="Object store memory")
    dashboard_host: str = Field(default="127.0.0.1", description="Ray dashboard host")
    dashboard_port: int = Field(default=8265, description="Ray dashboard port")

    model_config = {
        "env_prefix": "AUDIO_AGENT_RAY_",
        "case_sensitive": False,
    }


class IntelligentAgentConfig(BaseSettings):
    """Intelligent agent configuration."""
    enabled: bool = Field(default=True, description="Enable intelligent agent features")
    tool_calling_enabled: bool = Field(default=True, description="Enable tool calling")
    knowledge_retrieval_enabled: bool = Field(default=True, description="Enable knowledge retrieval")
    tool_execution_timeout: float = Field(default=30.0, description="Tool execution timeout in seconds")
    max_conversation_history: int = Field(default=50, description="Maximum conversation history to keep")

    model_config = {
        "env_prefix": "AUDIO_AGENT_INTELLIGENT_",
        "case_sensitive": False,
    }


class ToolConfig(BaseSettings):
    """Tool system configuration."""
    # Use string for environment variable, convert to list in property
    enabled_tools_str: str = Field(
        default="web_search,calculator,datetime",
        description="Comma-separated list of enabled tool names",
        alias="enabled_tools"
    )
    web_search_enabled: bool = Field(default=True, description="Enable web search tool")
    calculator_enabled: bool = Field(default=True, description="Enable calculator tool")
    datetime_enabled: bool = Field(default=True, description="Enable datetime tool")

    # Web search specific settings
    search_max_results: int = Field(default=5, description="Maximum search results to return")
    search_timeout: float = Field(default=10.0, description="Search request timeout in seconds")

    @property
    def enabled_tools(self) -> List[str]:
        """Get enabled tools as a list."""
        if not self.enabled_tools_str:
            return []
        return [tool.strip() for tool in self.enabled_tools_str.split(',') if tool.strip()]

    model_config = {
        "env_prefix": "AUDIO_AGENT_TOOLS_",
        "case_sensitive": False,
    }


class KnowledgeConfig(BaseSettings):
    """Knowledge system configuration."""
    enabled: bool = Field(default=True, description="Enable knowledge system")
    knowledge_base_path: str = Field(default="./knowledge", description="Path to knowledge base files")
    max_retrieval_results: int = Field(default=5, description="Maximum knowledge retrieval results")
    similarity_threshold: float = Field(default=0.7, description="Similarity threshold for knowledge retrieval")

    model_config = {
        "env_prefix": "AUDIO_AGENT_KNOWLEDGE_",
        "case_sensitive": False,
    }


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    level: str = Field(default="INFO", description="Logging level (DEBUG, INFO, WARNING, ERROR)")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )
    enable_file_logging: bool = Field(default=False, description="Enable logging to file")
    log_file: str = Field(default="audio_agent.log", description="Log file path")

    # Specific logger levels
    audio_debug: bool = Field(default=False, description="Enable debug logging for audio processing")
    websocket_debug: bool = Field(default=False, description="Enable debug logging for WebSocket")
    event_debug: bool = Field(default=False, description="Enable debug logging for events")

    model_config = SettingsConfigDict(
        env_prefix="AUDIO_AGENT_LOGGING_",
        case_sensitive=False,
    )


class AudioAgentConfig(BaseSettings):
    """Main configuration for the audio agent."""
    websocket: WebSocketConfig = Field(default_factory=WebSocketConfig)
    input_audio: InputAudioConfig = Field(default_factory=InputAudioConfig)
    output_audio: OutputAudioConfig = Field(default_factory=OutputAudioConfig)
    session: SessionConfig = Field(default_factory=SessionConfig)
    ray: RayConfig = Field(default_factory=RayConfig)
    interrupt: InterruptConfig = Field(default_factory=InterruptConfig)
    intelligent_agent: IntelligentAgentConfig = Field(default_factory=IntelligentAgentConfig)
    tools: ToolConfig = Field(default_factory=ToolConfig)
    knowledge: KnowledgeConfig = Field(default_factory=KnowledgeConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)

    model_config = {
        "env_prefix": "AUDIO_AGENT_",
        "case_sensitive": False,
    }


# Global configuration instance
config = None


def get_config() -> AudioAgentConfig:
    """Get the global configuration instance."""
    global config
    if config is None:
        # Load environment variables first
        from dotenv import load_dotenv
        load_dotenv()

        # Create configuration with environment variables
        config = AudioAgentConfig()
    return config


def update_config(**kwargs) -> AudioAgentConfig:
    """Update configuration with new values."""
    global config
    current_config = get_config()

    # Update nested configurations
    config_dict = current_config.model_dump()
    for key, value in kwargs.items():
        if key in config_dict and isinstance(value, dict):
            # Update nested config
            config_dict[key].update(value)
        else:
            config_dict[key] = value

    config = AudioAgentConfig(**config_dict)
    return config
