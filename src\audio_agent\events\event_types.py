"""
Event type definitions following Volcengine's official event system.

Based on: https://www.volcengine.com/docs/6561/1594356
"""

from enum import IntEnum
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime


class EventType(IntEnum):
    """Event type constants following Volcengine official documentation."""

    # Connection events (50-59)
    CONNECTION_STARTED = 50
    CONNECTION_FAILED = 51
    CONNECTION_FINISHED = 52

    # Session events (150-159)
    SESSION_STARTED = 150
    SESSION_FINISHED = 152
    SESSION_FAILED = 153

    # TTS events (350-359)
    TTS_SENTENCE_START = 350
    TTS_SENTENCE_END = 351
    TTS_RESPONSE = 352
    TTS_ENDED = 359

    # ASR events (450-459)
    ASR_INFO = 450
    ASR_RESPONSE = 451
    ASR_ENDED = 459

    # Chat events (550-559)
    CHAT_RESPONSE = 550
    CHAT_ENDED = 559

    # Custom LLM enhancement events (for internal processing)
    LLM_START = 1000
    LLM_TOOL_CALL = 1001
    LLM_TOOL_RESULT = 1002
    LLM_FINAL_RESULT = 1003
    INTENT_RECOGNITION = 1010


@dataclass
class BaseEvent:
    """Base event class."""
    event_type: EventType
    timestamp: datetime
    session_id: str
    event_id: str
    data: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary."""
        return {
            "event_type": self.event_type.value,
            "timestamp": self.timestamp.isoformat(),
            "session_id": self.session_id,
            "event_id": self.event_id,
            "data": self.data
        }


@dataclass
class ServerEvent(BaseEvent):
    """Server-side event from Volcengine."""
    message_type: str
    payload: Optional[Union[Dict[str, Any], bytes]] = None
    
    @classmethod
    def from_websocket_message(cls, message: Dict[str, Any], session_id: str) -> 'ServerEvent':
        """Create ServerEvent from WebSocket message."""
        import uuid
        from datetime import datetime

        # Extract event type from message
        event_type = EventType.TTS_RESPONSE  # Default to most common audio event
        if 'event' in message:
            try:
                event_type = EventType(message['event'])
            except ValueError:
                # If event ID is not recognized, try to infer from message type
                message_type = message.get('message_type', '')
                if message_type == 'SERVER_ACK':
                    # Check if it contains audio data
                    if message.get('payload_msg') or message.get('payload'):
                        event_type = EventType.TTS_RESPONSE  # Event 352
                    else:
                        event_type = EventType.CONNECTION_STARTED  # Event 50
                else:
                    event_type = EventType.TTS_RESPONSE

        return cls(
            event_type=event_type,
            timestamp=datetime.now(),
            session_id=session_id,
            event_id=str(uuid.uuid4()),
            data=message,
            message_type=message.get('message_type', 'unknown'),
            payload=message.get('payload') or message.get('payload_msg')
        )


@dataclass
class ClientEvent(BaseEvent):
    """Client-side event to send to Volcengine."""
    pass


@dataclass
class DialogEvent(BaseEvent):
    """Dialog-related event."""
    user_input: Optional[str] = None
    ai_response: Optional[str] = None
    intent: Optional[str] = None
    confidence: Optional[float] = None


@dataclass
class TTSEvent(BaseEvent):
    """TTS-related event."""
    text: Optional[str] = None
    audio_data: Optional[bytes] = None
    audio_format: Optional[str] = None
    sample_rate: Optional[int] = None


@dataclass
class ASREvent(BaseEvent):
    """ASR-related event."""
    audio_data: Optional[bytes] = None
    transcription: Optional[str] = None
    confidence: Optional[float] = None
    is_final: bool = False


@dataclass
class LLMEvent(BaseEvent):
    """LLM-related event for enhanced processing."""
    user_message: Optional[str] = None
    ai_response: Optional[str] = None
    tool_calls: Optional[list] = None
    tool_results: Optional[list] = None
    intent: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
