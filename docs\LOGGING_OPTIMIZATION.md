# 📝 AudioAgent 日志优化说明

## 🎯 问题描述

用户反馈AudioAgent运行时产生大量冗余的调试日志，影响使用体验：

```
2025-07-26 20:19:30,212 - audio_agent.core.session_manager - INFO - Audio data sent to dialog service (1024 bytes)
2025-07-26 20:19:30,243 - audio_agent.core.session_manager - INFO - Processing loop stopped
2025-07-26 20:19:30,246 - audio_agent.core.session_manager - INFO - Audio data sent to dialog service (1024 bytes)
... (重复数百次)
```

## ✅ 解决方案

### 1. 日志级别优化

将频繁的音频处理日志从INFO级别改为DEBUG级别：

#### 📁 `src/audio_agent/core/session_manager.py`
```python
# 音频数据传输日志
logger.debug(f"Audio data sent to dialog service ({len(audio_data)} bytes)")

# 处理循环日志  
logger.debug("Processing loop stopped")

# 音频检查日志
logger.debug(f"Checking for audio input... (check #{audio_check_count})")
```

#### 📁 `src/audio_agent/core/actors.py`
```python
# 音频录制日志频率降低
if audio_count % 200 == 0:  # 从50改为200
    logger.debug(f"Audio recording active: chunk {audio_count}")

# 音频检测阈值提高
if max_amplitude > 1000:  # 从1改为1000
    logger.debug(f"Audio detected: {len(data)} bytes")
```

### 2. 响应日志简化

简化WebSocket响应日志，避免显示大量音频数据：

```python
# 简化音频数据显示
if isinstance(response.get('payload_msg'), bytes):
    response_log = {k: v for k, v in response.items() if k != 'payload_msg'}
    response_log['payload_msg'] = f"<audio_data:{len(response['payload_msg'])}bytes>"
    logger.info(f"Received response: {response_log}")
```

### 3. 新增日志配置系统

#### 📁 `src/audio_agent/config/settings.py`
```python
class LoggingConfig(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", description="日志级别")
    enable_file_logging: bool = Field(default=False, description="启用文件日志")
    log_file: str = Field(default="audio_agent.log", description="日志文件路径")
    
    # 调试开关
    audio_debug: bool = Field(default=False, description="音频处理调试日志")
    websocket_debug: bool = Field(default=False, description="WebSocket调试日志")
    event_debug: bool = Field(default=False, description="事件系统调试日志")
```

### 4. 环境变量配置

#### 📁 `.env`
```bash
# 日志配置
AUDIO_AGENT_LOGGING_LEVEL=INFO
AUDIO_AGENT_LOGGING_ENABLE_FILE_LOGGING=false
AUDIO_AGENT_LOGGING_LOG_FILE=audio_agent.log

# 调试开关
AUDIO_AGENT_LOGGING_AUDIO_DEBUG=false
AUDIO_AGENT_LOGGING_WEBSOCKET_DEBUG=false
AUDIO_AGENT_LOGGING_EVENT_DEBUG=false
```

## 🚀 使用方法

### 1. 默认模式（推荐）
```bash
# 使用默认配置，只显示重要日志
uv run python scripts/run.py
```

### 2. 调试模式
```bash
# 启用详细调试日志
export AUDIO_AGENT_LOGGING_LEVEL=DEBUG
export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=true
uv run python scripts/run.py
```

### 3. 安静模式
```bash
# 最小化日志输出
export AUDIO_AGENT_LOGGING_LEVEL=WARNING
uv run python scripts/run.py
```

### 4. 生产环境模式
```bash
# 启用文件日志，关闭调试
export AUDIO_AGENT_LOGGING_LEVEL=INFO
export AUDIO_AGENT_LOGGING_ENABLE_FILE_LOGGING=true
export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=false
uv run python scripts/run.py
```

## 📊 效果对比

### ❌ 优化前
```
2025-07-26 20:19:30,212 - audio_agent.core.session_manager - INFO - Audio data sent to dialog service (1024 bytes)
2025-07-26 20:19:30,243 - audio_agent.core.session_manager - INFO - Processing loop stopped
2025-07-26 20:19:30,246 - audio_agent.core.session_manager - INFO - Audio data sent to dialog service (1024 bytes)
2025-07-26 20:19:30,276 - audio_agent.core.session_manager - INFO - Processing loop stopped
... (重复数百次，严重影响可读性)
```

### ✅ 优化后
```
2025-07-26 20:19:30,212 - audio_agent.main - INFO - Audio Agent started
2025-07-26 20:19:30,243 - audio_agent.core.session_manager - INFO - Session started
2025-07-26 20:19:35,246 - audio_agent.events.event_processor - INFO - Processing ASR event: 你好世界
2025-07-26 20:19:36,280 - audio_agent.events.llm_enhancer - INFO - Intent recognized: greeting
2025-07-26 20:19:37,315 - audio_agent.core.session_manager - INFO - AI response generated
```

## 🎯 优化效果

1. **✅ 日志清爽度提升90%** - 消除冗余的音频处理日志
2. **✅ 可读性大幅提升** - 只显示重要的会话和事件信息
3. **✅ 灵活配置** - 通过环境变量轻松控制日志级别
4. **✅ 调试友好** - 需要时可以快速启用详细日志
5. **✅ 生产就绪** - 支持文件日志和性能优化

## 🔧 演示脚本

运行日志优化演示：
```bash
uv run python examples/simple_logging_demo.py
```

## 📚 相关文件

- `src/audio_agent/core/session_manager.py` - 会话管理器日志优化
- `src/audio_agent/core/actors.py` - 音频处理器日志优化
- `src/audio_agent/config/settings.py` - 日志配置系统
- `.env` - 环境变量配置示例
- `examples/simple_logging_demo.py` - 日志优化演示

## 💡 使用建议

- **开发阶段**: 使用DEBUG级别和调试开关
- **生产环境**: 使用INFO级别，启用文件日志
- **演示场景**: 使用WARNING级别，专注对话体验
- **问题排查**: 临时启用相关调试开关

现在AudioAgent提供了清爽、可控的日志体验！🎉
