# 开发指南

本文档提供了Audio Agent项目的详细开发指南，包括架构设计、开发流程和最佳实践。

## 🏗️ 架构详解

### Ray Actor模型

项目采用Ray的Actor模型实现并行处理，每个Actor负责特定的功能：

#### AudioInputActor
- **职责**：音频输入处理
- **特性**：
  - 独立线程录制音频
  - 音频数据缓冲
  - 实时数据流管理
- **接口**：
  ```python
  start_recording() -> bool
  stop_recording() -> bool
  get_audio_data() -> Optional[bytes]
  is_recording_active() -> bool
  ```

#### AudioOutputActor
- **职责**：音频输出处理
- **特性**：
  - 独立线程播放音频
  - 音频队列管理
  - 实时播放控制
- **接口**：
  ```python
  start_playback() -> bool
  stop_playback() -> bool
  queue_audio_data(data: bytes) -> bool
  is_playback_active() -> bool
  ```

#### DialogClientActor
- **职责**：WebSocket通信
- **特性**：
  - 异步WebSocket连接
  - 协议编解码
  - 消息队列管理
- **接口**：
  ```python
  connect(session_id: str) -> bool
  disconnect() -> bool
  send_audio_data(data: bytes) -> bool
  receive_message() -> Optional[Dict[str, Any]]
  ```

#### SessionManager
- **职责**：会话协调
- **特性**：
  - Actor生命周期管理
  - 数据流协调
  - 错误处理和恢复
- **接口**：
  ```python
  start() -> None
  stop() -> None
  is_running() -> bool
  ```

### 数据流设计

```mermaid
graph TD
    A[用户语音] --> B[AudioInputActor]
    B --> C[音频数据队列]
    C --> D[DialogClientActor]
    D --> E[WebSocket服务]
    E --> F[AI处理]
    F --> G[响应数据]
    G --> D
    D --> H[AudioOutputActor]
    H --> I[扬声器输出]
```

## 🔧 开发环境

### 必需工具

1. **Python 3.10+**
2. **uv包管理器**
3. **Git**
4. **音频设备**（开发测试用）

### 环境设置

```bash
# 1. 克隆项目
git clone <repository-url>
cd audio-agent

# 2. 安装uv（如果未安装）
curl -LsSf https://astral.sh/uv/install.sh | sh

# 3. 创建开发环境
uv sync --group dev

# 4. 激活环境
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows

# 5. 验证安装
uv run python scripts/verify_basic.py
```

### IDE配置

#### VS Code推荐设置

创建 `.vscode/settings.json`：

```json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.mypyEnabled": true,
    "python.linting.ruffEnabled": true,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests/"],
    "files.exclude": {
        "**/__pycache__": true,
        "**/.pytest_cache": true,
        "**/.mypy_cache": true
    }
}
```

#### PyCharm配置

1. 设置Python解释器为 `.venv/bin/python`
2. 启用类型检查（mypy）
3. 配置代码格式化（black）
4. 设置测试运行器（pytest）

## 🧪 测试策略

### 测试层次

1. **单元测试**：测试单个组件功能
2. **集成测试**：测试组件间协作
3. **端到端测试**：测试完整流程
4. **性能测试**：测试系统性能

### 测试工具

- **pytest**：测试框架
- **pytest-asyncio**：异步测试支持
- **unittest.mock**：模拟对象
- **Ray测试工具**：Ray actor测试

### 测试最佳实践

```python
# 1. 使用fixture管理测试资源
@pytest.fixture
def ray_context():
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    yield
    # 清理资源

# 2. 模拟外部依赖
@patch('pyaudio.PyAudio')
def test_audio_actor(mock_pyaudio):
    # 测试逻辑
    pass

# 3. 异步测试
@pytest.mark.asyncio
async def test_async_function():
    result = await async_function()
    assert result is not None
```

## 📝 代码规范

### 代码风格

- **格式化**：使用black（行长度88字符）
- **导入排序**：使用isort
- **类型提示**：所有公共接口必须有类型提示
- **文档字符串**：使用Google风格的docstring

### 命名规范

- **类名**：PascalCase（如 `AudioInputActor`）
- **函数名**：snake_case（如 `start_recording`）
- **常量**：UPPER_SNAKE_CASE（如 `DEFAULT_SAMPLE_RATE`）
- **私有方法**：以下划线开头（如 `_internal_method`）

### 代码示例

```python
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class ExampleActor:
    """示例Actor类。
    
    这个类展示了标准的代码风格和文档规范。
    
    Attributes:
        config: 配置对象
        is_active: 是否处于活动状态
    """
    
    def __init__(self, config: Dict[str, Any]) -> None:
        """初始化Actor。
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.is_active = False
        logger.info("ExampleActor initialized")
    
    async def start(self) -> bool:
        """启动Actor。
        
        Returns:
            启动是否成功
            
        Raises:
            RuntimeError: 当启动失败时
        """
        try:
            self.is_active = True
            logger.info("ExampleActor started")
            return True
        except Exception as e:
            logger.error(f"Failed to start ExampleActor: {e}")
            raise RuntimeError(f"Start failed: {e}")
    
    def _internal_method(self) -> None:
        """内部方法示例。"""
        pass
```

## 🚀 发布流程

### 版本管理

使用语义化版本控制（SemVer）：
- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 发布检查清单

- [ ] 所有测试通过
- [ ] 代码覆盖率 > 80%
- [ ] 文档更新完整
- [ ] 版本号已更新
- [ ] CHANGELOG已更新
- [ ] 性能测试通过

### 发布命令

```bash
# 1. 运行完整测试
uv run pytest tests/ -v --cov=src/

# 2. 代码质量检查
uv run black --check src/ tests/
uv run isort --check-only src/ tests/
uv run mypy src/
uv run ruff check src/ tests/

# 3. 构建包
uv build

# 4. 发布（如果配置了PyPI）
uv publish
```

## 🐛 调试技巧

### Ray调试

1. **Ray集群配置**：
   ```python
   from audio_agent.config import update_config

   # 本地开发配置
   update_config(
       ray={
           "address": "auto",           # 自动启动本地集群
           "num_cpus": 4,              # 限制CPU使用
           "dashboard_host": "127.0.0.1",
           "dashboard_port": 8265
       }
   )

   # 连接远程集群
   update_config(
       ray={
           "address": "ray://head-node:10001",  # 远程集群地址
           "dashboard_host": "0.0.0.0",        # 允许外部访问
           "dashboard_port": 8265
       }
   )

   # 调试模式
   update_config(
       ray={
           "address": "local",         # 强制本地模式
           "num_cpus": 2,             # 限制资源
           "dashboard_port": 8266      # 避免端口冲突
       }
   )
   ```

2. **启用Ray仪表板**：
   ```python
   # 通过配置启用
   update_config(
       ray={
           "dashboard_host": "0.0.0.0",
           "dashboard_port": 8265
       }
   )

   # 或直接初始化
   ray.init(dashboard_host="0.0.0.0", dashboard_port=8265)
   ```

2. **查看Actor状态**：
   ```python
   print(ray.actors())
   ```

3. **日志调试**：
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

### 音频调试

1. **检查音频设备**：
   ```python
   import pyaudio
   p = pyaudio.PyAudio()
   for i in range(p.get_device_count()):
       print(p.get_device_info_by_index(i))
   ```

2. **音频数据验证**：
   ```python
   # 保存音频数据到文件进行分析
   with open("debug_audio.raw", "wb") as f:
       f.write(audio_data)
   ```

### 网络调试

1. **WebSocket连接测试**：
   ```bash
   # 使用wscat测试WebSocket连接
   wscat -c "wss://your-websocket-url"
   ```

2. **协议数据分析**：
   ```python
   # 打印协议数据
   print(f"Header: {header.hex()}")
   print(f"Payload: {payload}")
   ```

## 📚 学习资源

### 相关技术文档

- [Ray官方文档](https://docs.ray.io/)
- [uv用户指南](https://github.com/astral-sh/uv)
- [Pydantic文档](https://pydantic-docs.helpmanual.io/)
- [PyAudio文档](https://people.csail.mit.edu/hubert/pyaudio/)

### 推荐阅读

- 《Python并发编程》
- 《分布式系统设计》
- 《音频信号处理基础》
- 《WebSocket协议详解》

---

**维护者**: 苏兆强  
**最后更新**: 2025-07-24
