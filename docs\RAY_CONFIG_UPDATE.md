# Ray配置更新说明

本文档说明了新增的Ray `address`配置选项及其使用方法。

## 🆕 新增配置

### `ray.address` 配置项

**类型**: `str`  
**默认值**: `"auto"`  
**描述**: Ray集群连接地址

## 🎯 配置选项

### 1. 自动模式（默认）
```python
ray = {
    "address": "auto"  # 自动启动本地Ray集群
}
```

**特点**：
- 自动检测并启动本地Ray集群
- 如果已有集群运行，则连接到现有集群
- 适合大多数开发和生产场景

### 2. 本地模式
```python
ray = {
    "address": "local"  # 强制本地模式
}
```

**特点**：
- 强制启动本地Ray集群
- 不会连接到远程集群
- 适合开发调试

### 3. 远程集群模式
```python
ray = {
    "address": "ray://head-node-ip:10001"  # 连接到远程集群
}
```

**特点**：
- 连接到指定的远程Ray集群
- 不启动本地集群
- 适合分布式部署

### 4. 手动管理模式
```python
ray = {
    "address": None  # 不自动初始化Ray
}
```

**特点**：
- 不自动初始化Ray
- 需要手动管理Ray生命周期
- 适合高级用户

## 🚀 使用场景

### 场景1：本地开发
```python
from audio_agent.config import update_config

update_config(
    ray={
        "address": "auto",           # 自动模式
        "num_cpus": 4,              # 限制CPU使用
        "dashboard_port": 8265,     # 仪表板端口
        "dashboard_host": "127.0.0.1"  # 本地访问
    }
)
```

### 场景2：分布式部署
```python
# Head节点配置
update_config(
    ray={
        "address": "auto",              # 启动head节点
        "num_cpus": 16,                # 集群总CPU
        "num_gpus": 4,                 # 集群总GPU
        "dashboard_host": "0.0.0.0",   # 允许外部访问
        "dashboard_port": 8265
    }
)

# Worker节点配置
update_config(
    ray={
        "address": "ray://head-node-ip:10001",  # 连接到head节点
        "dashboard_host": "0.0.0.0"
    }
)
```

### 场景3：Kubernetes部署
```python
update_config(
    ray={
        "address": "ray://ray-head-service:10001",  # K8s服务名
        "dashboard_host": "0.0.0.0",
        "dashboard_port": 8265
    }
)
```

### 场景4：开发调试
```python
update_config(
    ray={
        "address": "local",         # 强制本地模式
        "num_cpus": 2,             # 限制资源
        "dashboard_port": 8266,     # 避免端口冲突
        "object_store_memory": 1000000000  # 1GB内存
    }
)
```

## 🔧 环境变量支持

可以通过环境变量设置Ray地址：

```bash
export AUDIO_AGENT_RAY__ADDRESS="ray://head-node:10001"
export AUDIO_AGENT_RAY__DASHBOARD_HOST="0.0.0.0"
export AUDIO_AGENT_RAY__DASHBOARD_PORT="8265"
```

## 📊 配置验证

系统会自动验证Ray配置：

```python
from audio_agent.config import get_config

config = get_config()
print(f"Ray地址: {config.ray.address}")
print(f"仪表板: http://{config.ray.dashboard_host}:{config.ray.dashboard_port}")
```

## 🐛 故障排除

### 问题1：连接远程集群失败
```python
# 检查网络连接
import socket
try:
    sock = socket.create_connection(("head-node-ip", 10001), timeout=5)
    print("✅ 网络连接正常")
    sock.close()
except Exception as e:
    print(f"❌ 网络连接失败: {e}")
```

### 问题2：端口冲突
```python
# 使用不同端口
update_config(
    ray={
        "address": "auto",
        "dashboard_port": 8266  # 使用其他端口
    }
)
```

### 问题3：资源不足
```python
# 限制资源使用
update_config(
    ray={
        "address": "auto",
        "num_cpus": 2,                    # 减少CPU需求
        "object_store_memory": 1000000000  # 减少内存需求
    }
)
```

## 📚 相关文档

- [README.md](../README.md#性能调优) - 性能调优部分
- [API.md](API.md#rayconfig) - RayConfig API文档
- [FAQ.md](FAQ.md#ray相关) - Ray相关问题解答
- [DEVELOPMENT.md](DEVELOPMENT.md#ray调试) - Ray调试指南

## 🔄 迁移指南

如果您之前使用的是旧版本配置，需要进行以下更新：

### 旧配置方式
```python
# 旧方式：直接调用ray.init()
import ray
ray.init(num_cpus=4, dashboard_port=8265)
```

### 新配置方式
```python
# 新方式：通过配置系统
from audio_agent.config import update_config

update_config(
    ray={
        "address": "auto",      # 新增：指定连接方式
        "num_cpus": 4,
        "dashboard_port": 8265
    }
)
```

## 💡 最佳实践

1. **开发环境**：使用 `"address": "auto"` 或 `"local"`
2. **生产环境**：根据部署架构选择合适的地址
3. **调试时**：使用 `"local"` 模式避免干扰
4. **分布式部署**：head节点用 `"auto"`，worker节点用具体地址
5. **资源限制**：根据实际需求设置 `num_cpus` 和 `object_store_memory`

---

**更新日期**: 2025-07-24  
**版本**: v0.1.0
