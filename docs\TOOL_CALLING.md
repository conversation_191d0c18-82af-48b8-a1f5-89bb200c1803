# 工具调用和知识增强功能

AudioAgent 现在支持强大的工具调用和知识增强功能，让AI助手能够执行外部操作并访问实时信息。

## 🎯 功能概览

### 工具调用系统
- **自动工具选择**: AI自动判断何时需要使用工具
- **多种内置工具**: 搜索、计算、时间查询等
- **可扩展架构**: 轻松添加自定义工具
- **安全执行**: 参数验证和错误处理

### 知识增强系统
- **知识库管理**: 存储和检索相关信息
- **智能搜索**: 基于内容的语义搜索
- **分类管理**: 按类别组织知识
- **持久化存储**: 自动保存到文件

### 智能代理
- **意图识别**: 理解用户真实需求
- **上下文管理**: 维护对话历史
- **多模态处理**: 整合工具结果和知识

## 🚀 快速开始

### 1. 基础配置

```python
from audio_agent.config import update_config

# 启用智能功能
update_config(
    intelligent_agent={
        "enabled": True,
        "tool_calling_enabled": True,
        "knowledge_retrieval_enabled": True
    },
    tools={
        "enabled_tools": ["web_search", "calculator", "datetime"],
        "web_search_enabled": True,
        "calculator_enabled": True,
        "datetime_enabled": True
    }
)
```

### 2. 使用工具调用

```python
from audio_agent.tools.builtin import register_builtin_tools
from audio_agent.tools import ToolExecutor, get_tool_registry

# 注册内置工具
register_builtin_tools()

# 创建执行器
executor = ToolExecutor()

# 执行计算
result = await executor.execute_tool_call("calculator", {
    "expression": "2 + 3 * 4"
})
print(f"计算结果: {result.data['formatted_result']}")

# 获取当前时间
result = await executor.execute_tool_call("datetime", {
    "operation": "current_time"
})
print(f"当前时间: {result.data['time']}")

# 网络搜索
result = await executor.execute_tool_call("web_search", {
    "query": "Python编程",
    "max_results": 3
})
for item in result.data['results']:
    print(f"- {item['title']}: {item['snippet']}")
```

### 3. 知识库管理

```python
from audio_agent.knowledge import SimpleKnowledgeBase, KnowledgeItem

# 创建知识库
kb = SimpleKnowledgeBase("my_kb", "./knowledge/my_knowledge.json")

# 添加知识项
item = KnowledgeItem(
    id="python_intro",
    title="Python简介",
    content="Python是一种高级编程语言...",
    category="编程",
    tags=["Python", "编程语言"]
)
await kb.add_item(item)

# 搜索知识
results = await kb.search("Python编程", max_results=5)
for result in results:
    print(f"{result.title}: {result.content[:100]}...")
```

### 4. 智能代理使用

```python
from audio_agent.agent import IntelligentAgentActor
from audio_agent.config import get_config
import ray

# 初始化Ray
ray.init()

# 创建智能代理
config = get_config()
agent = IntelligentAgentActor.remote(config)

# 处理用户消息
response = await agent.process_user_message.remote("现在几点了？")
print(f"AI回复: {response['response']}")

# 查看使用的工具
if response['tools_used']:
    print("使用的工具:")
    for tool in response['tools_used']:
        print(f"- {tool['tool_name']}: {tool['result']}")
```

## 🔧 内置工具详解

### 1. 计算器工具 (calculator)

执行数学计算，支持基本运算和数学函数。

**参数:**
- `expression` (string): 数学表达式

**示例:**
```python
# 基本运算
await executor.execute_tool_call("calculator", {"expression": "2 + 3 * 4"})

# 数学函数
await executor.execute_tool_call("calculator", {"expression": "sqrt(16) + sin(pi/2)"})
```

**支持的函数:**
- 基本运算: `+`, `-`, `*`, `/`, `**`, `%`
- 数学函数: `sqrt`, `sin`, `cos`, `tan`, `log`, `log10`, `exp`
- 常数: `pi`, `e`

### 2. 时间工具 (datetime)

获取当前时间、日期和执行时间相关操作。

**参数:**
- `operation` (string): 操作类型
- `timezone` (string, 可选): 时区
- `format` (string, 可选): 格式字符串

**操作类型:**
- `current_time`: 获取当前时间
- `current_date`: 获取当前日期
- `current_datetime`: 获取当前日期时间
- `add_time`: 时间加减运算

**示例:**
```python
# 获取当前时间
await executor.execute_tool_call("datetime", {
    "operation": "current_time"
})

# 获取特定时区的时间
await executor.execute_tool_call("datetime", {
    "operation": "current_datetime",
    "timezone": "UTC+8"
})
```

### 3. 网络搜索工具 (web_search)

搜索互联网获取实时信息。

**参数:**
- `query` (string): 搜索查询
- `max_results` (integer, 可选): 最大结果数 (默认: 5)

**示例:**
```python
await executor.execute_tool_call("web_search", {
    "query": "最新AI技术发展",
    "max_results": 3
})
```

## 🎨 自定义工具开发

### 创建自定义工具

```python
from audio_agent.tools.base import BaseTool, ToolResult, ToolParameter, ParameterType

class WeatherTool(BaseTool):
    def __init__(self):
        super().__init__("weather", "获取天气信息")
        
        self.add_parameter(ToolParameter(
            name="city",
            type=ParameterType.STRING,
            description="城市名称",
            required=True
        ))
    
    async def execute(self, **kwargs) -> ToolResult:
        validated_params = self.validate_parameters(**kwargs)
        city = validated_params["city"]
        
        # 实现天气查询逻辑
        weather_data = await self._get_weather(city)
        
        return ToolResult(
            success=True,
            data={
                "city": city,
                "temperature": weather_data["temp"],
                "description": weather_data["desc"]
            }
        )
    
    async def _get_weather(self, city: str):
        # 调用天气API
        pass
```

### 注册自定义工具

```python
from audio_agent.tools import register_tool

# 注册工具
weather_tool = WeatherTool()
register_tool(weather_tool)

# 使用工具
result = await executor.execute_tool_call("weather", {"city": "北京"})
```

## 📊 配置选项

### 智能代理配置

```python
intelligent_agent = {
    "enabled": True,                    # 启用智能代理
    "tool_calling_enabled": True,       # 启用工具调用
    "knowledge_retrieval_enabled": True, # 启用知识检索
    "tool_execution_timeout": 30.0,     # 工具执行超时(秒)
    "max_conversation_history": 50      # 最大对话历史
}
```

### 工具系统配置

```python
tools = {
    "enabled_tools": ["web_search", "calculator", "datetime"],
    "web_search_enabled": True,
    "calculator_enabled": True,
    "datetime_enabled": True,
    "search_max_results": 5,
    "search_timeout": 10.0
}
```

### 知识库配置

```python
knowledge = {
    "enabled": True,
    "knowledge_base_path": "./knowledge",
    "max_retrieval_results": 5,
    "similarity_threshold": 0.7
}
```

## 🧪 测试和调试

### 运行测试

```bash
# 运行工具系统测试
python -m pytest tests/test_tools.py -v

# 运行演示
python examples/tool_calling_demo.py
```

### 调试工具

```python
# 测试单个工具
from audio_agent.tools import get_tool_registry

registry = get_tool_registry()
tool = registry.get_tool("calculator")
result = await tool.execute(expression="2+2")
print(result.to_json())

# 查看工具模式
schemas = registry.get_function_schemas()
for schema in schemas:
    print(f"工具: {schema['function']['name']}")
    print(f"描述: {schema['function']['description']}")
```

## 🔒 安全考虑

1. **参数验证**: 所有工具输入都经过严格验证
2. **执行超时**: 防止工具执行时间过长
3. **错误处理**: 完善的异常处理机制
4. **权限控制**: 可配置启用/禁用特定工具

## 📈 性能优化

1. **并行执行**: 使用Ray实现并行处理
2. **缓存机制**: 知识库查询结果缓存
3. **连接池**: 网络请求连接复用
4. **内存管理**: 自动清理过期数据

## 🤝 贡献指南

欢迎贡献新的工具和功能！请参考以下步骤：

1. Fork 项目
2. 创建功能分支
3. 实现新工具或功能
4. 添加测试用例
5. 提交 Pull Request

## 📚 更多资源

- [API文档](./API.md)
- [配置指南](./CONFIGURATION.md)
- [故障排除](./TROUBLESHOOTING.md)
- [更新日志](../CHANGELOG.md)
