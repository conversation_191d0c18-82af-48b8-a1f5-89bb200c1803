#!/usr/bin/env python3
"""
WebSocket Event Handling Demo

This demo shows how AudioAgent correctly handles events received from 
Volcengine's WebSocket server, following the official event flow diagram.

Events flow: 客户端 ←→ RealtimeAPI
- ASR events (450-459) from server
- TTS events (350-359) from server  
- Chat events (550-559) from server
- Session events (150-159) from server
- Connection events (50-59) from server
"""

import asyncio
import json
from datetime import datetime
from audio_agent.events import EventType, EventProcessor
from audio_agent.config import get_config


async def simulate_websocket_events():
    """Simulate receiving events from Volcengine WebSocket server."""
    
    print("🎯 WebSocket Event Handling Demo")
    print("=" * 50)
    print("Simulating events received from Volcengine RealtimeAPI server")
    print()
    
    # Initialize event processor
    config = get_config()
    session_id = "demo-session-12345"
    event_processor = EventProcessor(session_id, config)
    
    # Start event processing in background
    processing_task = asyncio.create_task(event_processor.start_processing())
    
    try:
        print("📡 Starting event processing...")
        await asyncio.sleep(0.1)  # Let processing start
        
        # 1. Connection Started Event (50)
        print("\n🔗 1. Connection Started Event (50)")
        connection_message = {
            'message_type': 'SERVER_ACK',
            'event': 50,  # CONNECTION_STARTED
            'session_id': session_id
        }
        await event_processor.process_websocket_message(connection_message)
        print(f"   ✅ Processed: {connection_message}")
        
        await asyncio.sleep(0.1)
        
        # 2. Session Started Event (150)
        print("\n🎬 2. Session Started Event (150)")
        session_message = {
            'message_type': 'SERVER_ACK',
            'event': 150,  # SESSION_STARTED
            'session_id': session_id,
            'payload': {
                'dialog_id': 'dialog-67890'
            }
        }
        await event_processor.process_websocket_message(session_message)
        print(f"   ✅ Processed: {session_message}")
        
        await asyncio.sleep(0.1)
        
        # 3. ASR Response Event (451) - User speaking
        print("\n🎤 3. ASR Response Event (451) - User speaking")
        asr_message = {
            'message_type': 'SERVER_ACK',
            'event': 451,  # ASR_RESPONSE
            'session_id': session_id,
            'payload': {
                'results': [
                    {
                        'text': '今天北京的天气怎么样？',
                        'is_interim': False
                    }
                ]
            }
        }
        await event_processor.process_websocket_message(asr_message)
        print(f"   ✅ Processed: {asr_message}")
        print(f"   🗣️  User said: '今天北京的天气怎么样？'")
        
        await asyncio.sleep(0.1)
        
        # 4. ASR Ended Event (459) - User finished speaking
        print("\n🛑 4. ASR Ended Event (459) - User finished speaking")
        asr_ended_message = {
            'message_type': 'SERVER_ACK',
            'event': 459,  # ASR_ENDED
            'session_id': session_id
        }
        await event_processor.process_websocket_message(asr_ended_message)
        print(f"   ✅ Processed: {asr_ended_message}")
        print("   🧠 This triggers LLM enhancement processing...")
        
        await asyncio.sleep(0.2)  # Give time for LLM processing
        
        # 5. Chat Response Event (550) - AI responding
        print("\n💬 5. Chat Response Event (550) - AI responding")
        chat_message = {
            'message_type': 'SERVER_ACK',
            'event': 550,  # CHAT_RESPONSE
            'session_id': session_id,
            'payload': {
                'content': '今天北京天气晴朗，气温22°C，适合外出活动。'
            }
        }
        await event_processor.process_websocket_message(chat_message)
        print(f"   ✅ Processed: {chat_message}")
        print(f"   🤖 AI response: '今天北京天气晴朗，气温22°C，适合外出活动。'")
        
        await asyncio.sleep(0.1)
        
        # 6. TTS Sentence Start Event (350)
        print("\n🔊 6. TTS Sentence Start Event (350)")
        tts_start_message = {
            'message_type': 'SERVER_ACK',
            'event': 350,  # TTS_SENTENCE_START
            'session_id': session_id,
            'payload': {
                'tts_type': 'synthesis',
                'text': '今天北京天气晴朗，气温22°C，适合外出活动。'
            }
        }
        await event_processor.process_websocket_message(tts_start_message)
        print(f"   ✅ Processed: {tts_start_message}")
        
        await asyncio.sleep(0.1)
        
        # 7. TTS Response Event (352) - Audio data
        print("\n🎵 7. TTS Response Event (352) - Audio data")
        tts_audio_message = {
            'message_type': 'SERVER_ACK',
            'event': 352,  # TTS_RESPONSE
            'session_id': session_id,
            'payload_msg': b"fake_audio_data_chunk_1"
        }
        await event_processor.process_websocket_message(tts_audio_message)
        print(f"   ✅ Processed: Audio chunk (length: {len(tts_audio_message['payload_msg'])} bytes)")
        
        await asyncio.sleep(0.1)
        
        # 8. TTS Ended Event (359)
        print("\n🏁 8. TTS Ended Event (359)")
        tts_ended_message = {
            'message_type': 'SERVER_ACK',
            'event': 359,  # TTS_ENDED
            'session_id': session_id
        }
        await event_processor.process_websocket_message(tts_ended_message)
        print(f"   ✅ Processed: {tts_ended_message}")
        
        await asyncio.sleep(0.1)
        
        # 9. Session Finished Event (152)
        print("\n🎬 9. Session Finished Event (152)")
        session_finished_message = {
            'message_type': 'SERVER_ACK',
            'event': 152,  # SESSION_FINISHED
            'session_id': session_id
        }
        await event_processor.process_websocket_message(session_finished_message)
        print(f"   ✅ Processed: {session_finished_message}")
        
        await asyncio.sleep(0.1)
        
        print("\n" + "=" * 50)
        print("✅ WebSocket Event Flow Completed!")
        print()
        print("📊 Event Summary:")
        print("   🔗 Connection: Started (50)")
        print("   🎬 Session: Started (150) → Finished (152)")
        print("   🎤 ASR: Response (451) → Ended (459)")
        print("   💬 Chat: Response (550)")
        print("   🔊 TTS: Start (350) → Audio (352) → End (359)")
        print()
        print("🎯 This demonstrates the complete Volcengine event flow!")
        print("   All events are correctly parsed and processed by AudioAgent.")
        
    except Exception as e:
        print(f"❌ Error during demo: {e}")
        
    finally:
        # Stop event processing
        print("\n🛑 Stopping event processing...")
        event_processor.stop_processing()
        processing_task.cancel()
        try:
            await processing_task
        except asyncio.CancelledError:
            pass


def show_event_mapping():
    """Show the mapping of Volcengine event IDs."""
    print("\n📋 Volcengine Event ID Mapping")
    print("=" * 40)
    
    events = [
        (50, "CONNECTION_STARTED", "成功建立连接"),
        (51, "CONNECTION_FAILED", "建立连接失败"),
        (52, "CONNECTION_FINISHED", "连接结束"),
        (150, "SESSION_STARTED", "成功启动会话"),
        (152, "SESSION_FINISHED", "会话已结束"),
        (153, "SESSION_FAILED", "会话失败"),
        (350, "TTS_SENTENCE_START", "合成音频的起始事件"),
        (351, "TTS_SENTENCE_END", "合成音频的分句结束事件"),
        (352, "TTS_RESPONSE", "返回模型生成的音频数据"),
        (359, "TTS_ENDED", "模型一轮音频合成结束事件"),
        (450, "ASR_INFO", "识别出音频流中的首字"),
        (451, "ASR_RESPONSE", "识别出用户说话的文本内容"),
        (459, "ASR_ENDED", "模型认为用户说话结束的事件"),
        (550, "CHAT_RESPONSE", "模型回复的文本内容"),
        (559, "CHAT_ENDED", "模型回复文本结束事件"),
    ]
    
    for event_id, name, description in events:
        print(f"   {event_id:3d} - {name:20s} - {description}")


async def main():
    """Main demo function."""
    print("🎯 AudioAgent WebSocket Event Demo")
    print("Following Volcengine's official event flow")
    print("https://www.volcengine.com/docs/6561/1594356")
    print()
    
    show_event_mapping()
    
    print("\n" + "=" * 60)
    await simulate_websocket_events()


if __name__ == "__main__":
    asyncio.run(main())
