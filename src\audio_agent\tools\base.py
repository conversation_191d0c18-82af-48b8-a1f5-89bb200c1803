"""
Base classes for the tool system.

This module defines the fundamental interfaces and data structures
for the tool calling system, compatible with Volcengine's Function Calling API.
"""

import json
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum


class ToolError(Exception):
    """Exception raised when tool execution fails."""
    
    def __init__(self, message: str, tool_name: str = "", error_code: str = ""):
        super().__init__(message)
        self.tool_name = tool_name
        self.error_code = error_code


@dataclass
class ToolResult:
    """Result of a tool execution."""
    
    success: bool
    data: Any = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        result = {
            "success": self.success,
            "data": self.data,
        }
        if self.error:
            result["error"] = self.error
        if self.metadata:
            result["metadata"] = self.metadata
        return result
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)


class ParameterType(Enum):
    """Parameter types for tool functions."""
    STRING = "string"
    INTEGER = "integer"
    NUMBER = "number"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"


@dataclass
class ToolParameter:
    """Tool parameter definition."""
    
    name: str
    type: ParameterType
    description: str
    required: bool = True
    default: Any = None
    enum: Optional[List[Any]] = None
    
    def to_schema(self) -> Dict[str, Any]:
        """Convert to JSON schema format for Volcengine API."""
        schema = {
            "type": self.type.value,
            "description": self.description
        }
        
        if self.enum:
            schema["enum"] = self.enum
            
        if self.default is not None:
            schema["default"] = self.default
            
        return schema


class BaseTool(ABC):
    """
    Abstract base class for all tools.
    
    This class defines the interface that all tools must implement
    to be compatible with the Volcengine Function Calling system.
    """
    
    def __init__(self, name: str, description: str):
        """
        Initialize the tool.
        
        Args:
            name: Tool name (must be unique)
            description: Tool description for the AI model
        """
        self.name = name
        self.description = description
        self._parameters: List[ToolParameter] = []
    
    def add_parameter(self, parameter: ToolParameter) -> None:
        """Add a parameter to the tool."""
        self._parameters.append(parameter)
    
    def get_parameters(self) -> List[ToolParameter]:
        """Get all tool parameters."""
        return self._parameters.copy()
    
    def to_function_schema(self) -> Dict[str, Any]:
        """
        Convert tool to Volcengine Function Calling schema format.
        
        Returns:
            Dictionary in the format expected by Volcengine API
        """
        required_params = [p.name for p in self._parameters if p.required]
        properties = {p.name: p.to_schema() for p in self._parameters}
        
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": {
                    "type": "object",
                    "properties": properties,
                    "required": required_params
                }
            }
        }
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute the tool with given parameters.
        
        Args:
            **kwargs: Tool parameters
            
        Returns:
            ToolResult containing the execution result
        """
        pass
    
    def validate_parameters(self, **kwargs) -> Dict[str, Any]:
        """
        Validate and process input parameters.
        
        Args:
            **kwargs: Input parameters
            
        Returns:
            Validated parameters
            
        Raises:
            ToolError: If validation fails
        """
        validated = {}
        
        # Check required parameters
        for param in self._parameters:
            if param.required and param.name not in kwargs:
                raise ToolError(
                    f"Required parameter '{param.name}' is missing",
                    tool_name=self.name,
                    error_code="MISSING_PARAMETER"
                )
            
            value = kwargs.get(param.name, param.default)
            
            # Type validation (basic)
            if value is not None:
                validated[param.name] = value
        
        return validated
    
    def __str__(self) -> str:
        return f"Tool({self.name}): {self.description}"
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}')>"
