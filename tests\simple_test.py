"""
基于官方demo的简化测试版本
用于验证音频输入是否正常工作
"""
import asyncio
import uuid
import queue
import threading
import time
import pyaudio
import signal
from typing import Optional, Dict, Any
import websockets
import gzip
import json

# 配置
WS_CONFIG = {
    "base_url": "wss://openspeech.bytedance.com/api/v3/realtime/dialogue",
    "headers": {
        "X-Api-App-ID": "6929935222",
        "X-Api-Access-Key": "J53Rjdq2YILTJoKO3c7qv-UwFRrUMOBy",
        "X-Api-Resource-Id": "volc.speech.dialog",
        "X-Api-App-Key": "PlgvMymc7f3tQnJ6",
        "X-Api-Connect-Id": str(uuid.uuid4()),
    }
}

INPUT_AUDIO_CONFIG = {
    "chunk": 3200,
    "channels": 1,
    "sample_rate": 16000,
    "bit_size": pyaudio.paInt16
}

OUTPUT_AUDIO_CONFIG = {
    "chunk": 3200,
    "channels": 1,
    "sample_rate": 24000,
    "bit_size": pyaudio.paFloat32
}

START_SESSION_REQ = {
    "tts": {
        "audio_config": {
            "channel": 1,
            "format": "pcm",
            "sample_rate": 24000
        },
    },
    "dialog": {
        "bot_name": "豆包",
        "system_role": "你使用活泼灵动的女声，性格开朗，热爱生活。",
        "speaking_style": "你的说话风格简洁明了，语速适中，语调自然。",
        "extra": {
            "strict_audit": False,
            "audit_response": "支持客户自定义安全审核回复话术。"
        }
    }
}

def generate_header(message_type=1, serial_method=1):
    """生成协议头"""
    header = bytearray(16)
    header[0] = (1 << 4) | 4  # version=1, header_size=4
    header[1] = (message_type << 4) | 0  # message_type, flags=0
    header[2] = (serial_method << 4) | 0  # serialization=1, compression=0
    header[3] = 0  # reserved
    return header

def parse_response(data):
    """解析响应数据"""
    if len(data) < 16:
        return {}
    
    # 解析头部
    header_size = data[0] & 0x0f
    message_type = data[1] >> 4
    
    # 跳过头部
    offset = header_size * 4
    if len(data) < offset + 8:
        return {}
    
    # 读取event和session_id_size
    event = int.from_bytes(data[offset:offset+4], 'big')
    session_id_size = int.from_bytes(data[offset+4:offset+8], 'big')
    
    offset += 8
    if len(data) < offset + session_id_size + 4:
        return {}
    
    # 读取session_id和payload_size
    session_id = data[offset:offset+session_id_size].decode('utf-8')
    offset += session_id_size
    payload_size = int.from_bytes(data[offset:offset+4], 'big')
    offset += 4
    
    if len(data) < offset + payload_size:
        return {}
    
    # 读取payload
    payload_data = data[offset:offset+payload_size]
    
    result = {
        'message_type': 'SERVER_ACK' if message_type == 2 else 'SERVER_FULL_RESPONSE',
        'event': event,
        'session_id': session_id
    }
    
    if message_type == 2:  # SERVER_ACK (音频数据)
        result['payload_msg'] = payload_data
    else:  # SERVER_FULL_RESPONSE (JSON数据)
        try:
            payload_json = gzip.decompress(payload_data).decode('utf-8')
            result['payload_msg'] = json.loads(payload_json)
        except:
            result['payload_msg'] = {}
    
    return result

class SimpleAudioTest:
    def __init__(self):
        self.session_id = str(uuid.uuid4())
        self.ws = None
        self.is_running = True
        self.is_recording = True
        self.is_playing = True
        
        # 音频设备
        self.pyaudio = pyaudio.PyAudio()
        self.input_stream = None
        self.output_stream = None
        self.audio_queue = queue.Queue()
        
        # 信号处理
        signal.signal(signal.SIGINT, self._keyboard_signal)
        
    def _keyboard_signal(self, sig, frame):
        print("收到Ctrl+C信号，正在停止...")
        self.is_recording = False
        self.is_playing = False
        self.is_running = False
        
    def _audio_player_thread(self):
        """音频播放线程"""
        while self.is_playing:
            try:
                audio_data = self.audio_queue.get(timeout=1.0)
                if audio_data is not None:
                    self.output_stream.write(audio_data)
            except queue.Empty:
                time.sleep(0.1)
            except Exception as e:
                print(f"音频播放错误: {e}")
                time.sleep(0.1)
                
    async def connect(self):
        """连接到服务器"""
        print(f"连接到: {WS_CONFIG['base_url']}")
        self.ws = await websockets.connect(
            WS_CONFIG['base_url'],
            additional_headers=WS_CONFIG['headers'],
            ping_interval=None
        )
        
        # StartConnection
        start_connection_request = bytearray(generate_header())
        start_connection_request.extend(int(1).to_bytes(4, 'big'))
        payload_bytes = str.encode("{}")
        payload_bytes = gzip.compress(payload_bytes)
        start_connection_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        start_connection_request.extend(payload_bytes)
        await self.ws.send(start_connection_request)
        response = await self.ws.recv()
        print(f"StartConnection响应: {parse_response(response)}")
        
        # StartSession
        payload_bytes = str.encode(json.dumps(START_SESSION_REQ))
        payload_bytes = gzip.compress(payload_bytes)
        start_session_request = bytearray(generate_header())
        start_session_request.extend(int(100).to_bytes(4, 'big'))
        start_session_request.extend((len(self.session_id)).to_bytes(4, 'big'))
        start_session_request.extend(str.encode(self.session_id))
        start_session_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        start_session_request.extend(payload_bytes)
        await self.ws.send(start_session_request)
        response = await self.ws.recv()
        print(f"StartSession响应: {parse_response(response)}")
        
    async def send_audio(self, audio_data):
        """发送音频数据"""
        task_request = bytearray(generate_header(message_type=1, serial_method=0))
        task_request.extend(int(200).to_bytes(4, 'big'))
        task_request.extend((len(self.session_id)).to_bytes(4, 'big'))
        task_request.extend(str.encode(self.session_id))
        payload_bytes = gzip.compress(audio_data)
        task_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        task_request.extend(payload_bytes)
        await self.ws.send(task_request)
        
    async def receive_loop(self):
        """接收消息循环"""
        while self.is_running:
            try:
                response_data = await self.ws.recv()
                response = parse_response(response_data)
                
                if response.get('message_type') == 'SERVER_ACK':
                    audio_data = response.get('payload_msg')
                    if audio_data:
                        print(f"收到音频数据: {len(audio_data)} 字节")
                        self.audio_queue.put(audio_data)
                elif response.get('message_type') == 'SERVER_FULL_RESPONSE':
                    print(f"服务器响应: {response}")
                    
            except Exception as e:
                print(f"接收消息错误: {e}")
                break
                
    async def microphone_loop(self):
        """麦克风输入循环"""
        # 打开音频流
        self.input_stream = self.pyaudio.open(
            format=INPUT_AUDIO_CONFIG["bit_size"],
            channels=INPUT_AUDIO_CONFIG["channels"],
            rate=INPUT_AUDIO_CONFIG["sample_rate"],
            input=True,
            frames_per_buffer=INPUT_AUDIO_CONFIG["chunk"]
        )
        
        self.output_stream = self.pyaudio.open(
            format=OUTPUT_AUDIO_CONFIG["bit_size"],
            channels=OUTPUT_AUDIO_CONFIG["channels"],
            rate=OUTPUT_AUDIO_CONFIG["sample_rate"],
            output=True,
            frames_per_buffer=OUTPUT_AUDIO_CONFIG["chunk"]
        )
        
        # 启动播放线程
        player_thread = threading.Thread(target=self._audio_player_thread)
        player_thread.daemon = True
        player_thread.start()
        
        print("开始录音，请说话...")
        
        while self.is_recording:
            try:
                audio_data = self.input_stream.read(INPUT_AUDIO_CONFIG["chunk"], exception_on_overflow=False)
                print(f"读取音频数据: {len(audio_data)} 字节")
                await self.send_audio(audio_data)
                await asyncio.sleep(0.01)
            except Exception as e:
                print(f"麦克风错误: {e}")
                await asyncio.sleep(0.1)
                
    async def start(self):
        """启动测试"""
        try:
            await self.connect()
            
            # 启动任务
            receive_task = asyncio.create_task(self.receive_loop())
            microphone_task = asyncio.create_task(self.microphone_loop())
            
            # 等待任务完成
            await asyncio.gather(receive_task, microphone_task, return_exceptions=True)
            
        except Exception as e:
            print(f"测试错误: {e}")
        finally:
            self.cleanup()
            
    def cleanup(self):
        """清理资源"""
        if self.input_stream:
            self.input_stream.stop_stream()
            self.input_stream.close()
        if self.output_stream:
            self.output_stream.stop_stream()
            self.output_stream.close()
        self.pyaudio.terminate()

async def main():
    test = SimpleAudioTest()
    await test.start()

if __name__ == "__main__":
    asyncio.run(main())
