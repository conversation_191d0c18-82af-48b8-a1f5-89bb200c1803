#!/usr/bin/env python3
"""
测试流式ChatTTSText实现的脚本

验证工具调用结果是否能正确通过流式ChatTTSText发送到火山引擎服务器
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from audio_agent.config import get_config
from audio_agent.events import EventProcessor
from datetime import datetime
import uuid

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MockDialogClientActor:
    """模拟的 DialogClientActor 用于测试流式ChatTTSText"""
    
    def __init__(self):
        self.sent_messages = []
        
    async def send_chat_tts_text(self, text: str, start: bool = True, end: bool = True) -> bool:
        """模拟发送流式ChatTTSText"""
        message = {
            "text": text,
            "start": start,
            "end": end,
            "timestamp": datetime.now()
        }
        self.sent_messages.append(message)
        logger.info(f"📤 ChatTTSText: start={start}, end={end}, text='{text[:30]}...'")
        return True
    
    def get_streaming_sequence(self):
        """获取流式发送序列"""
        return [(msg['start'], msg['end'], msg['text']) for msg in self.sent_messages]


async def test_streaming_chattts():
    """测试流式ChatTTSText实现"""
    logger.info("🧪 开始测试流式ChatTTSText实现")
    
    # 获取配置
    config = get_config()
    
    # 创建模拟的 DialogClientActor
    mock_dialog_actor = MockDialogClientActor()
    
    # 创建 EventProcessor
    session_id = "test-session-streaming"
    event_processor = EventProcessor(session_id, config, mock_dialog_actor)
    
    logger.info("✅ EventProcessor 创建成功")
    
    # 模拟ASR结束后的工具调用流程
    user_input = "现在是什么时间？"
    
    # 模拟意图识别结果
    intent_result = {
        "intent": "datetime_query",
        "confidence": 0.9,
        "requires_tools": True,
        "tools": ["datetime"],
        "parameters": {}
    }
    
    logger.info("🔧 模拟工具调用流程")
    
    try:
        # 1. 发送占位符ChatTTSText（start=True, end=False）
        await mock_dialog_actor.send_chat_tts_text("正在为您查询", start=True, end=False)
        
        # 2. 模拟工具执行
        await asyncio.sleep(0.1)  # 模拟工具执行时间
        tool_result = "现在是晚上10点30分"
        
        # 3. 发送工具结果（start=False, end=False）
        await mock_dialog_actor.send_chat_tts_text(tool_result, start=False, end=False)
        
        # 4. 发送结束包（start=False, end=True）
        await mock_dialog_actor.send_chat_tts_text("", start=False, end=True)
        
        logger.info("✅ 流式ChatTTSText发送完成")
        
        # 验证发送序列
        sequence = mock_dialog_actor.get_streaming_sequence()
        logger.info("📋 发送序列验证:")
        
        expected_sequence = [
            (True, False, "正在为您查询"),      # 第一包：start=True, end=False
            (False, False, tool_result),      # 中间包：start=False, end=False
            (False, True, "")                 # 最后包：start=False, end=True
        ]
        
        if sequence == expected_sequence:
            logger.info("✅ 流式ChatTTSText序列正确")
            for i, (start, end, text) in enumerate(sequence):
                logger.info(f"   包{i+1}: start={start}, end={end}, text='{text}'")
        else:
            logger.error("❌ 流式ChatTTSText序列错误")
            logger.error(f"   期望: {expected_sequence}")
            logger.error(f"   实际: {sequence}")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_event_processor_streaming():
    """测试EventProcessor的流式ChatTTSText集成"""
    logger.info("🧪 测试EventProcessor流式ChatTTSText集成")
    
    config = get_config()
    mock_dialog_actor = MockDialogClientActor()
    event_processor = EventProcessor("test-session-2", config, mock_dialog_actor)
    
    # 模拟工具调用结果发送
    user_input = "今天天气怎么样？"
    tool_results = [{"tool": "weather", "result": "今天晴天，温度25度"}]
    ai_response = "今天是晴天，温度25度，适合外出活动。"
    
    try:
        # 先发送占位符
        await mock_dialog_actor.send_chat_tts_text("正在查询天气", start=True, end=False)
        
        # 发送工具调用结果
        await event_processor._send_tool_result_to_server(user_input, tool_results, ai_response)
        
        # 验证序列
        sequence = mock_dialog_actor.get_streaming_sequence()
        logger.info("📋 EventProcessor发送序列:")
        for i, (start, end, text) in enumerate(sequence):
            logger.info(f"   包{i+1}: start={start}, end={end}, text='{text[:50]}...'")
            
        if len(sequence) >= 3:
            logger.info("✅ EventProcessor流式ChatTTSText集成成功")
        else:
            logger.error("❌ EventProcessor流式ChatTTSText集成失败")
            
    except Exception as e:
        logger.error(f"❌ EventProcessor测试失败: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """主测试函数"""
    logger.info("🚀 开始流式ChatTTSText测试")
    
    try:
        # 测试基本流式ChatTTSText
        await test_streaming_chattts()
        
        # 测试EventProcessor集成
        await test_event_processor_streaming()
        
        logger.info("🎉 所有测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
