{"name": "AudioAgent默认知识库", "items": [{"id": "audioagent_intro", "title": "AudioAgent简介", "content": "AudioAgent是一个先进的实时语音对话AI助手，具备语音识别、自然语言处理、工具调用和知识检索等功能。它使用Ray分布式计算框架，支持高并发和低延迟的语音交互。", "category": "产品介绍", "tags": ["AudioAgent", "语音AI", "实时对话"], "metadata": {"importance": "high", "version": "1.0"}, "created_at": "2025-07-26T19:12:00", "updated_at": "2025-07-26T19:12:00"}, {"id": "tool_calling_feature", "title": "工具调用功能", "content": "AudioAgent支持智能工具调用功能，可以根据用户的需求自动选择和执行相应的工具。内置工具包括：网络搜索、数学计算、时间查询等。用户可以通过自然语言触发工具调用，如'帮我算一下2+3'或'现在几点了'。", "category": "功能特性", "tags": ["工具调用", "自动化", "智能助手"], "metadata": {"importance": "high", "related_tools": ["calculator", "web_search", "datetime"]}, "created_at": "2025-07-26T19:12:00", "updated_at": "2025-07-26T19:12:00"}, {"id": "knowledge_system", "title": "知识库系统", "content": "AudioAgent集成了智能知识库系统，可以存储、检索和管理各种知识内容。支持按分类和标签组织知识，提供智能搜索功能。知识库可以动态更新，支持多种格式的知识导入。", "category": "功能特性", "tags": ["知识库", "搜索", "知识管理"], "metadata": {"importance": "medium", "storage_format": "JSON"}, "created_at": "2025-07-26T19:12:00", "updated_at": "2025-07-26T19:12:00"}, {"id": "voice_interruption", "title": "语音中断功能", "content": "AudioAgent具备先进的语音中断功能，使用VAD（语音活动检测）技术实时监测用户语音输入。当检测到用户开始说话时，系统会立即中断当前的语音播放，提供自然流畅的对话体验。", "category": "技术特性", "tags": ["VAD", "语音中断", "实时交互"], "metadata": {"importance": "high", "technology": "Silero VAD"}, "created_at": "2025-07-26T19:12:00", "updated_at": "2025-07-26T19:12:00"}, {"id": "configuration_system", "title": "配置系统", "content": "AudioAgent提供灵活的配置系统，支持通过环境变量、配置文件等方式进行个性化设置。可以配置音频参数、AI模型参数、工具启用状态、知识库路径等。所有配置都有合理的默认值，同时支持运行时动态调整。", "category": "技术特性", "tags": ["配置", "环境变量", "个性化"], "metadata": {"importance": "medium", "config_file": ".env"}, "created_at": "2025-07-26T19:12:00", "updated_at": "2025-07-26T19:12:00"}, {"id": "ray_architecture", "title": "Ray分布式架构", "content": "AudioAgent基于Ray分布式计算框架构建，采用Actor模型实现高并发处理。主要包括AudioInputActor（音频输入）、AudioOutputActor（音频输出）、DialogClientActor（对话客户端）和IntelligentAgentActor（智能代理）等组件。", "category": "技术架构", "tags": ["<PERSON>", "分布式", "Actor模型"], "metadata": {"importance": "high", "framework": "<PERSON>"}, "created_at": "2025-07-26T19:12:00", "updated_at": "2025-07-26T19:12:00"}, {"id": "usage_examples", "title": "使用示例", "content": "AudioAgent支持多种使用场景：1) 日常对话：'你好，今天天气怎么样？' 2) 计算任务：'帮我算一下15乘以8' 3) 信息查询：'搜索一下最新的AI技术发展' 4) 时间查询：'现在几点了？' 5) 知识问答：基于内置知识库回答问题。", "category": "使用指南", "tags": ["示例", "使用场景", "对话"], "metadata": {"importance": "medium", "examples_count": 5}, "created_at": "2025-07-26T19:12:00", "updated_at": "2025-07-26T19:12:00"}]}