#!/usr/bin/env python3
"""
音频设备测试

测试系统的音频输入和输出设备是否正常工作。
"""

import sys
import pyaudio
import numpy as np
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def list_audio_devices():
    """列出所有可用的音频设备"""
    print("🎵 音频设备列表")
    print("=" * 50)
    
    p = pyaudio.PyAudio()
    
    print(f"可用设备数量: {p.get_device_count()}")
    print()
    
    for i in range(p.get_device_count()):
        info = p.get_device_info_by_index(i)
        print(f"设备 {i}: {info['name']}")
        print(f"  最大输入通道: {info['maxInputChannels']}")
        print(f"  最大输出通道: {info['maxOutputChannels']}")
        print(f"  默认采样率: {info['defaultSampleRate']}")
        print(f"  主机API: {p.get_host_api_info_by_index(info['hostApi'])['name']}")
        print()
    
    # 获取默认设备
    try:
        default_input = p.get_default_input_device_info()
        print(f"默认输入设备: {default_input['name']} (设备 {default_input['index']})")
    except OSError:
        print("❌ 没有默认输入设备")
    
    try:
        default_output = p.get_default_output_device_info()
        print(f"默认输出设备: {default_output['name']} (设备 {default_output['index']})")
    except OSError:
        print("❌ 没有默认输出设备")
    
    p.terminate()


def test_microphone():
    """测试麦克风录音"""
    print("\n🎤 麦克风测试")
    print("=" * 50)
    
    p = pyaudio.PyAudio()
    
    # 音频参数
    chunk = 1024
    format = pyaudio.paInt16
    channels = 1
    rate = 16000
    
    try:
        # 尝试打开输入流
        stream = p.open(
            format=format,
            channels=channels,
            rate=rate,
            input=True,
            frames_per_buffer=chunk
        )
        
        print("✅ 麦克风可以正常打开")
        print("正在录音3秒钟，请说话...")
        
        frames = []
        for i in range(0, int(rate / chunk * 3)):
            data = stream.read(chunk)
            frames.append(data)
            
            # 计算音量
            audio_data = np.frombuffer(data, dtype=np.int16)
            volume = np.sqrt(np.mean(audio_data**2))
            
            if i % 10 == 0:  # 每隔一段时间显示音量
                print(f"音量: {'█' * int(volume / 1000)}")
        
        stream.stop_stream()
        stream.close()
        
        print("✅ 麦克风录音测试完成")
        
    except Exception as e:
        print(f"❌ 麦克风测试失败: {e}")
    
    p.terminate()


def test_speaker():
    """测试扬声器播放"""
    print("\n🔊 扬声器测试")
    print("=" * 50)
    
    p = pyaudio.PyAudio()
    
    # 音频参数
    chunk = 1024
    format = pyaudio.paFloat32
    channels = 1
    rate = 24000
    
    try:
        # 尝试打开输出流
        stream = p.open(
            format=format,
            channels=channels,
            rate=rate,
            output=True,
            frames_per_buffer=chunk
        )
        
        print("✅ 扬声器可以正常打开")
        print("正在播放测试音调...")
        
        # 生成440Hz的正弦波（A音）
        duration = 2.0  # 秒
        samples = int(rate * duration)
        
        for i in range(0, samples, chunk):
            # 生成正弦波数据
            t = np.linspace(i/rate, (i+chunk)/rate, chunk)
            wave = 0.3 * np.sin(2 * np.pi * 440 * t)  # 440Hz, 音量0.3
            
            # 转换为float32格式
            audio_data = wave.astype(np.float32).tobytes()
            stream.write(audio_data)
        
        stream.stop_stream()
        stream.close()
        
        print("✅ 扬声器播放测试完成")
        
    except Exception as e:
        print(f"❌ 扬声器测试失败: {e}")
    
    p.terminate()


def test_audio_config():
    """测试AudioAgent的音频配置"""
    print("\n⚙️ AudioAgent音频配置测试")
    print("=" * 50)
    
    try:
        from audio_agent.config import get_config
        
        config = get_config()
        
        print("输入音频配置:")
        print(f"  采样率: {config.input_audio.sample_rate} Hz")
        print(f"  通道数: {config.input_audio.channels}")
        print(f"  块大小: {config.input_audio.chunk}")
        print(f"  格式: {config.input_audio.format}")
        print(f"  位深: {config.input_audio.bit_size}")
        
        print("\n输出音频配置:")
        print(f"  采样率: {config.output_audio.sample_rate} Hz")
        print(f"  通道数: {config.output_audio.channels}")
        print(f"  块大小: {config.output_audio.chunk}")
        print(f"  格式: {config.output_audio.format}")
        print(f"  位深: {config.output_audio.bit_size}")
        
        print("\nVAD配置:")
        print(f"  启用VAD: {config.input_audio.vad_enabled}")
        print(f"  VAD模型: {config.input_audio.vad_model}")
        print(f"  VAD阈值: {config.input_audio.vad_threshold}")
        
        print("✅ AudioAgent配置加载成功")
        
    except Exception as e:
        print(f"❌ AudioAgent配置测试失败: {e}")


def main():
    """主测试函数"""
    print("🧪 AudioAgent音频设备测试")
    print("=" * 60)
    print("测试系统音频设备和AudioAgent配置")
    print()
    
    # 1. 列出音频设备
    list_audio_devices()
    
    # 2. 测试麦克风
    test_microphone()
    
    # 3. 测试扬声器
    test_speaker()
    
    # 4. 测试AudioAgent配置
    test_audio_config()
    
    print("\n🎉 音频设备测试完成！")
    print()
    print("💡 如果有任何测试失败，请检查：")
    print("  - 麦克风和扬声器是否正确连接")
    print("  - 系统是否授予了麦克风权限")
    print("  - 音频驱动是否正常工作")
    print("  - 是否有其他程序占用音频设备")


if __name__ == "__main__":
    main()
