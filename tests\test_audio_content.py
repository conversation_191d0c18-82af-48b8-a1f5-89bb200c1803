#!/usr/bin/env python3
"""
测试音频内容是否包含实际声音
"""

import asyncio
import logging
import numpy as np
import time
from audio_agent.config import get_config
from audio_agent.core.actors import AudioInputActor
import ray

# 设置日志级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_audio_content():
    """测试音频内容"""
    print("=== 测试音频内容 ===")
    
    # 获取配置
    config = get_config()
    print(f"输入音频配置:")
    print(f"  - 采样率: {config.input_audio.sample_rate}Hz")
    print(f"  - 通道数: {config.input_audio.channels}")
    print(f"  - 块大小: {config.input_audio.chunk}")
    print(f"  - 位深度: {config.input_audio.bit_size}")
    
    # 初始化Ray
    if not ray.is_initialized():
        ray.init(ignore_reinit_error=True)
    
    try:
        # 创建音频输入Actor
        audio_input_actor = AudioInputActor.remote(config.input_audio)
        
        # 启动录音
        print("\n启动录音...")
        started = await audio_input_actor.start_recording.remote()
        
        if not started:
            print("❌ 启动录音失败")
            return
        
        print("✅ 录音启动成功")
        print("请说话，我会分析音频内容...")
        print("测试20秒...")
        
        # 监控音频数据
        audio_count = 0
        silent_count = 0
        sound_count = 0
        max_amplitude_overall = 0
        
        start_time = time.time()
        
        try:
            while time.time() - start_time < 20:  # 测试20秒
                # 获取音频数据
                audio_data = await audio_input_actor.get_audio_data.remote()
                
                if audio_data:
                    audio_count += 1
                    
                    # 分析音频数据
                    audio_int16 = np.frombuffer(audio_data, dtype=np.int16)
                    max_amplitude = np.abs(audio_int16).max()
                    avg_amplitude = np.abs(audio_int16).mean()
                    
                    max_amplitude_overall = max(max_amplitude_overall, max_amplitude)
                    
                    # 判断是否有声音（阈值可以调整）
                    if max_amplitude > 100:  # 有明显声音
                        sound_count += 1
                        print(f"🔊 检测到声音 #{sound_count}: 最大幅度={max_amplitude}, 平均幅度={avg_amplitude:.1f}")
                    elif max_amplitude > 10:  # 有微弱声音
                        print(f"🔉 微弱声音: 最大幅度={max_amplitude}, 平均幅度={avg_amplitude:.1f}")
                    else:  # 静音
                        silent_count += 1
                        if silent_count % 50 == 0:  # 每50个静音块显示一次
                            print(f"🔇 静音块 #{silent_count}: 最大幅度={max_amplitude}, 平均幅度={avg_amplitude:.1f}")
                
                await asyncio.sleep(0.05)  # 50ms间隔
                
        except KeyboardInterrupt:
            print("\n用户中断测试")
        
        # 停止录音
        await audio_input_actor.stop_recording.remote()
        print("\n=== 测试结果 ===")
        print(f"总音频块数: {audio_count}")
        print(f"静音块数: {silent_count}")
        print(f"有声音块数: {sound_count}")
        print(f"最大音频幅度: {max_amplitude_overall}")
        
        if sound_count > 0:
            print("✅ 检测到麦克风声音输入")
        else:
            print("❌ 未检测到麦克风声音输入")
            print("可能的原因:")
            print("1. 麦克风权限问题")
            print("2. 麦克风设备选择问题")
            print("3. 音频配置问题")
            print("4. 麦克风音量太低")
        
    except Exception as e:
        print(f"测试出错: {e}")
    finally:
        if ray.is_initialized():
            ray.shutdown()

if __name__ == "__main__":
    asyncio.run(test_audio_content())
