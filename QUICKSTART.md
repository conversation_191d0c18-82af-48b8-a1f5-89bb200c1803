# 🚀 快速开始指南

这个指南将帮助您在5分钟内体验Audio Agent的语音对话功能。

## 📋 准备工作

### 1. 环境要求
- Python 3.10+ 
- 麦克风和扬声器
- 网络连接

### 2. 获取API密钥

1. 访问 [火山引擎控制台](https://console.volcengine.com/)
2. 注册并登录账号
3. 开通"语音技术"服务
4. 获取 `App ID` 和 `Access Key`

## ⚡ 一键体验

### 方法一：使用演示脚本（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd audio-agent

# 2. 安装依赖
curl -LsSf https://astral.sh/uv/install.sh | sh  # 安装uv
uv sync  # 安装项目依赖

# 3. 运行演示
uv run python scripts/demo.py \
  --app-id "your_app_id_here" \
  --access-key "your_access_key_here" \
  --bot-name "我的AI助手" \
  --duration 120
```

### 方法二：使用环境变量

```bash
# 1. 设置环境变量
export AUDIO_AGENT_WEBSOCKET__APP_ID="your_app_id_here"
export AUDIO_AGENT_WEBSOCKET__ACCESS_KEY="your_access_key_here"

# 2. 运行演示
uv run python scripts/demo.py --duration 120
```

### 方法三：使用主程序

```bash
# 直接运行主程序
uv run python scripts/run.py \
  --app-id "your_app_id_here" \
  --access-key "your_access_key_here"
```

## 🎤 开始对话

启动成功后，您会看到：

```
============================================================
🎤 Audio Agent - 实时语音对话演示
============================================================
基于 uv + Ray 的现代化语音对话系统
支持实时语音输入和AI语音回复

📋 检查运行要求...
✅ Python版本: 3.10.17
✅ Ray版本: 2.39.0
✅ PyAudio已安装

🎵 检查音频设备...
✅ 找到 2 个输入设备
   📱 MacBook Pro麦克风
✅ 找到 3 个输出设备
   🔊 MacBook Pro扬声器

🔑 配置API密钥...
✅ API密钥已配置

🚀 启动语音对话演示...
⏳ 正在启动会话...
✅ 会话启动成功!
📊 Ray仪表板: http://localhost:8265

🎤 开始说话，AI将实时回复...
💡 提示: 说话时保持清晰，等待AI回复完成后再继续
⏹️  按 Ctrl+C 可以随时退出
```

现在您可以：
1. **对着麦克风说话** - 系统会实时识别您的语音
2. **等待AI回复** - AI会通过扬声器回复您
3. **继续对话** - 可以进行多轮对话交互

## 🎯 使用技巧

### 获得最佳体验

1. **环境准备**
   - 选择安静的环境
   - 确保麦克风距离适中（15-30cm）
   - 调整音量到合适水平

2. **说话技巧**
   - 语速适中，发音清晰
   - 等待AI回复完成后再继续说话
   - 避免长时间停顿

3. **对话内容**
   - 可以问任何问题
   - 支持中文和英文
   - 可以要求AI扮演不同角色

### 示例对话

```
用户: "你好，请介绍一下自己"
AI: "你好！我是AI助手，很高兴为您服务。我可以回答问题、提供建议、进行对话交流。有什么我可以帮助您的吗？"

用户: "帮我写一个Python函数，计算斐波那契数列"
AI: "好的，我来为您写一个计算斐波那契数列的Python函数..."

用户: "今天天气怎么样？"
AI: "抱歉，我无法获取实时天气信息。建议您查看天气应用或网站获取准确的天气预报。"
```

## 🔧 自定义配置

### 快速配置不同角色

**技术助手**：
```bash
uv run python scripts/demo.py \
  --bot-name "技术专家" \
  --app-id "your_app_id" \
  --access-key "your_access_key"
```

**英语老师**：
```bash
uv run python scripts/demo.py \
  --bot-name "英语老师" \
  --app-id "your_app_id" \
  --access-key "your_access_key"
```

**生活助手**：
```bash
uv run python scripts/demo.py \
  --bot-name "生活助手" \
  --app-id "your_app_id" \
  --access-key "your_access_key"
```

### 调整音频参数

如果遇到音频问题，可以尝试：

```python
# 创建 custom_config.py
from audio_agent.config import update_config

update_config(
    input_audio={
        "sample_rate": 16000,  # 降低采样率
        "chunk": 800,          # 减小块大小，降低延迟
    },
    output_audio={
        "sample_rate": 24000,
        "chunk": 1200,
    }
)
```

然后运行：
```bash
uv run python -c "import custom_config; from scripts.demo import main; main()"
```

## 🐛 常见问题

### 问题1：没有声音输出
**解决方案**：
```bash
# 检查音频设备
uv run python -c "
import pyaudio
p = pyaudio.PyAudio()
for i in range(p.get_device_count()):
    info = p.get_device_info_by_index(i)
    if info['maxOutputChannels'] > 0:
        print(f'输出设备: {info[\"name\"]}')
p.terminate()
"
```

### 问题2：麦克风无法录音
**解决方案**：
- **macOS**: 系统偏好设置 → 安全性与隐私 → 隐私 → 麦克风 → 允许终端访问
- **Windows**: 设置 → 隐私 → 麦克风 → 允许应用访问麦克风
- **Linux**: 检查用户是否在audio组中

### 问题3：网络连接失败
**解决方案**：
```bash
# 测试网络连接
ping openspeech.bytedance.com

# 检查API密钥是否正确
echo $AUDIO_AGENT_WEBSOCKET__APP_ID
echo $AUDIO_AGENT_WEBSOCKET__ACCESS_KEY
```

### 问题4：Ray初始化失败
**解决方案**：
```bash
# 清理Ray进程
ray stop

# 使用不同端口
uv run python scripts/demo.py --ray-dashboard-port 8266
```

### 问题5：需要连接远程Ray集群
**解决方案**：
```python
# 创建 remote_config.py
from audio_agent.config import update_config

update_config(
    ray={
        "address": "ray://your-head-node:10001",  # 远程集群地址
        "dashboard_host": "0.0.0.0"
    }
)
```

然后运行：
```bash
uv run python -c "import remote_config; from scripts.demo import main; main()"
```

## 📊 监控和调试

### 查看Ray仪表板
启动后访问：http://localhost:8265

可以看到：
- Actor状态和资源使用
- 任务执行情况
- 系统性能指标

### 启用详细日志
```bash
uv run python scripts/demo.py --verbose
```

### 性能监控
```bash
# 查看系统资源使用
top  # Linux/macOS
taskmgr  # Windows
```

## 🎉 下一步

体验成功后，您可以：

1. **阅读完整文档**：[README.md](README.md)
2. **学习开发指南**：[docs/DEVELOPMENT.md](docs/DEVELOPMENT.md)
3. **查看API文档**：[docs/API.md](docs/API.md)
4. **集成到项目**：参考集成示例
5. **贡献代码**：提交Issue或PR

## 📞 获取帮助

如果遇到问题：
1. 查看 [FAQ文档](docs/FAQ.md)
2. 搜索 [项目Issues](../../issues)
3. 创建 [新Issue](../../issues/new)

---

**祝您使用愉快！🎉**
