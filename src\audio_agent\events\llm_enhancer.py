"""
LLM enhancer for processing user intents and generating enhanced responses.

This module integrates with external LLM services to provide:
- Intent recognition
- Tool calling
- Enhanced response generation
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
import json
import os
from datetime import datetime

logger = logging.getLogger(__name__)


class LLMEnhancer:
    """
    LLM enhancer that provides intelligent processing capabilities.
    
    This class integrates with external LLM services to enhance the basic
    dialog functionality with intent recognition, tool calling, and
    context-aware response generation.
    """
    
    def __init__(self, config: Any):
        """
        Initialize LLM enhancer.

        Args:
            config: Audio agent configuration
        """
        self.config = config
        self.available_tools = self._load_available_tools()

        # Initialize tool system
        from ..tools import get_tool_registry, ToolExecutor
        from ..tools.builtin import CalculatorTool, DateTimeTool, WebSearchTool

        self.tool_registry = get_tool_registry()

        # Register built-in tools
        self._register_builtin_tools()

        self.tool_executor = ToolExecutor(self.tool_registry)

        # Initialize OpenAI client (lazy loading to avoid blocking startup)
        self.openai_client = None
        self.use_openai = False
        self._openai_initialized = False

        logger.info(f"LLMEnhancer initialized (OpenAI: lazy loading)")

    def _initialize_openai_client(self):
        """Initialize OpenAI client lazily to avoid blocking startup."""
        if self._openai_initialized:
            return

        self._openai_initialized = True

        try:
            import openai
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.openai_client = openai.AsyncOpenAI(api_key=api_key)
                self.use_openai = True
                logger.info("OpenAI client initialized")
            else:
                logger.warning("OPENAI_API_KEY not found, using rule-based fallback")
        except ImportError:
            logger.warning("OpenAI package not installed, using rule-based fallback")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")

    def _register_builtin_tools(self):
        """Register built-in tools."""
        from ..tools.builtin import CalculatorTool, DateTimeTool, WebSearchTool

        try:
            # Register calculator tool
            if self.config.tools.calculator_enabled:
                calculator = CalculatorTool()
                self.tool_registry.register_tool(calculator)
                logger.info("Registered calculator tool")

            # Register datetime tool
            if self.config.tools.datetime_enabled:
                datetime_tool = DateTimeTool()
                self.tool_registry.register_tool(datetime_tool)
                logger.info("Registered datetime tool")

            # Register web search tool
            if self.config.tools.web_search_enabled:
                web_search = WebSearchTool()
                self.tool_registry.register_tool(web_search)
                logger.info("Registered web search tool")

        except Exception as e:
            logger.error(f"Failed to register built-in tools: {e}")

    def _load_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Load available tools for the agent."""
        # This would typically load from configuration or registry
        return {
            "get_weather": {
                "description": "Get current weather information for a location",
                "parameters": {
                    "location": {"type": "string", "description": "City name or location"}
                }
            },
            "search_web": {
                "description": "Search the web for information",
                "parameters": {
                    "query": {"type": "string", "description": "Search query"}
                }
            },
            "set_reminder": {
                "description": "Set a reminder for the user",
                "parameters": {
                    "message": {"type": "string", "description": "Reminder message"},
                    "time": {"type": "string", "description": "When to remind (e.g., '10 minutes', 'tomorrow 9am')"}
                }
            },
            "play_music": {
                "description": "Play music or audio content",
                "parameters": {
                    "query": {"type": "string", "description": "Song, artist, or playlist name"}
                }
            },
            "control_smart_home": {
                "description": "Control smart home devices",
                "parameters": {
                    "device": {"type": "string", "description": "Device name (e.g., 'lights', 'thermostat')"},
                    "action": {"type": "string", "description": "Action to perform (e.g., 'turn on', 'set to 72')"}
                }
            }
        }
    
    async def recognize_intent(self, user_input: str, conversation_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Recognize user intent from input.
        
        Args:
            user_input: User's spoken input
            conversation_history: Previous conversation context
            
        Returns:
            Intent recognition result
        """
        try:
            # Simulate intent recognition (in real implementation, this would call an LLM)
            intent_result = await self._analyze_intent_with_llm(user_input, conversation_history)
            
            logger.info(f"Intent recognized: {intent_result}")
            return intent_result
            
        except Exception as e:
            logger.error(f"Failed to recognize intent: {e}")
            return {
                "intent": "general_conversation",
                "confidence": 0.5,
                "requires_tools": False,
                "tools": []
            }
    
    async def _analyze_intent_with_llm(self, user_input: str, conversation_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze intent using LLM or rule-based fallback."""

        # Initialize OpenAI client lazily
        self._initialize_openai_client()

        if self.use_openai and self.openai_client:
            return await self._analyze_intent_with_openai(user_input, conversation_history)
        else:
            return await self._analyze_intent_rule_based(user_input)

    async def _analyze_intent_with_openai(self, user_input: str, conversation_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze intent using OpenAI."""
        try:
            # Create system prompt for intent recognition
            system_prompt = """You are an intent recognition system. Analyze the user's input and return a JSON response with:
{
  "intent": "one of: weather_query, web_search, smart_home_control, calculator, datetime_query, general_conversation",
  "confidence": 0.0-1.0,
  "requires_tools": true/false,
  "tools": ["list", "of", "tool", "names"],
  "parameters": {"key": "value"}
}

Available tools:
- get_weather: Get weather information (requires location)
- search_web: Search the internet (requires query)
- calculator: Perform calculations (requires expression)
- get_datetime: Get current date/time
- smart_home_control: Control smart devices (requires device and action)"""

            # Create user prompt
            user_prompt = f"Analyze this user input: '{user_input}'"

            # Call OpenAI
            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=200
            )

            # Parse response
            content = response.choices[0].message.content.strip()
            try:
                result = json.loads(content)
                logger.info(f"OpenAI intent analysis: {result}")
                return result
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse OpenAI response as JSON: {content}")
                return await self._analyze_intent_rule_based(user_input)

        except Exception as e:
            logger.error(f"OpenAI intent analysis failed: {e}")
            return await self._analyze_intent_rule_based(user_input)

    async def _analyze_intent_rule_based(self, user_input: str) -> Dict[str, Any]:
        """Fallback rule-based intent recognition."""
        user_input_lower = user_input.lower()
        
        # Weather intent (English and Chinese)
        weather_keywords = ["weather", "temperature", "rain", "sunny", "cloudy", "天气", "温度", "下雨", "晴天", "多云", "气温"]
        if any(word in user_input_lower for word in weather_keywords):
            return {
                "intent": "weather_query",
                "confidence": 0.9,
                "requires_tools": True,
                "tools": ["get_weather"],
                "parameters": {
                    "location": self._extract_location(user_input)
                }
            }

        # Calculator intent (English and Chinese)
        calc_keywords = ["calculate", "compute", "math", "计算", "算", "数学", "+", "-", "*", "/", "sqrt", "sin", "cos"]
        if any(word in user_input_lower for word in calc_keywords):
            return {
                "intent": "calculator",
                "confidence": 0.9,
                "requires_tools": True,
                "tools": ["calculator"],
                "parameters": {
                    "expression": user_input
                }
            }

        # DateTime intent (English and Chinese)
        time_keywords = ["time", "date", "now", "today", "现在", "时间", "日期", "今天", "几点", "几号"]
        if any(word in user_input_lower for word in time_keywords):
            return {
                "intent": "datetime_query",
                "confidence": 0.9,
                "requires_tools": True,
                "tools": ["datetime"],
                "parameters": {}
            }

        # Search intent (English and Chinese)
        search_keywords = ["search", "find", "look up", "what is", "who is", "搜索", "查找", "搜", "查", "找"]
        if any(word in user_input_lower for word in search_keywords):
            return {
                "intent": "web_search",
                "confidence": 0.8,
                "requires_tools": True,
                "tools": ["search_web"],
                "parameters": {
                    "query": user_input
                }
            }
        
        # Reminder intent
        elif any(word in user_input_lower for word in ["remind", "reminder", "don't forget", "remember"]):
            return {
                "intent": "set_reminder",
                "confidence": 0.85,
                "requires_tools": True,
                "tools": ["set_reminder"],
                "parameters": {
                    "message": user_input,
                    "time": self._extract_time(user_input)
                }
            }
        
        # Music intent
        elif any(word in user_input_lower for word in ["play", "music", "song", "artist", "playlist"]):
            return {
                "intent": "play_music",
                "confidence": 0.9,
                "requires_tools": True,
                "tools": ["play_music"],
                "parameters": {
                    "query": user_input
                }
            }
        
        # Smart home intent
        elif any(word in user_input_lower for word in ["lights", "thermostat", "temperature", "turn on", "turn off"]):
            return {
                "intent": "smart_home_control",
                "confidence": 0.8,
                "requires_tools": True,
                "tools": ["control_smart_home"],
                "parameters": {
                    "device": self._extract_device(user_input),
                    "action": self._extract_action(user_input)
                }
            }
        
        # General conversation
        else:
            return {
                "intent": "general_conversation",
                "confidence": 0.7,
                "requires_tools": False,
                "tools": []
            }
    
    async def execute_tools(self, user_input: str, tool_calls: List[str], conversation_history: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Execute the requested tools.
        
        Args:
            user_input: Original user input
            tool_calls: List of tools to execute
            conversation_history: Conversation context
            
        Returns:
            Tool execution results
        """
        results = []
        
        for tool_name in tool_calls:
            try:
                # Extract parameters for the tool from user input
                parameters = self._extract_tool_parameters(tool_name, user_input)

                # Execute tool using the tool executor
                tool_result = await self.tool_executor.execute_tool_call(tool_name, parameters)

                results.append({
                    "tool": tool_name,
                    "success": tool_result.success,
                    "result": tool_result.data if tool_result.success else None,
                    "error": tool_result.error if not tool_result.success else None
                })

                if tool_result.success:
                    logger.info(f"Tool {tool_name} executed successfully")
                else:
                    logger.error(f"Tool {tool_name} failed: {tool_result.error}")

            except Exception as e:
                logger.error(f"Failed to execute tool {tool_name}: {e}")
                results.append({
                    "tool": tool_name,
                    "success": False,
                    "error": str(e)
                })
        
        return results

    def _extract_tool_parameters(self, tool_name: str, user_input: str) -> Dict[str, Any]:
        """Extract parameters for a specific tool from user input."""
        user_input_lower = user_input.lower()

        if tool_name == "calculator":
            # Extract mathematical expression
            # Look for patterns like "calculate", "what is", numbers and operators
            import re
            # Try to find mathematical expressions
            math_pattern = r'[\d+\-*/().\s]+'
            matches = re.findall(math_pattern, user_input)
            if matches:
                expression = max(matches, key=len).strip()
                return {"expression": expression}
            else:
                # Fallback: use the whole input
                return {"expression": user_input}

        elif tool_name == "get_weather":
            # Extract location from user input
            location = self._extract_location(user_input)
            return {"location": location}

        elif tool_name == "search_web":
            # Extract search query
            # Remove common prefixes
            query = user_input
            for prefix in ["search for", "look up", "find", "what is", "tell me about"]:
                if query.lower().startswith(prefix):
                    query = query[len(prefix):].strip()
                    break
            return {"query": query}

        elif tool_name == "datetime":
            # Determine operation based on user input
            user_input_lower = user_input.lower()
            if any(word in user_input_lower for word in ["时间", "几点", "time"]):
                return {"operation": "current_time"}
            elif any(word in user_input_lower for word in ["日期", "几号", "date", "今天"]):
                return {"operation": "current_date"}
            else:
                return {"operation": "current_datetime"}

        else:
            # Default: pass user input as query/text parameter
            return {"query": user_input}

    async def _execute_single_tool(self, tool_name: str, user_input: str) -> Dict[str, Any]:
        """Execute a single tool (simulated for demonstration)."""
        
        # Simulate tool execution with delays
        await asyncio.sleep(0.5)
        
        if tool_name == "get_weather":
            return {
                "location": self._extract_location(user_input) or "current location",
                "temperature": "22°C",
                "condition": "Partly cloudy",
                "humidity": "65%"
            }
        
        elif tool_name == "search_web":
            return {
                "query": user_input,
                "results": [
                    {"title": "Example Result 1", "snippet": "This is a sample search result..."},
                    {"title": "Example Result 2", "snippet": "Another sample result..."}
                ]
            }
        
        elif tool_name == "set_reminder":
            return {
                "message": user_input,
                "scheduled_time": self._extract_time(user_input) or "in 1 hour",
                "status": "reminder_set"
            }
        
        elif tool_name == "play_music":
            return {
                "query": user_input,
                "playing": "Sample Song by Sample Artist",
                "status": "now_playing"
            }
        
        elif tool_name == "control_smart_home":
            return {
                "device": self._extract_device(user_input),
                "action": self._extract_action(user_input),
                "status": "command_executed"
            }
        
        else:
            raise ValueError(f"Unknown tool: {tool_name}")
    
    async def generate_response(self, user_input: str, conversation_history: List[Dict[str, Any]], intent_result: Dict[str, Any]) -> str:
        """
        Generate response without tools.
        
        Args:
            user_input: User input
            conversation_history: Conversation context
            intent_result: Intent recognition result
            
        Returns:
            Generated response
        """
        # Simulate LLM response generation
        await asyncio.sleep(0.3)
        
        intent = intent_result.get("intent", "general_conversation")

        if intent == "general_conversation":
            return f"我理解您说的是'{user_input}'。我可以为您做些什么吗？"
        else:
            intent_cn = intent.replace('_', ' ')
            return f"我知道您想要{intent_cn}，但我需要更多信息来帮助您。"
    
    async def generate_enhanced_response(self, user_input: str, tool_results: List[Dict[str, Any]], conversation_history: List[Dict[str, Any]]) -> str:
        """
        Generate enhanced response using tool results.
        
        Args:
            user_input: Original user input
            tool_results: Results from tool execution
            conversation_history: Conversation context
            
        Returns:
            Enhanced response
        """
        # Simulate LLM response generation with tool results
        await asyncio.sleep(0.5)
        
        if not tool_results:
            return "我尝试帮助您，但无法获取您请求的信息。"
        
        # Generate response based on tool results
        response_parts = []
        
        for result in tool_results:
            if not result.get("success"):
                tool_name_cn = result['tool'].replace('_', ' ')
                response_parts.append(f"由于错误，我无法执行{tool_name_cn}操作。")
                continue
            
            tool_name = result["tool"]
            tool_result = result["result"]
            
            if tool_name == "get_weather":
                response_parts.append(
                    f"The weather in {tool_result['location']} is {tool_result['temperature']} "
                    f"with {tool_result['condition'].lower()} conditions."
                )
            
            elif tool_name == "search_web":
                response_parts.append(
                    f"I found some information about '{tool_result['query']}'. "
                    f"Here are the top results: {tool_result['results'][0]['title']} - {tool_result['results'][0]['snippet']}"
                )
            
            elif tool_name == "set_reminder":
                response_parts.append(
                    f"I've set a reminder for '{tool_result['message']}' {tool_result['scheduled_time']}."
                )
            
            elif tool_name == "play_music":
                response_parts.append(
                    f"Now playing: {tool_result['playing']}"
                )
            
            elif tool_name == "control_smart_home":
                response_parts.append(
                    f"I've {tool_result['action']} the {tool_result['device']}."
                )

            elif tool_name == "datetime":
                # Handle datetime tool results in Chinese
                if tool_result.get("operation") == "current_time":
                    formatted_time = tool_result.get("formatted", "")
                    time_only = tool_result.get("time", "")
                    if formatted_time:
                        # Parse the formatted time to create a natural Chinese response
                        try:
                            from datetime import datetime
                            dt = datetime.strptime(formatted_time, "%Y-%m-%d %H:%M:%S")
                            hour = dt.hour
                            minute = dt.minute

                            # Convert to Chinese time format
                            if hour < 12:
                                period = "上午"
                            elif hour < 18:
                                period = "下午"
                                hour = hour - 12 if hour > 12 else hour
                            else:
                                period = "晚上"
                                hour = hour - 12 if hour > 12 else hour

                            if hour == 0:
                                hour = 12

                            response_parts.append(f"现在是{period}{hour}点{minute:02d}分")
                        except:
                            response_parts.append(f"现在的时间是{time_only}")
                    else:
                        response_parts.append("现在的时间是" + str(tool_result.get("time", "未知")))
                else:
                    response_parts.append("时间查询完成")

            elif tool_name == "calculator":
                # Handle calculator tool results in Chinese
                expression = tool_result.get("expression", "")
                result_value = tool_result.get("result", "")
                if expression and result_value is not None:
                    response_parts.append(f"{expression} 的计算结果是 {result_value}")
                else:
                    response_parts.append("计算完成")

            else:
                # Generic fallback for unknown tools
                response_parts.append(f"{tool_name}工具执行完成")

        return " ".join(response_parts) if response_parts else "我已经完成了您的请求。"
    
    # Helper methods for parameter extraction
    def _extract_location(self, text: str) -> Optional[str]:
        """Extract location from text."""
        # Simple extraction - in real implementation would use NER
        words = text.lower().split()
        location_indicators = ["in", "at", "for"]
        
        for i, word in enumerate(words):
            if word in location_indicators and i + 1 < len(words):
                return words[i + 1].title()
        
        return None
    
    def _extract_time(self, text: str) -> Optional[str]:
        """Extract time from text."""
        # Simple extraction
        if "tomorrow" in text.lower():
            return "tomorrow"
        elif "hour" in text.lower():
            return "in 1 hour"
        elif "minute" in text.lower():
            return "in 10 minutes"
        
        return None
    
    def _extract_device(self, text: str) -> str:
        """Extract device from text."""
        text_lower = text.lower()
        if "light" in text_lower:
            return "lights"
        elif "thermostat" in text_lower or "temperature" in text_lower:
            return "thermostat"
        else:
            return "unknown device"
    
    def _extract_action(self, text: str) -> str:
        """Extract action from text."""
        text_lower = text.lower()
        if "turn on" in text_lower or "on" in text_lower:
            return "turn on"
        elif "turn off" in text_lower or "off" in text_lower:
            return "turn off"
        elif "set" in text_lower:
            return "set"
        else:
            return "control"
