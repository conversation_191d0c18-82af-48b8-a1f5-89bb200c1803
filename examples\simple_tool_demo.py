"""
简化的工具调用演示 - 不依赖Ray

这个演示展示了工具系统的核心功能，无需Ray集群。
"""

import asyncio
import logging
from audio_agent.tools.builtin import register_builtin_tools
from audio_agent.tools import get_tool_registry, ToolExecutor
from audio_agent.knowledge import SimpleKnowledgeBase, KnowledgeItem

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def demo_basic_tools():
    """演示基础工具功能"""
    print("\n🔧 基础工具演示")
    print("-" * 40)
    
    # 注册内置工具
    register_builtin_tools()
    
    # 获取工具注册表和执行器
    registry = get_tool_registry()
    executor = ToolExecutor(registry)
    
    print(f"✅ 已注册 {len(registry)} 个工具:")
    for tool_name in registry.list_tools():
        tool = registry.get_tool(tool_name)
        print(f"  - {tool_name}: {tool.description}")
    
    print("\n" + "="*50)
    
    # 测试计算器工具
    print("\n📊 测试计算器工具:")
    test_expressions = [
        "2 + 3 * 4",
        "sqrt(16)",
        "sin(pi/2)",
        "log(10)"
    ]
    
    for expr in test_expressions:
        try:
            result = await executor.execute_tool_call("calculator", {"expression": expr})
            if result.success:
                print(f"  {expr} = {result.data['formatted_result']}")
            else:
                print(f"  {expr} -> 错误: {result.error}")
        except Exception as e:
            print(f"  {expr} -> 异常: {e}")
    
    # 测试时间工具
    print("\n⏰ 测试时间工具:")
    time_operations = [
        ("current_time", "获取当前时间"),
        ("current_date", "获取当前日期"),
        ("current_datetime", "获取当前日期时间")
    ]
    
    for operation, description in time_operations:
        try:
            result = await executor.execute_tool_call("datetime", {"operation": operation})
            if result.success:
                if operation == "current_time":
                    print(f"  {description}: {result.data['time']}")
                elif operation == "current_date":
                    print(f"  {description}: {result.data['date']} ({result.data['weekday']})")
                elif operation == "current_datetime":
                    print(f"  {description}: {result.data['datetime']}")
            else:
                print(f"  {description} -> 错误: {result.error}")
        except Exception as e:
            print(f"  {description} -> 异常: {e}")
    
    # 测试搜索工具（可能因网络问题失败）
    print("\n🔍 测试搜索工具:")
    try:
        result = await executor.execute_tool_call("web_search", {
            "query": "Python programming tutorial",
            "max_results": 2
        })
        if result.success and result.data.get('results'):
            print(f"  搜索到 {result.data['count']} 个结果:")
            for i, item in enumerate(result.data['results'], 1):
                title = item.get('title', 'No title')[:50]
                snippet = item.get('snippet', 'No snippet')[:80]
                print(f"    {i}. {title}")
                print(f"       {snippet}...")
        else:
            print(f"  搜索失败或无结果: {result.error if not result.success else '无结果'}")
    except Exception as e:
        print(f"  搜索异常: {e}")


async def demo_knowledge_base():
    """演示知识库功能"""
    print("\n📚 知识库演示")
    print("-" * 40)
    
    # 创建临时知识库
    import tempfile
    import os
    
    temp_dir = tempfile.mkdtemp()
    kb_path = os.path.join(temp_dir, "demo_kb.json")
    
    kb = SimpleKnowledgeBase("demo_kb", kb_path)
    
    # 添加示例知识项
    knowledge_items = [
        KnowledgeItem(
            id="python_basics",
            title="Python基础语法",
            content="Python是一种解释型、面向对象的编程语言。它具有简洁的语法，支持多种编程范式，包括面向过程、面向对象和函数式编程。Python的设计哲学强调代码的可读性和简洁性。",
            category="编程语言",
            tags=["Python", "编程", "语法", "基础"]
        ),
        KnowledgeItem(
            id="ai_ml_intro",
            title="人工智能与机器学习简介",
            content="人工智能(AI)是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。机器学习是AI的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
            category="人工智能",
            tags=["AI", "机器学习", "深度学习", "算法"]
        ),
        KnowledgeItem(
            id="web_development",
            title="Web开发技术栈",
            content="现代Web开发涉及前端和后端技术。前端技术包括HTML、CSS、JavaScript和各种框架如React、Vue.js。后端技术包括服务器语言如Python、Node.js、Java，以及数据库如MySQL、MongoDB。",
            category="Web开发",
            tags=["HTML", "CSS", "JavaScript", "React", "Node.js", "数据库"]
        ),
        KnowledgeItem(
            id="data_science",
            title="数据科学工具与方法",
            content="数据科学结合了统计学、计算机科学和领域专业知识来从数据中提取洞察。常用工具包括Python的pandas、numpy、scikit-learn，R语言，以及可视化工具如matplotlib、seaborn。",
            category="数据科学",
            tags=["数据分析", "pandas", "numpy", "可视化", "统计学"]
        )
    ]
    
    # 添加知识项到知识库
    print("📝 添加知识项:")
    for item in knowledge_items:
        success = await kb.add_item(item)
        status = "✅" if success else "❌"
        print(f"  {status} {item.title}")
    
    # 显示知识库统计
    stats = kb.get_stats()
    print(f"\n📊 知识库统计:")
    print(f"  总项目数: {stats['total_items']}")
    print(f"  分类数: {stats['categories']}")
    print("  分类详情:")
    for category, count in stats['category_breakdown'].items():
        print(f"    - {category}: {count} 项")
    
    # 测试搜索功能
    print(f"\n🔍 搜索测试:")
    search_queries = [
        "Python编程",
        "人工智能",
        "Web开发",
        "数据分析",
        "机器学习算法"
    ]
    
    for query in search_queries:
        results = await kb.search(query, max_results=3)
        print(f"\n  搜索 '{query}':")
        if results:
            for i, result in enumerate(results, 1):
                print(f"    {i}. {result.title}")
                print(f"       分类: {result.category}")
                print(f"       内容: {result.content[:60]}...")
                if result.tags:
                    print(f"       标签: {', '.join(result.tags[:3])}")
        else:
            print("    没有找到相关结果")
    
    # 清理临时文件
    try:
        os.remove(kb_path)
        os.rmdir(temp_dir)
    except:
        pass


async def demo_tool_schemas():
    """演示工具模式"""
    print("\n🔧 工具模式演示")
    print("-" * 40)
    
    registry = get_tool_registry()
    
    print("📋 所有工具的函数模式 (Volcengine格式):")
    schemas = registry.get_function_schemas()
    
    for schema in schemas:
        function_info = schema["function"]
        print(f"\n🔹 工具: {function_info['name']}")
        print(f"   描述: {function_info['description']}")
        print(f"   参数:")
        
        properties = function_info["parameters"]["properties"]
        required = function_info["parameters"]["required"]
        
        for param_name, param_info in properties.items():
            required_mark = "* " if param_name in required else "  "
            param_type = param_info["type"]
            param_desc = param_info["description"]
            print(f"     {required_mark}{param_name} ({param_type}): {param_desc}")


async def main():
    """主演示函数"""
    print("🎯 AudioAgent 工具调用和知识增强演示")
    print("=" * 50)
    print("注意: 这是简化版演示，不需要Ray集群")
    
    try:
        # 演示1: 基础工具
        await demo_basic_tools()
        
        # 演示2: 知识库
        await demo_knowledge_base()
        
        # 演示3: 工具模式
        await demo_tool_schemas()
        
        print("\n" + "="*50)
        print("✅ 演示完成！")
        print("\n💡 功能总结:")
        print("  🔧 工具调用系统 - 支持计算、时间查询、网络搜索")
        print("  📚 知识库系统 - 支持知识存储、搜索和管理")
        print("  🤖 智能代理 - 自动判断何时使用工具")
        print("  ⚙️  可配置系统 - 灵活的配置选项")
        print("  🔒 安全机制 - 参数验证和错误处理")
        
        print("\n🚀 下一步:")
        print("  1. 集成到AudioAgent的语音对话系统")
        print("  2. 添加更多自定义工具")
        print("  3. 扩展知识库功能")
        print("  4. 优化智能代理的决策逻辑")
        
    except Exception as e:
        logger.error(f"演示过程中出现错误: {e}")
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
