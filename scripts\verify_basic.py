#!/usr/bin/env python3
"""
Basic verification script for the audio agent.

This script verifies that the basic components are working correctly.
"""

import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.config import get_config, AudioAgentConfig
from audio_agent.core.protocol import ProtocolHandler, MessageType, encode_message, decode_message


def test_config():
    """Test configuration loading."""
    print("Testing configuration...")
    
    config = get_config()
    assert isinstance(config, AudioAgentConfig)
    
    print(f"✓ Input audio sample rate: {config.input_audio.sample_rate}")
    print(f"✓ Output audio sample rate: {config.output_audio.sample_rate}")
    print(f"✓ WebSocket base URL: {config.websocket.base_url}")
    print(f"✓ Bot name: {config.session.dialog.bot_name}")
    
    print("Configuration test passed!\n")


def test_protocol():
    """Test protocol encoding/decoding."""
    print("Testing protocol...")
    
    # Test basic encoding/decoding
    test_data = {
        "type": "test",
        "message": "Hello, world!",
        "timestamp": 1234567890
    }
    
    # Test with protocol handler
    handler = ProtocolHandler()
    encoded = handler.encode(test_data, MessageType.CLIENT_FULL_REQUEST)
    decoded = handler.decode(encoded)
    
    assert decoded == test_data
    print(f"✓ Protocol encoding/decoding works")
    print(f"✓ Original data: {test_data}")
    print(f"✓ Decoded data: {decoded}")
    
    # Test direct functions
    encoded_direct = encode_message(test_data)
    decoded_direct = decode_message(encoded_direct)
    
    assert decoded_direct == test_data
    print(f"✓ Direct encoding/decoding works")
    
    print("Protocol test passed!\n")


def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        from audio_agent import __version__
        print(f"✓ Main package version: {__version__}")
        
        from audio_agent.config import (
            AudioAgentConfig, WebSocketConfig, InputAudioConfig, OutputAudioConfig
        )
        print("✓ Config classes imported successfully")
        
        from audio_agent.core.protocol import (
            ProtocolHandler, MessageType, encode_message, decode_message
        )
        print("✓ Protocol classes imported successfully")
        
        from audio_agent.core.session_manager import SessionManager
        print("✓ SessionManager imported successfully")
        
        print("Import test passed!\n")
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    
    return True


def main():
    """Main verification function."""
    print("=== Audio Agent Basic Verification ===\n")
    
    try:
        # Test imports first
        if not test_imports():
            sys.exit(1)
        
        # Test configuration
        test_config()
        
        # Test protocol
        test_protocol()
        
        print("🎉 All basic tests passed!")
        print("The audio agent basic components are working correctly.")
        
    except Exception as e:
        print(f"✗ Verification failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
