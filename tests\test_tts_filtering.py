"""
Test TTS filtering logic to ensure server auto-responses are properly discarded.
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from src.audio_agent.events.event_processor import EventProcessor
from src.audio_agent.events.event_handler import EventHandler
from src.audio_agent.core.session_manager import SessionManager
from src.audio_agent.config.settings import AudioConfig


@pytest.fixture
def audio_config():
    """Create a test audio configuration."""
    return AudioConfig()


@pytest.fixture
def mock_session_manager():
    """Create a mock session manager."""
    session_manager = MagicMock()
    session_manager.audio_output_actor = AsyncMock()
    session_manager.audio_output_actor.queue_audio_data = AsyncMock(return_value=True)
    return session_manager


@pytest.fixture
def event_processor(audio_config, mock_session_manager):
    """Create an event processor for testing."""
    processor = EventProcessor(
        session_id="test-session",
        config=audio_config,
        session_manager=mock_session_manager
    )
    return processor


@pytest.mark.asyncio
async def test_tts_filtering_default_type_during_tool_calling(event_processor):
    """Test that default TTS type is filtered during tool calling."""
    # Activate tool calling mode
    event_processor.tool_calling_active = True
    
    # Simulate TTS sentence start with default type
    tts_start_message = {
        'message_type': 'SERVER_ACK',
        'event': 350,  # TTS_SENTENCE_START
        'session_id': 'test-session',
        'payload': {
            'tts_type': 'default',
            'text': 'This is server auto response',
            'tts_task_id': 'task-123'
        }
    }
    
    # Process the TTS start event
    await event_processor.process_websocket_message(tts_start_message)
    
    # Verify that current_tts_should_skip is set to True
    assert event_processor.current_tts_should_skip == True
    assert event_processor.server_auto_response_started == True


@pytest.mark.asyncio
async def test_tts_filtering_chat_tts_text_during_tool_calling(event_processor):
    """Test that chat_tts_text type is allowed during tool calling."""
    # Activate tool calling mode
    event_processor.tool_calling_active = True
    
    # Simulate TTS sentence start with chat_tts_text type
    tts_start_message = {
        'message_type': 'SERVER_ACK',
        'event': 350,  # TTS_SENTENCE_START
        'session_id': 'test-session',
        'payload': {
            'tts_type': 'chat_tts_text',
            'text': 'This is tool result response',
            'tts_task_id': 'task-456'
        }
    }
    
    # Process the TTS start event
    await event_processor.process_websocket_message(tts_start_message)
    
    # Verify that current_tts_should_skip is set to False
    assert event_processor.current_tts_should_skip == False
    assert event_processor.tool_response_tts_task_id == 'task-456'


@pytest.mark.asyncio
async def test_audio_discarding_in_session_manager(audio_config):
    """Test that audio is actually discarded in session manager when filtering is active."""
    # Create a real session manager with mocked audio actor
    session_manager = SessionManager(config=audio_config)
    session_manager.audio_output_actor = AsyncMock()
    session_manager.audio_output_actor.queue_audio_data = AsyncMock(return_value=True)
    
    # Create event processor with filtering active
    event_processor = EventProcessor(
        session_id="test-session",
        config=audio_config,
        session_manager=session_manager
    )
    event_processor.current_tts_should_skip = True  # Simulate filtering active
    session_manager.event_processor = event_processor
    
    # Simulate audio response message
    audio_message = {
        'message_type': 'SERVER_ACK',
        'event': 352,  # TTS_RESPONSE
        'session_id': 'test-session',
        'payload': b"fake_audio_data_should_be_discarded"
    }
    
    # Process the audio message
    await session_manager._handle_server_response(audio_message)
    
    # Verify that audio was NOT queued (because it should be discarded)
    session_manager.audio_output_actor.queue_audio_data.assert_not_called()
    
    # Verify that AI is not marked as speaking
    assert session_manager.ai_is_speaking == False


@pytest.mark.asyncio
async def test_audio_allowed_when_filtering_inactive(audio_config):
    """Test that audio is allowed when filtering is not active."""
    # Create a real session manager with mocked audio actor
    session_manager = SessionManager(config=audio_config)
    session_manager.audio_output_actor = AsyncMock()
    session_manager.audio_output_actor.queue_audio_data = AsyncMock(return_value=True)
    
    # Create event processor with filtering inactive
    event_processor = EventProcessor(
        session_id="test-session",
        config=audio_config,
        session_manager=session_manager
    )
    event_processor.current_tts_should_skip = False  # Filtering inactive
    session_manager.event_processor = event_processor
    
    # Simulate audio response message
    audio_message = {
        'message_type': 'SERVER_ACK',
        'event': 352,  # TTS_RESPONSE
        'session_id': 'test-session',
        'payload': b"fake_audio_data_should_be_played"
    }
    
    # Process the audio message
    await session_manager._handle_server_response(audio_message)
    
    # Verify that audio WAS queued
    session_manager.audio_output_actor.queue_audio_data.assert_called_once_with(b"fake_audio_data_should_be_played")
    
    # Verify that AI is marked as speaking
    assert session_manager.ai_is_speaking == True


@pytest.mark.asyncio
async def test_complete_filtering_workflow(event_processor, mock_session_manager):
    """Test the complete workflow from TTS start to audio discarding."""
    # Set up session manager reference
    event_processor.session_manager = mock_session_manager
    mock_session_manager.event_processor = event_processor
    
    # Step 1: Activate tool calling
    event_processor.tool_calling_active = True
    
    # Step 2: Process TTS start with default type
    tts_start_message = {
        'message_type': 'SERVER_ACK',
        'event': 350,  # TTS_SENTENCE_START
        'session_id': 'test-session',
        'payload': {
            'tts_type': 'default',
            'text': 'Server auto response',
            'tts_task_id': 'task-123'
        }
    }
    await event_processor.process_websocket_message(tts_start_message)
    
    # Verify filtering is activated
    assert event_processor.current_tts_should_skip == True
    
    # Step 3: Process audio data - should be discarded
    audio_message = {
        'message_type': 'SERVER_ACK',
        'event': 352,  # TTS_RESPONSE
        'session_id': 'test-session',
        'payload': b"audio_data_to_discard"
    }
    
    # Mock the session manager's audio handling
    with patch.object(mock_session_manager, '_handle_server_response') as mock_handle:
        await event_processor.process_websocket_message(audio_message)
        mock_handle.assert_called_once()
