# AudioAgent 知识库

这个目录用于存储AudioAgent的知识库文件。

## 目录结构

```
knowledge/
├── README.md           # 本文件
├── default.json        # 默认知识库
├── tech/              # 技术相关知识
├── general/           # 通用知识
└── custom/            # 自定义知识
```

## 知识库格式

知识库文件使用JSON格式存储：

```json
{
  "name": "知识库名称",
  "items": [
    {
      "id": "唯一标识符",
      "title": "知识标题",
      "content": "知识内容",
      "category": "分类",
      "tags": ["标签1", "标签2"],
      "metadata": {},
      "created_at": "2025-07-26T19:12:00",
      "updated_at": "2025-07-26T19:12:00"
    }
  ]
}
```

## 使用方法

1. **添加知识**: 通过代码或API添加新的知识项
2. **搜索知识**: 使用关键词搜索相关知识
3. **分类管理**: 按类别组织知识内容
4. **标签系统**: 使用标签进行细粒度分类

## 配置

在 `.env` 文件中配置知识库路径：

```bash
AUDIO_AGENT_KNOWLEDGE_KNOWLEDGE_BASE_PATH=./knowledge
```

## 示例

参考 `examples/simple_tool_demo.py` 中的知识库使用示例。
