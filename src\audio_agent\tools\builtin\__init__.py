"""
Built-in tools for the audio agent.

This module contains pre-built tools that provide common functionality
like web search, calculations, time queries, etc.
"""

from .search import WebSearchTool
from .calculator import CalculatorTool
from .datetime_tool import DateTimeTool

__all__ = [
    "WebSearchTool",
    "CalculatorTool", 
    "DateTimeTool",
]


def register_builtin_tools():
    """Register all built-in tools to the global registry."""
    from ..registry import register_tool_class
    
    # Register all built-in tools
    register_tool_class(WebSearchTool)
    register_tool_class(CalculatorTool)
    register_tool_class(DateTimeTool)
