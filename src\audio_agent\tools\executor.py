"""
Tool executor for executing tool calls.

This module provides the execution engine for tool calls,
handling the invocation of tools and managing their results.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional
from .base import BaseTool, ToolR<PERSON>ult, ToolError
from .registry import ToolRegistry, get_tool_registry

logger = logging.getLogger(__name__)


class ToolExecutor:
    """
    Executor for tool calls.
    
    This class handles the execution of tool calls from the AI model,
    managing the invocation process and result handling.
    """
    
    def __init__(self, registry: Optional[ToolRegistry] = None):
        """
        Initialize the tool executor.
        
        Args:
            registry: Tool registry to use (defaults to global registry)
        """
        self.registry = registry or get_tool_registry()
        self.execution_timeout = 30.0  # Default timeout in seconds
    
    async def execute_tool_call(
        self, 
        tool_name: str, 
        parameters: Dict[str, Any]
    ) -> ToolResult:
        """
        Execute a single tool call.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Parameters to pass to the tool
            
        Returns:
            ToolResult containing the execution result
        """
        try:
            # Get the tool
            tool = self.registry.get_tool(tool_name)
            if tool is None:
                return ToolResult(
                    success=False,
                    error=f"Tool '{tool_name}' not found"
                )
            
            logger.info(f"Executing tool: {tool_name} with parameters: {parameters}")
            
            # Validate parameters
            validated_params = tool.validate_parameters(**parameters)
            
            # Execute the tool with timeout
            result = await asyncio.wait_for(
                tool.execute(**validated_params),
                timeout=self.execution_timeout
            )
            
            logger.info(f"Tool {tool_name} executed successfully")
            return result
            
        except ToolError as e:
            logger.error(f"Tool error in {tool_name}: {e}")
            return ToolResult(
                success=False,
                error=str(e),
                metadata={"tool_name": tool_name, "error_code": e.error_code}
            )
        
        except asyncio.TimeoutError:
            logger.error(f"Tool {tool_name} execution timed out")
            return ToolResult(
                success=False,
                error=f"Tool execution timed out after {self.execution_timeout} seconds",
                metadata={"tool_name": tool_name, "error_code": "TIMEOUT"}
            )
        
        except Exception as e:
            logger.error(f"Unexpected error in tool {tool_name}: {e}")
            return ToolResult(
                success=False,
                error=f"Unexpected error: {str(e)}",
                metadata={"tool_name": tool_name, "error_code": "UNEXPECTED_ERROR"}
            )
    
    async def execute_tool_calls(
        self, 
        tool_calls: List[Dict[str, Any]]
    ) -> List[ToolResult]:
        """
        Execute multiple tool calls.
        
        Args:
            tool_calls: List of tool call dictionaries in Volcengine format
            
        Returns:
            List of ToolResult objects
        """
        results = []
        
        for tool_call in tool_calls:
            try:
                # Parse Volcengine tool call format
                function_info = tool_call.get("function", {})
                tool_name = function_info.get("name", "")
                
                # Parse parameters (may be JSON string)
                parameters_raw = function_info.get("arguments", {})
                if isinstance(parameters_raw, str):
                    try:
                        parameters = json.loads(parameters_raw)
                    except json.JSONDecodeError:
                        results.append(ToolResult(
                            success=False,
                            error=f"Invalid JSON in tool call arguments: {parameters_raw}"
                        ))
                        continue
                else:
                    parameters = parameters_raw
                
                # Execute the tool call
                result = await self.execute_tool_call(tool_name, parameters)
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error processing tool call: {e}")
                results.append(ToolResult(
                    success=False,
                    error=f"Error processing tool call: {str(e)}"
                ))
        
        return results
    
    def set_timeout(self, timeout: float) -> None:
        """
        Set the execution timeout for tools.
        
        Args:
            timeout: Timeout in seconds
        """
        self.execution_timeout = timeout
        logger.info(f"Tool execution timeout set to {timeout} seconds")
    
    def get_available_tools(self) -> List[str]:
        """
        Get list of available tools.
        
        Returns:
            List of tool names
        """
        return self.registry.list_tools()
    
    def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """
        Get function schemas for all available tools.
        
        Returns:
            List of function schemas in Volcengine format
        """
        return self.registry.get_function_schemas()
    
    async def test_tool(self, tool_name: str, parameters: Dict[str, Any]) -> ToolResult:
        """
        Test a tool with given parameters.
        
        Args:
            tool_name: Name of the tool to test
            parameters: Test parameters
            
        Returns:
            ToolResult containing the test result
        """
        logger.info(f"Testing tool: {tool_name}")
        return await self.execute_tool_call(tool_name, parameters)
