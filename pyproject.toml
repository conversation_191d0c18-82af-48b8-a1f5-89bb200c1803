[project]
name = "audio-agent"
version = "0.1.0"
description = "实时语音对话系统，支持语音输入和语音输出，基于Ray和uv构建"
readme = "README.md"
authors = [
    { name = "苏兆强", email = "su<PERSON><PERSON><PERSON><PERSON>@kaadas.com" }
]
requires-python = ">=3.10"
dependencies = [
    "pyaudio>=0.2.11",
    "websockets>=11.0",
    "ray[default]>=2.8.0",
    "pydantic>=2.0.0",
    "asyncio-mqtt>=0.16.0",
    "numpy>=1.24.0",
    "psutil>=7.0.0",
    "pydantic-settings>=2.10.1",
    "torch>=2.7.1",
    "silero-vad>=5.1.2",
    "webrtcvad>=2.0.10",
    "openai>=1.97.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.5.0",
    "ruff>=0.1.0",
]

[project.scripts]
audio-agent = "audio_agent.main:main"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.ruff]
line-length = 88
target-version = "py310"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
]

# 配置阿里云 PyPI 镜像源
[[tool.uv.index]]
name = "aliyun"
url = "https://mirrors.aliyun.com/pypi/simple/"
default = true
