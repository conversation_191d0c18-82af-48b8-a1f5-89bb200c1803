# 🧹 AudioAgent 二进制日志清理修复

## 🎯 问题描述

用户反馈AudioAgent在运行时输出大量不可读的二进制数据到日志中，严重影响日志可读性：

```
ba\xfc\xffo\xba\xfd\xff\x87\xba\xfd\xff\x87\xba\xfb\xff\x7f\xba\xfc\xff_\xba\xfc\xffo\xba\xfc\xff_\xba\xfd\xff\x87\xba\xfc\xff\x8f\xba\xfc\xff\x8f\xba\xfc\xff\x8f\xba\xfa\xff\xa7\xba\xfa\xff\xa7...
```

这些二进制数据是音频流的原始字节，不应该直接显示在日志中。

## ✅ 解决方案

### 1. 创建日志清理工具

#### 📁 `src/audio_agent/utils/logging_utils.py`
```python
def clean_data_for_logging(data: Any, max_str_length: int = 200) -> Any:
    """清理数据用于日志输出，替换二进制数据和截断长字符串"""
    if isinstance(data, bytes):
        return f"<binary_data:{len(data)}bytes>"
    
    if isinstance(data, str) and len(data) > max_str_length:
        return f"{data[:max_str_length]}...<truncated:{len(data)}chars>"
    
    if isinstance(data, dict):
        return {k: clean_data_for_logging(v, max_str_length) for k, v in data.items()}
    
    # 递归处理嵌套结构...
```

### 2. 专用日志函数

```python
def log_response_summary(logger: logging.Logger, response: Dict[str, Any]):
    """记录响应摘要，不包含二进制数据"""
    
def log_websocket_message(logger: logging.Logger, message: Dict[str, Any], direction: str):
    """记录WebSocket消息，自动清理二进制数据"""
    
def log_audio_data(logger: logging.Logger, data: bytes, context: str):
    """记录音频数据信息，不包含实际二进制内容"""
```

### 3. 更新核心模块

#### 📁 `src/audio_agent/core/session_manager.py`
```python
# 替换原来的复杂日志清理代码
if response:
    log_response_summary(logger, response)
```

#### 📁 `src/audio_agent/core/actors.py`
```python
def _clean_response_for_logging(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
    """清理响应数据用于日志"""
    # 递归清理二进制数据...

# 在日志输出前清理数据
log_data = self._clean_response_for_logging(response_data)
logger.info(f"StartConnection completed: {log_data}")
```

## 📊 效果对比

### ❌ 修复前
```
2025-07-26 20:33:08,755 - INFO - Received response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6b1fbbdb-f098-4257-a0c8-db9eb4a72c2d', 'payload': {}, 'payload_msg': ba\xfc\xffo\xba\xfd\xff\x87\xba\xfd\xff\x87\xba\xfb\xff\x7f\xba\xfc\xff_\xba\xfc\xffo\xba\xfc\xff_\xba\xfd\xff\x87\xba\xfc\xff\x8f\xba\xfc\xff\x8f\xba\xfc\xff\x8f\xba\xfa\xff\xa7\xba\xfa\xff\xa7\xba\xfa\xff\xa7\xba\xfc\xff\x97\xba\xfc\xff\x97\xba\xfb\xff\x7f\xba\xfc\xff\x8f\xba\xfb\xff\x9f\xba\xfc\xff\x97\xba\xf9\xff\xaf\xba\xfc\xff\x97\xba\xfc\xff\x8f\xba\xfd\xff\x87\xba\xfc\xffo\xba\xfb\xff\x7f\xba\xfe\xff/\xba\xfb\xff\x7f\xba\xfb\xff\x7f\xba\xfc\xff_\xba\xfc\xff_\xba\xfd\xffO\xba\xfe\xff/\xba\xfc\xff_\xba\xfc\xff_\xba\xfe\xff?\xba\xfc\xff_\xba\xfd\xffO\xba\xfc\xffo\xba\xfc\xff_\xba\xfd\xffO\xba\xfe\xff?\xba\xfc\xff_\xba\xfc\xff_\xba\xfd\xffO\xba\xff\xff\x1f\xba\xfe\xff?\xba\xfc\xff_\xba\xfe\xff?\xba\xfd\xffO\xba\xfc\xff_\xba\xfd\xffO\xba\xfc\xffo\xba\xfd\xff\x87\xba\xfc\xffo\xba\xfc\xff_\xba\xfb\xff\x7f\xba\xfb\xff\x7f\xba\xfe\xff/\xba\xfe\xff?\xba\xfc\xff_\xba\xfc\xff_\xba\xfc\xffo\xba\xfc\xffo\xba\xfd\xff\x87\xba\xfd\xff\x87\xba\xfc\xff\x8f\xba\xfc\xff_\xba\xfc\xffo\xba\xfd\xffO\xba\xfd\xffO\xba\xfc\xff_\xba\xfc\xff_\xba\xfc\xff_\xba\xfe\xff/\xba\xff\xff\x0f\xba\xff\xff\x0f\xba\xff\xff\xff\xb9\xff\xff\xff\xb9\xff\xff\xbf\xb9\xff\xff\xbf\xb9\xff\xff\xdf\xb9\xff\xff\xdf\xb9\xff\xff\xdf\xb9\xff\xff\x0f\xba\xff\xff\xff\xb9\xff\xff\xff\xb9\xff\xff\xdf\xb9\x00\x00\xa0\xb9\x00\x00\x80\xb9\xff\xff\xbf\xb9\x00\x00\xa0\xb9\x00\x00@\xb9\x00\x00\x00\xb9\x00\x00@\xb9\x00\x00\x00\xb9\xff\xff\xdf\xb9\x00\x00\x80\xb9\x00\x00\x00\xb9\x00\x00\xa0\xb9\xff\xff\xdf\xb9\x00\x00@\xb9\x00\x00\xa0\xb9\x00\x00\x00\xb9\x00\x00\x00\x00\x00\x00\x00\xb9\x00\x00@\xb9\x00\x00\x80\xb8\x00\x00\x80\xb8\x00\x00@\xb9\x00\x00\xa0\xb9\x00\x00\x00\xb9\x00\x00\x00\x00\x00\x00\x80\xb9\x00\x00\x80\xb8\x00\x00\x00\x00\x00\x00\x80\xb8\x00\x00\x808\x00\x00\x808\x00\x00\x00\x00\x00\x00\x009\x00\x00\x808\x00\x00\x808\x00\x00\x009\x00\x00\x00\x00\x00\x00\x808\x00\x00\x009\x00\x00\x80\xb8\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x808\x00\x00\x809\xff\xff\xdf9\xff\xff\xbf9\xff\xff\xdf9\xff\xff\xbf9\xff\xff\x0f:\xff\xff\x1f:\xfe\xff/:\xfe\xff/:\xff\xff\xdf9\xff\xff\x0f:\x00\x00\x809\x00\x00\xa09\xff\xff\xff9\xff\xff\xbf9\xff\xff\xbf9\xff\xff\xbf9\x00\x00\xa09\xff\xff\xbf9\xff\xff\x0f:\xfd\xffO:\xfe\xff?:\xff\xff\xff9\xfe\xff?:\xff\xff\xdf9\xff\xff\xdf9\xff\xff\x1f:\xff\xff\x0f:\xff\xff\xbf9\xff\xff\xff9\xfe\xff/:\xfe\xff/:\xfe\xff/:\xff\xff\x1f:\xff\xff\xff9\xff\xff\x1f:\xff\xff\xff9\xff\xff\xdf9\xff\xff\x0f:\xff\xff\xdf9\xfe\xff/:\xfe\xff/:\xfc\xff_:\xfd\xffO:\xfc\xff_:\xfe\xff?:\xfe\xff/:\xfc\xff_:\xfd\xff\x87:\xfd\xff\x87:\xfd\xff\x87:\xfb\xff\x9f:\xfb\xff\x9f:\xfb\xff\x9f:\xfc\xff\x97:\xfc\xff\x8f:\xfa\xff\xa7:\xf8\xff\xb7:\xf9\xff\xaf:\xfa\xff\xa7:\xf7\xff\xbf:\xf5\xff\xcf:\xf5\xff\xcf:\xf9\xff\xaf:\xfa\xff\xa7:\xf8\xff\xb7:\xfa\xff\xa7:\xfb\xff\x9f:\xf9\xff\xaf:\xf7\xff\xbf:\xfb\xff\x9f:\xfa\xff\xa7:\xf8\xff\xb7:\xfa\xff\xa7:\xf9\xff\xaf:\xf7\xff\xbf:\xf7\xff\xbf:\xfb\xff\x9f:\xf8\xff\xb7:\xf6\xff\xc7:\xf7\xff\xbf:\xf5\xff\xcf:\xf6\xff\xc7:\xf9\xff\xaf:\xf8\xff\xb7:\xf6\xff\xc7:\xf3\xff\xd7:\xf5\xff\xcf:\xf5\xff\xcf:\xf7\xff\xbf:\xf9\xff\xaf:\xf9\xff\xaf:\xf9\xff\xaf:\xfc\xff\x97:\xfa\xff\xa7:\xfc\xff\x97:\xfc\xff_:\xfc\xffo:\xfc\xff_:\xfd\xffO:\xfe\xff?:\xff\xff\xff9\xfe\xff/:\xfe\xff/:\xff\xff\x0f:\xff\xff\x1f:\xfd\xffO:\xfe\xff?:\xfe\xff/:\xfe\xff?:\xfe\xff?:\xfc\xffo:\xfb\xff\x7f:\xfd\xff\x87:\xfc\xff\x8f:\xfb\xff\x9f:\xfc\xff\x8f:\xfb\xff\x7f:\xfb\xff\x7f:\xfc\xff\x8f:\xfc\xffo:\xfe\xff?:\xfe\xff?:\xfd\xffO:\xfc\xffo:\xfd\xff\x87:\xfc\xffo:\xfc\xff\x8f:\xf8\xff\xb7:\xfb\xff\x9f:\xf8\xff\xb7:\xf8\xff\xb7:\xf7\xff\xbf:\xf3\xff\xd7:\xf8\xff\xb7:\xf5\xff\xcf:\xf6\xff\xc7:\xf0\xff\xe7:\xf2\xff\xdf:\xf0\xff\xe7:\xf4\xff\x03;\xf4\xff\x03;\xed\xff\xf7:\xf2\xff\x0b;\xf4\xff\x03;\xeb\xff\xff:\xf3\xff\x07;\xf2\xff\x0b;\xf3\xff\x07;\xee\xff\xef:\xed\xff\xf7:\xf3\xff\x07;\xee\xff\xef:\xee\xff\xef:\xed\xff\xf7:\xf2\xff\x0b;\xeb\xff\xff:\xf4\xff\x03;\xee\xff\x17;\xee\xff\x17;\xea\xff#;\xeb\xff\x1f;\xf1\xff\x0f;\xf0\xff\x13;\xee\xff\x17;\xee\xff\x17;\xf0\xff\x13;\xee\xff\x17;\xf1\xff\x0f;\xeb\xff\x1f;\xeb\xff\x1f;\xeb\xff\x1f;\xe8\xff\';\xea\xff#;\xe6\xff+;\xe2\xff3;\xde\xff;;\xdc\xff?;\xdc\xff?;\xd2\xffO;\xd7\xffG;\xdc\xff?;\xd2\xffO;\xd5\xffK;\xd7\xffG;\xd7\xffG;\xd0\xffS;\xd2\xffO;\xd7\xffG;\xe0\xff7;\xda\xffC;\xd2\xffO;\xda\xffC;\xd5\xffK;\xd5\xffK;\xd0\xffS;\xc7\xff_;\xcd\xffW;\xd0\xffS;\xd0\xffS;\xca\xff[;\xd2\xffO;\xca\xff[;\xd0\xffS;\xda\xffC;\xd2\xffO;\xd2\xffO;\xc7\xff_;\xc7\xff_;\xcd\xffW;\xc7\xff_;\xd0\xffS;\xc4\xffc;\xbd\xffk;\xc0\xffg;\xbd\xffk;\xc0\xffg;\xbd\xffk;\xbd\xffk;\xaf\xff{;\xaf\xff{;\xcf\xff\x85;\xcd\xff\x87;\xcf\xff\x85;\xc8\xff\x8b;\xc8\xff\x8b;\xc8\xff\x8b;\xcd\xff\x87;\xcd\xff\x87;\xc3\xff\x8f;\xc3\xff\x8f;\xc3\xff\x8f;\xc6\xff\x8d;\xc6\xff\x8d;\xc8\xff\x8b;\xcd\xff\x87;\xcb\xff\x89;\xc6\xff\x8d;\xc1\xff\x91;\xbb\xff\x95;\xc3\xff\x8f;\xc8\xff\x8b;\xbb\xff\x95;\xbb\xff\x95;\xc1\xff\x91;\xc1\xff\x91;\xc3\xff\x8f;\xb9\xff\x97;\xb9\xff\x97;\xbb\xff\x95;\xb9\xff\x97;\xb9\xff\x97;\xbb\xff\x95;\xbb\xff\x95;\xb9\xff\x97;\xc8\xff\x8b;\xc3\xff\x8f;\xb9\xff\x97;\xb3\xff\x9b;\xa3\xff\xa5;\xa3\xff\xa5;\xaa\xff\xa1;\xa3\xff\xa5;\xb3\xff\x9b;\xa3\xff\xa5;\x9c\xff\xa9;\xa0\xff\xa7;\x91\xff\xaf;\xa3\xff\xa5;\x95\xff\xad;\x8d\xff\xb1;\x8d\xff\xb1;\x89\xff\xb3;\x8d\xff\xb1;\x89\xff\xb3;\x81\xff\xb7;\x89\xff\xb3;\x98\xff\xab;\xa6\xff\xa3;\xa6\xff\xa3;\x9c\xff\xa9;\x98\xff\xab;\xa0\xff\xa7;\x91\xff\xaf;\x85\xff\xb5;y\xff\xbb;\x81\xff\xb7;\x81\xff\xb7;\x85\xff\xb5;\x85\xff\xb5;\x81\xff\xb7;\x81\xff\xb7;y\xff\xbb;y\xff\xbb;k\xff\xc1;}\xff\xb9;y\xff\xbb;k\xff\xc1;g\xff\xc3;t\xff\xbd;\x85\xff\xb5;y\xff\xbb;\x8d\xff\xb1;\x98\xff\xab;\x91\xff\xaf;\x8d\xff\xb1;\x81\xff\xb7;t\xff\xbd;\x81\xff\xb7;p\xff\xbf;\x81\xff\xb7;\x81\xff\xb7;p\xff\xbf;\x98\xff\xab;\x89\xff\xb3;}\xff\xb9;}\xff\xb9;y\xff\xbb;y\xff\xbb;k\xff\xc1;p\xff\xbf;}\xff\xb9;\x8d\xff\xb1;y\xff\xbb;}\xff\xb9;\x98\xff\xab;\x8d\xff\xb1;\xaa\xff\xa1;\xaa\xff\xa1;\xa3\xff\xa5;\xb0\xff\x9d;\xad\xff\x9f;\xa6\xff\xa3;\xa0\xff\xa7;\x91\xff\xaf;\x8d\xff\xb1;\x98\xff\xab;\x95\xff\xad;\x95\xff\xad;\x98\xff\xab;\x9c\xff\xa9;\x95\xff\xad;\x95\xff\xad;\x91\xff\xaf;\xa0\xff\xa7;\xb3\xff\x9b;\xad\xff\x9f;\xb0\xff\x9d;\xb6\xff\x99;\xb9\xff\x97;\xc3\xff\x8f;\xcd\xff\x87;\xaf\xff{;\xb2\xffw;\xaf\xff{;\xcd\xffW;\xd0\xffS;\xc0\xffg;\xcd\xffW;\xcd\xffW;\xb6\xffs;\xc4\xffc;\xb6\xffs;\xbd\xffk;\xab\xff\x7f;\xcf\xff\x85;\xb2\xffw;\xb2\xffw;\xba\xffo;\xc4\xffc;\xc0\xffg;\xd2\xffO;\xca\xff[;\xc7\xff_;\xc4\xffc;\xd0\xffS;\xc4\xffc;\xaf\xff{;\xca\xff[;\xc7\xff_;\xd0\xffS;\xda\xffC;\xde\xff;;\xd0\xffS;\xd7\xffG;\xed\xff\x1b;\xf0\xff\x13;\xeb\xff\x1f;\xe8\xff\';\xf0\xff\x13;\xf2\xff\x0b;\xeb\xff\xff:\xf0\xff\xe7:\xfa\xff\xa7:\xfc\xff\x97:\xfd\xff\x87:\xfc\xff\x97:\xf9\xff\xaf:\xf9\xff\xaf:\xfc\xff\x97:\xfa\xff\xa7:\xfc\xff\x8f:\xfe\xff?:\xfc\xff_:\xfc\xff\x8f:\xfc\xffo:\xfb\xff\x7f:\xfa\xff\xa7:\xfd\xff\x87:\xfc\xff\x8f:\xfb\xff\x7f:\xfc\xff\x97:\xfc\xff_:\xfd\xffO:\xfd\xff\x87:\xfd\xffO:\xfd\xffO:\xff\xff\x1f:\xfd\xffO:\xfe\xff?:\xfc\xffo:\xf8\xff\xb7:\xf8\xff\xb7:\xfd\xff\x87:\xfb\xff\x9f:\xf9\xff\xaf:\xf7\xff\xbf:\xf5\xff\xcf:\xf3\xff\x07;\xf0\xff\x13;\xf4\xff\x03;\xed\xff\x1b;\xea\xff#;\xe4\xff/;\xdc\xff?;\xe0\xff7;\xe0\xff7;\xe0\xff7;\xdc\xff?;\xea\xff#;\xe2\xff3;\xda\xffC;\xda\xffC;\xde\xff;;\xe0\xff7;\xd7\xffG;\xda\xffC;\xb6\xffs;\xd5\xffK;\xe4\xff/;\xd0\xffS;\xc4\xffc;\xc7\xff_;\xd0\xffS;\xba\xffo;\xba\xffo;\xb6\xffs;\xcd\xffW;\xcd\xffW;\xc4\xffc;\xbd\xffk;\xcd\xffW;\xd5\xffK;\xcd\xffW;\xdc\xff?;\xc4\xffc;\xb2\xffw;\xc0\xffg;\xd3\xff\x81;\xcd\xff\x87;\xc8\xff\x8b;\xcf\xff\x85;\xc6\xff\x8d;\xb9\xff\x97;\xc3\xff\x8f;\xc3\xff\x8f;\xb6\xff\x99;\xbb\xff\x95;\xc6\xff\x8d;\xc6\xff\x8d;\xc3\xff\x8f;\xb3\xff\x9b;\xb0\xff\x9d;\xb9\xff\x97;\xbb\xff\x95;\xa6\xff\xa3;\xaa\xff\xa1;t\xff\xbd;\x81\xff\xb7;\x8d\xff\xb1;\x81\xff\xb7;p\xff\xbf;3\xff\xd7;p\xff\xbf;\xfb\xfe\xe9;\x0f\xff\xe3;D\xff\xd1;\xfb\xfe\xe9;\xee\xfe\xed;\xd1\xfe\xf5;\xfb\xfe\xe9;\xee\xfe\xed;\xc2\xfe\xf9;8\xff\x06<*\xff\t<\x1c\xff\x0c<\xfe\xfe\x12<\xed\xfe\x15<\xf3\xfe\x14<\xf3\xfe\x14<\xe2\xfe\x17<\xa0\xfe"<\xb9\xfe\x1e<\xa6\xfe!<=\xfe0<\x05\xfe7<5\xfe1<\x92\xfe$<\x1d\xfe4<\xd2\xfd=<\x1d\xfe4<5\xfe1<\x1d\xfe4<\xf4\xfd9<\xc9\xfd><\xdb\xfd<<5\xfe1<\xec\xfd:<\xec\xfd:<\xd2\xfd=<\xc9\xfd><\xd2\xfd=<\xc0\xfd?<\r\xfe6<\x05\xfe7<\xae\xfdA<\xb7\xfd@<\xc0\xfd?<u\xfdG<\x7f\xfdF<k\xfdH<$\xfdO<W\xfdJ<a\xfdI<\x0e\xfdQ<\xcc\xfcW<\x0e\xfdQ<M\xfdK<\x04\xfdR<\x9e\xfc[<\xf9\xfcS<\xe3\xfcU<\xd7\xfcV<\xcc\xfcW<\x04\xfdR<W\xfdJ<\x0e\xfdQ<\xc1\xfcX<\xe3\xfcU<M\xfdK<\x04\xfdR<\x04\xfdR<\x04\xfdR<\xf9\xfcS<\xa9\xfcZ<\xd7\xfcV<\xf9\xfcS<\x19\xfdP<9\xfdM<k\xfdH<\xae\xfdA<M\xfdK<\xc0\xfd?<\xa5\xfdB<\x92\xfdD<\xf4\xfd9<\xc0\xfd?<\xae\xfdA<\xe3\xfd;<5\xfe1<\x1d\xfe4<\xd2\xfd=<5\xfe1<i\xfe*<\x88\xfdE<\x92\xfdD<S\xfe-<\x9b\xfdC<\x9b\xfdC<\xb7\xfd@<%\xfe3<~\xfe\'<\xec\xfd:<\xdb\xfd<<\xc0\xfd?<\xae\xfdA<\xe3\xfd;<S\xfe-<-\xfe2<\x92\xfdD<\x1d\xfe4<i\xfe*<S\xfe-<\xf4\xfd9<\xec\xfd:<\xf4\xfd9<b\xfe+<~\xfe\'<u\xfdG<\xf9\xfcS<\xa9\xfcZ<.\xfdN<\x92\xfc\\<\x92\xfc\\<\xe0\xfbj<\xe0\xfbj<\xd7\xfcV<"\xfce<\x86\xfc]</\xfcd<m\xfc_<\xcc\xfcW<\x19\xfdP<\xcc\xfcW<9\xfdM<\x7f\xfdF<\x19\xfdP<u\xfdG<\x9b\xfdC<\x05\xfe7<\xd2\xfd=<\xe3\xfd;<\xb7\xfd@<\xc0\xfd?<\xae\xfdA<9\xfdM<\x9b\xfdC<\xd2\xfd=<i\xfe*<\xdd\xfe\x18<\x99\xfe#<\x99\xfe#<\xe2\xfe\x17<\xfe\xfe\x12<\x1c\xff\x0c<\x12\xff\x0e<M\xff\x01<3\xff\xd7;\xf5\xfe\xeb;\xfb\xfe\xe9;}\xff\xb9;\x8d\xff\xb1;\x98\xff\xab;\xc6\xff\x8d;\xb2\xffw;\xcd\xffW;\xda\xffC;\xda\xffC;\xf3\xff\xd7:\xee\xff\xef:\xfe\xff/:\xff\xff\x0f\xba\x00\x00\x009\xff\xff\xdf\xb9\xe8\xff\'\xbb\xf3\xff\x07\xbb\xf7\xff\xbf\xba\xee\xff\x17\xbb\xe9\xff%\xbb\xb1\xffy\xbb\xd2\xff\x82\xbb\xba\xffo\xbb\xb8\xffq\xbb\xc1\xff\x91\xbb\xd1\xff\x83\xbb\xc8\xff\x8b\xbb\x8b\xff\xb2\xbb\xa0\xff\xa7\xbb\x9e\xff\xa8\xbb\xa5\xff\xa4\xbb\x8f\xff\xb0\xbb\xa0\xff\xa7\xbb\x81\xff\xb7\xbb\xab\xff\xa0\xbb\xbe\xff\x93\xbb\xa3\xff\xa5\xbb\xa0\xff\xa7\xbb\xd0\xff\x84\xbb\xef\xff\x15\xbb\xe3\xff1\xbb\xed\xff\x19\xbb\xe6\xff+\xbb\xed\xff\xf7\xba\xc2\xffe\xbbC\x7f\x04\xbc\x0e\xfdQ\xbc\xab\xfa\x7f\xbc\xf4{i\xbc\xfe|S\xbc\xdb\xfd<\xbc\x97}D\xbc.\xfdN\xbc\r\xfe6\xbci\xfe*\xbc\x8f~%\xbc\xa0\xfe"\xbc5\xfe1\xbck\xfdH\xbc\xa0}C\xbc\xf9}9\xbc\xc5}?\xbc\x05\xfe7\xbc\xf3|T\xbca\xfdI\xbc$\xfdO\xbc\xd2\xfbk\xbc\x83\xfc\x8b\xbc\xf2z{\xbc\xbb\xfc\x88\xbc\x90\xb5\xc9\xbc\xe8|\x86\xbc\x1a\xbb\x9c\xbc\xe3\xf4\xcd\xbc\xb5\xf8\xb2\xbcC:\xa5\xbc\xb2\xfa\xa0\xbc\xb3y\xaa\xbc\xc6;\x95\xbcm;\x99\xbcM\xbc\x8e\xbcQ=\x80\xbc\x01{z\xbc\xa9\xfbn\xbc\x88\xfdE\xbcb\xfe+\xbc\x12\xff\x0e\xbc;\xff\xd4\xbb\x98\xff\xab\xbb\xe5\xff-\xbb\x00\x00\x80\xb9\xf4\xff\x03;\xde\xff;;\xb3\xff\x9b;y\xff\xbb;}\xff\xb9;\x08\xff\xe5;\x08\xff\x10<\xd7\xfe\x19<3\xff\x07<\xdd\xfe\x18<=\xfe0<\x88\xfdE<\xc0\xfd?<\xe3\xfd;<u\xfdG<a\xfdI<\xe3\xfcU<\xcc\xfcW<\'\xfbw<\xf9\xfaz<\xb7\xfbm<6\xfbv<\xd2\xfbk<6\xfbv<"\xfce<<\xfcc<\x08\xfcg<\xf9\xfcS<\x04\xfdR<\xd2\xfd=<\xbf\xfe\x1d</\xff\x08<\xb9\xfe\x1e<\x08\xff\x10<D\xff\xd1;\xad\xff\x9f;\xca\xff[;\xd2\xffO;\xe8\xff\';\xfb\xff\x7f:\xea\xff#\xbb\xfd\xff\x87\xba\xff\xff\x0f:\xd0\xff\x84\xbb\xd2\xff\x82\xbb\xfa\xff\xa7\xba\xcd\xffW\xbb{\xff\xba\xbb\xc7\xff\x8c\xbb\xc7\xff_\xbb\xda\xffC\xbb\xae\xff\x9e\xbb\x8b\xff\xb2\xbb\x95\xff\xad\xbb]\xff\xc7\xbb\xa3\xff\xa5\xbb`\xff\xc6\xbbQ\xff\xcc\xbb\xae\xff\x9e\xbb\xad\xff}\xbb\xb1\xffy\xbb\xc0\xffg\xbb\x89\xff\xb3\xbb\xc2\xfe\xf9\xbb\xcd\xfe\xf6\xbb(\x7f\n\xbc\xc6\xfe\xf8\xbb\xea\xfe\xee\xbb1\x7f\x08\xbc[\xfe,\xbc!~4\xbc^~,\xbcz}G\xbc\xb0~ \xbc\xb9\xfe\x1e\xbc\xa3~"\xbcW~-\xbc9\xff\xd5\xbb\xea\xfe\xee\xbbS\x7f\x00\xbc\xfb\xff\x7f\xba\xa1\xff\xa6\xbb\x7f\xff\xb8\xbb\xa3~"\xbcI\xbd\x80\xbc\x10{y\xbc\x18\xfbx\xbc8\xff\x06\xbc\xb9\xfe\x1e\xbc\xe8|U\xbc\x04\xfdR\xbc\xf2z{\xbc(\xbd\x82\xbc\xc9<\x88\xbc\xf2z{\xbc\xd9{k\xbc\xcc\xfcW\xbc\x98|\\\xbc\xee\xfcT\xbc\xf4\xfd9\xbcU\xfca\xbc[|a\xbc\xc5\xfe\x1c\xbc\xe3\xfe\xf0\xbbC\x7f\x04\xbc\xbc~\x1e\xbc,\x7f\t\xbc%\xff\n\xbc<\xff\x05\xbc\xab\xfe\xff\xbbA\xff\xd2\xbb\xce\xffU\xbb\xd2\xffO\xbb\xdd\xff=\xbb\xfb\xff\x7f\xba', 'payload_msg': '<audio_data:35960bytes>'}
```

### ✅ 修复后
```
2025-07-26 20:37:40,958 - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': 'test-session-123', 'payload': {'results': [{'text': '今天天气怎么样？', 'is_interim': False, 'confidence': 0.98}], 'audio_metadata': {'sample_rate': 16000, 'channels': 1, 'raw_audio': '<binary_data:8000bytes>'}}, 'payload_msg': '<binary_data:16000bytes>'}
```

## 🎯 修复效果

1. **✅ 二进制数据清理** - 所有二进制数据被替换为大小信息
2. **✅ 嵌套结构支持** - 递归处理嵌套字典和列表中的二进制数据
3. **✅ 长字符串截断** - 超长字符串被适当截断，避免日志过长
4. **✅ 保持重要信息** - 文本内容、事件类型等重要信息完整保留
5. **✅ 性能优化** - 避免序列化大量二进制数据，提高日志性能

## 🚀 使用方法

修复后的AudioAgent会自动使用新的日志清理功能，无需额外配置。

如果需要查看详细的二进制数据（调试时），可以设置：
```bash
export AUDIO_AGENT_LOGGING_LEVEL=DEBUG
export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=true
```

## 📚 相关文件

- `src/audio_agent/utils/logging_utils.py` - 日志清理工具
- `src/audio_agent/core/session_manager.py` - 会话管理器日志优化
- `src/audio_agent/core/actors.py` - 音频处理器日志优化
- `examples/binary_logging_test.py` - 日志清理功能测试

## 🎉 总结

现在AudioAgent的日志输出清爽易读，不再有令人头疼的二进制数据污染！🎯

- **问题解决**: 彻底消除二进制数据在日志中的显示
- **保持功能**: 所有重要信息依然完整记录
- **性能提升**: 避免序列化大量二进制数据
- **开发友好**: 需要时可以启用详细调试日志
