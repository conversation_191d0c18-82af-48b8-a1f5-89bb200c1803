"""
Event handler for processing Volcengine events.

This implements the event-driven architecture following Volcengine's official practices.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime
import uuid

from .event_types import (
    EventType, BaseEvent, ServerEvent, ClientEvent, 
    DialogEvent, TTSEvent, ASREvent, LLMEvent
)

logger = logging.getLogger(__name__)


class EventHandler:
    """
    Event handler that processes events according to Volcengine's architecture.
    
    This class implements the event-driven flow shown in the official documentation:
    1. Receive server events
    2. Process events based on type
    3. Trigger appropriate handlers (ASR, LLM, TTS, etc.)
    4. Send client events back to server
    """
    
    def __init__(self, session_id: str, event_processor=None):
        """
        Initialize event handler.

        Args:
            session_id: Session ID for this conversation
            event_processor: Reference to parent EventProcessor for state access
        """
        self.session_id = session_id
        self.event_processor = event_processor  # Reference to parent processor for state access
        self.event_queue = asyncio.Queue()
        self.event_handlers: Dict[EventType, List[Callable]] = {}
        self.is_processing = False
        
        # Register default handlers
        self._register_default_handlers()
        
        logger.info(f"EventHandler initialized for session {session_id}")
    
    def _register_default_handlers(self):
        """Register default event handlers for Volcengine official events."""
        # Connection events
        self.register_handler(EventType.CONNECTION_STARTED, self._handle_connection_started)
        self.register_handler(EventType.CONNECTION_FAILED, self._handle_connection_failed)
        self.register_handler(EventType.CONNECTION_FINISHED, self._handle_connection_finished)

        # Session events
        self.register_handler(EventType.SESSION_STARTED, self._handle_session_started)
        self.register_handler(EventType.SESSION_FINISHED, self._handle_session_finished)
        self.register_handler(EventType.SESSION_FAILED, self._handle_session_failed)

        # TTS events
        self.register_handler(EventType.TTS_SENTENCE_START, self._handle_tts_sentence_start)
        self.register_handler(EventType.TTS_SENTENCE_END, self._handle_tts_sentence_end)
        self.register_handler(EventType.TTS_RESPONSE, self._handle_tts_response)
        self.register_handler(EventType.TTS_ENDED, self._handle_tts_ended)

        # ASR events
        self.register_handler(EventType.ASR_INFO, self._handle_asr_info)
        self.register_handler(EventType.ASR_RESPONSE, self._handle_asr_response)
        self.register_handler(EventType.ASR_ENDED, self._handle_asr_ended)

        # Chat events
        self.register_handler(EventType.CHAT_RESPONSE, self._handle_chat_response)
        self.register_handler(EventType.CHAT_ENDED, self._handle_chat_ended)

        # Custom LLM enhancement events
        self.register_handler(EventType.LLM_START, self._handle_llm_start)
        self.register_handler(EventType.LLM_TOOL_CALL, self._handle_llm_tool_call)
        self.register_handler(EventType.LLM_FINAL_RESULT, self._handle_llm_result)
    
    def register_handler(self, event_type: EventType, handler: Callable):
        """
        Register an event handler.
        
        Args:
            event_type: Type of event to handle
            handler: Handler function
        """
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        logger.debug(f"Registered handler for event type {event_type}")
    
    async def handle_server_event(self, message: Dict[str, Any]) -> Optional[BaseEvent]:
        """
        Handle incoming server event from WebSocket.
        
        Args:
            message: Raw message from WebSocket
            
        Returns:
            Processed event or None
        """
        try:
            # Convert WebSocket message to ServerEvent
            event = ServerEvent.from_websocket_message(message, self.session_id)

            logger.info(f"📨 Processing server event: {event.event_type} (message_type: {event.message_type})")
            logger.info(f"📨 Event data keys: {list(event.data.keys()) if event.data else 'None'}")

            # Add to processing queue
            await self.event_queue.put(event)
            
            return event
            
        except Exception as e:
            logger.error(f"Failed to handle server event: {e}")
            return None
    
    async def process_events(self):
        """Process events from the queue."""
        self.is_processing = True
        
        try:
            while self.is_processing:
                try:
                    # Get event from queue with timeout
                    event = await asyncio.wait_for(self.event_queue.get(), timeout=0.1)
                    
                    # Process the event
                    await self._process_single_event(event)
                    
                except asyncio.TimeoutError:
                    # No events to process, continue
                    continue
                except Exception as e:
                    logger.error(f"Error processing event: {e}")
                    
        except Exception as e:
            logger.error(f"Event processing loop error: {e}")
        finally:
            self.is_processing = False
    
    async def _process_single_event(self, event: BaseEvent):
        """Process a single event."""
        try:
            logger.debug(f"🔄 Processing event: {event.event_type} (value: {event.event_type.value})")

            # Get handlers for this event type
            handlers = self.event_handlers.get(event.event_type, [])

            if not handlers:
                logger.warning(f"No handlers registered for event type {event.event_type} (value: {event.event_type.value})")
                logger.debug(f"Available handlers: {list(self.event_handlers.keys())}")
                return

            logger.debug(f"Found {len(handlers)} handlers for event {event.event_type}")

            # Execute all handlers
            for handler in handlers:
                try:
                    logger.debug(f"Executing handler: {handler.__name__}")
                    await handler(event)
                except Exception as e:
                    logger.error(f"Handler error for {event.event_type}: {e}")

        except Exception as e:
            logger.error(f"Failed to process event {event.event_type}: {e}")
    
    def stop_processing(self):
        """Stop event processing."""
        self.is_processing = False
        logger.info("Event processing stopped")
    
    # Default event handlers for Volcengine official events
    async def _handle_connection_started(self, event: BaseEvent):
        """Handle connection started event (50)."""
        logger.info(f"Connection started: {event.session_id}")

    async def _handle_connection_failed(self, event: BaseEvent):
        """Handle connection failed event (51)."""
        error = event.data.get('error', 'Unknown error')
        logger.error(f"Connection failed: {error}")

    async def _handle_connection_finished(self, event: BaseEvent):
        """Handle connection finished event (52)."""
        logger.info(f"Connection finished: {event.session_id}")

    async def _handle_session_started(self, event: BaseEvent):
        """Handle session started event (150)."""
        dialog_id = event.data.get('dialog_id', 'unknown')
        logger.info(f"Session started with dialog_id: {dialog_id}")

    async def _handle_session_finished(self, event: BaseEvent):
        """Handle session finished event (152)."""
        logger.info(f"Session finished: {event.session_id}")

    async def _handle_session_failed(self, event: BaseEvent):
        """Handle session failed event (153)."""
        error = event.data.get('error', 'Unknown error')
        logger.error(f"Session failed: {error}")

    async def _handle_tts_sentence_start(self, event: BaseEvent):
        """Handle TTS sentence start event (350)."""
        logger.info(f"🎯 TTS_SENTENCE_START handler called! Event: {event.event_type}")

        # Extract TTS info from payload (not data)
        payload = event.payload if hasattr(event, 'payload') and event.payload else {}
        tts_type = payload.get('tts_type', 'unknown')
        text = payload.get('text', '')
        tts_task_id = payload.get('tts_task_id', '')

        logger.info(f"🎯 TTS info - type: {tts_type}, text: '{text}', task_id: {tts_task_id}")

        # 🎯 CRITICAL: Filter based on tts_type during tool calling
        logger.info(f"🔍 TTS Filter Debug - event_processor: {self.event_processor is not None}")
        if self.event_processor:
            logger.info(f"🔍 TTS Filter Debug - has tool_calling_active: {hasattr(self.event_processor, 'tool_calling_active')}")
            if hasattr(self.event_processor, 'tool_calling_active'):
                logger.info(f"🔍 TTS Filter Debug - tool_calling_active: {self.event_processor.tool_calling_active}")

        if self.event_processor and hasattr(self.event_processor, 'tool_calling_active'):
            if self.event_processor.tool_calling_active:
                if tts_type == 'default':
                    # This is server's automatic response - mark for filtering
                    self.event_processor.current_tts_should_skip = True
                    self.event_processor.server_auto_response_started = True
                    logger.warning(f"🚫 FILTERING server auto-response (tts_type=default): '{text}' - audio will be DISCARDED")
                elif tts_type == 'chat_tts_text':
                    # This is our tool result response - allow it
                    self.event_processor.current_tts_should_skip = False
                    self.event_processor.tool_response_tts_task_id = tts_task_id
                    logger.info(f"✅ ALLOWING tool result TTS (tts_type=chat_tts_text): '{text}' (task_id: {tts_task_id})")
                else:
                    # Unknown type during tool calling - be safe and skip
                    self.event_processor.current_tts_should_skip = True
                    logger.warning(f"🚫 FILTERING unknown TTS type during tool calling: '{tts_type}', text: '{text}'")
            else:
                # Not in tool calling mode - allow all
                self.event_processor.current_tts_should_skip = False
                logger.info(f"🔍 TTS Filter Debug - tool calling not active, allowing TTS")
        else:
            # No event processor or tool calling state - allow all
            if self.event_processor and hasattr(self.event_processor, 'current_tts_should_skip'):
                self.event_processor.current_tts_should_skip = False
            logger.info(f"🔍 TTS Filter Debug - no event processor or tool calling state, allowing TTS")

        logger.info(f"TTS sentence start - type: {tts_type}, text: '{text}'")

    async def _handle_tts_sentence_end(self, event: BaseEvent):
        """Handle TTS sentence end event (351)."""
        logger.info(f"TTS sentence ended")

    async def _handle_tts_response(self, event: BaseEvent):
        """Handle TTS response event (352) - contains audio data."""
        if isinstance(event, ServerEvent) and event.payload:
            logger.info(f"TTS audio response: {len(event.payload) if isinstance(event.payload, bytes) else 'unknown'} bytes")

    async def _handle_tts_ended(self, event: BaseEvent):
        """Handle TTS ended event (359)."""
        logger.info(f"TTS synthesis ended")

        # Reset tool calling state when TTS synthesis is complete
        if self.event_processor and hasattr(self.event_processor, 'tool_calling_active'):
            if self.event_processor.tool_calling_active:
                logger.info("🔧 TTS synthesis ended, resetting tool calling state")
                self.event_processor.tool_calling_active = False
                self.event_processor.server_auto_response_started = False
                self.event_processor.current_tts_should_skip = False
                if hasattr(self.event_processor, 'tool_response_tts_task_id'):
                    delattr(self.event_processor, 'tool_response_tts_task_id')

        # Reset LLM response sent flag when TTS synthesis is complete
        if self.event_processor and hasattr(self.event_processor, 'llm_response_sent'):
            if self.event_processor.llm_response_sent:
                logger.info("🔧 TTS synthesis ended, resetting LLM response sent flag")
                self.event_processor.llm_response_sent = False

    async def _handle_asr_info(self, event: BaseEvent):
        """Handle ASR info event (450) - first word detected."""
        logger.info(f"ASR first word detected - can interrupt playback")

    async def _handle_asr_response(self, event: BaseEvent):
        """Handle ASR response event (451) - speech recognition result."""
        results = event.data.get('results', [])
        for result in results:
            text = result.get('text', '')
            is_interim = result.get('is_interim', False)
            logger.info(f"ASR result: '{text}' (interim: {is_interim})")

    async def _handle_asr_ended(self, event: BaseEvent):
        """Handle ASR ended event (459) - user finished speaking."""
        logger.info(f"ASR ended - user finished speaking")

    async def _handle_chat_response(self, event: BaseEvent):
        """Handle chat response event (550) - model text response."""
        content = event.data.get('content', '')
        logger.info(f"Chat response: '{content}'")

    async def _handle_chat_ended(self, event: BaseEvent):
        """Handle chat ended event (559) - model finished responding."""
        logger.info(f"Chat response ended")

    # Custom LLM enhancement event handlers
    async def _handle_llm_start(self, event: BaseEvent):
        """Handle LLM processing start."""
        logger.info(f"LLM processing started: {event.session_id}")

    async def _handle_llm_result(self, event: BaseEvent):
        """Handle LLM result."""
        logger.info(f"LLM result: {event.data}")

    async def _handle_llm_tool_call(self, event: BaseEvent):
        """Handle LLM tool call."""
        logger.info(f"LLM tool call: {event.data}")
