"""
Calculator tool for the audio agent.

This tool provides mathematical calculation capabilities.
"""

import ast
import math
import operator
import logging
from typing import Any, Dict, Union
from ..base import BaseTool, ToolResult, ToolError, ToolParameter, ParameterType

logger = logging.getLogger(__name__)


class CalculatorTool(BaseTool):
    """
    Calculator tool that can perform mathematical calculations.
    
    This tool safely evaluates mathematical expressions and provides
    calculation results.
    """
    
    # Safe operators for evaluation
    SAFE_OPERATORS = {
        ast.Add: operator.add,
        ast.Sub: operator.sub,
        ast.Mult: operator.mul,
        ast.Div: operator.truediv,
        ast.Pow: operator.pow,
        ast.Mod: operator.mod,
        ast.USub: operator.neg,
        ast.UAdd: operator.pos,
    }
    
    # Safe functions
    SAFE_FUNCTIONS = {
        'abs': abs,
        'round': round,
        'min': min,
        'max': max,
        'sum': sum,
        'sqrt': math.sqrt,
        'sin': math.sin,
        'cos': math.cos,
        'tan': math.tan,
        'log': math.log,
        'log10': math.log10,
        'exp': math.exp,
        'pi': math.pi,
        'e': math.e,
    }
    
    def __init__(self):
        """Initialize the calculator tool."""
        super().__init__(
            name="calculator",
            description="Perform mathematical calculations. Can handle basic arithmetic, trigonometry, logarithms, and other mathematical functions."
        )
        
        # Add parameters
        self.add_parameter(ToolParameter(
            name="expression",
            type=ParameterType.STRING,
            description="Mathematical expression to calculate (e.g., '2 + 3 * 4', 'sqrt(16)', 'sin(pi/2)')",
            required=True
        ))
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute the calculation.
        
        Args:
            expression: Mathematical expression to evaluate
            
        Returns:
            ToolResult with calculation result
        """
        try:
            # Validate parameters
            validated_params = self.validate_parameters(**kwargs)
            expression = validated_params["expression"].strip()
            
            logger.info(f"Calculating: {expression}")
            
            # Evaluate the expression safely
            result = self._safe_eval(expression)
            
            return ToolResult(
                success=True,
                data={
                    "expression": expression,
                    "result": result,
                    "formatted_result": self._format_result(result)
                }
            )
            
        except Exception as e:
            logger.error(f"Calculation error: {e}")
            return ToolResult(
                success=False,
                error=f"Calculation failed: {str(e)}"
            )
    
    def _safe_eval(self, expression: str) -> Union[int, float]:
        """
        Safely evaluate a mathematical expression.
        
        Args:
            expression: Expression to evaluate
            
        Returns:
            Calculation result
            
        Raises:
            ToolError: If expression is invalid or unsafe
        """
        try:
            # Parse the expression
            node = ast.parse(expression, mode='eval')
            
            # Evaluate the AST safely
            result = self._eval_node(node.body)
            
            return result
            
        except SyntaxError:
            raise ToolError(
                f"Invalid mathematical expression: {expression}",
                tool_name=self.name,
                error_code="SYNTAX_ERROR"
            )
        except Exception as e:
            raise ToolError(
                f"Error evaluating expression: {str(e)}",
                tool_name=self.name,
                error_code="EVALUATION_ERROR"
            )
    
    def _eval_node(self, node: ast.AST) -> Union[int, float]:
        """
        Recursively evaluate an AST node.
        
        Args:
            node: AST node to evaluate
            
        Returns:
            Evaluation result
        """
        if isinstance(node, ast.Constant):  # Python 3.8+
            return node.value
        elif hasattr(ast, 'Num') and isinstance(node, ast.Num):  # Python < 3.8 compatibility
            return node.n
        elif isinstance(node, ast.BinOp):
            left = self._eval_node(node.left)
            right = self._eval_node(node.right)
            op = self.SAFE_OPERATORS.get(type(node.op))
            if op is None:
                raise ToolError(
                    f"Unsupported operator: {type(node.op).__name__}",
                    tool_name=self.name,
                    error_code="UNSUPPORTED_OPERATOR"
                )
            return op(left, right)
        elif isinstance(node, ast.UnaryOp):
            operand = self._eval_node(node.operand)
            op = self.SAFE_OPERATORS.get(type(node.op))
            if op is None:
                raise ToolError(
                    f"Unsupported unary operator: {type(node.op).__name__}",
                    tool_name=self.name,
                    error_code="UNSUPPORTED_OPERATOR"
                )
            return op(operand)
        elif isinstance(node, ast.Call):
            func_name = node.func.id if isinstance(node.func, ast.Name) else None
            if func_name not in self.SAFE_FUNCTIONS:
                raise ToolError(
                    f"Unsupported function: {func_name}",
                    tool_name=self.name,
                    error_code="UNSUPPORTED_FUNCTION"
                )
            
            args = [self._eval_node(arg) for arg in node.args]
            func = self.SAFE_FUNCTIONS[func_name]
            
            # Handle special cases
            if func_name in ['pi', 'e']:
                return func
            else:
                return func(*args)
        elif isinstance(node, ast.Name):
            # Handle constants
            if node.id in self.SAFE_FUNCTIONS:
                return self.SAFE_FUNCTIONS[node.id]
            else:
                raise ToolError(
                    f"Undefined variable: {node.id}",
                    tool_name=self.name,
                    error_code="UNDEFINED_VARIABLE"
                )
        else:
            raise ToolError(
                f"Unsupported expression type: {type(node).__name__}",
                tool_name=self.name,
                error_code="UNSUPPORTED_EXPRESSION"
            )
    
    def _format_result(self, result: Union[int, float]) -> str:
        """
        Format the calculation result for display.
        
        Args:
            result: Calculation result
            
        Returns:
            Formatted result string
        """
        if isinstance(result, float):
            # Round to reasonable precision
            if result.is_integer():
                return str(int(result))
            else:
                return f"{result:.10g}"  # Remove trailing zeros
        else:
            return str(result)
