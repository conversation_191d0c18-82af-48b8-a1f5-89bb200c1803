"""
Main entry point for the audio agent.

This module provides the main function to start the real-time audio dialog system.
"""

import asyncio
import logging
import signal
import sys
from typing import Optional

from .config import get_config
from .core.session_manager import SessionManager


# Configure logging based on config
def setup_logging():
    """Setup logging based on configuration."""
    config = get_config()

    # Create handlers list
    handlers = [logging.StreamHandler()]

    # Add file handler if enabled
    if config.logging.enable_file_logging:
        import os
        # Create logs directory if it doesn't exist
        log_dir = os.path.dirname(config.logging.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        file_handler = logging.FileHandler(config.logging.log_file, encoding='utf-8')
        handlers.append(file_handler)

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, config.logging.level.upper()),
        format=config.logging.format,
        handlers=handlers,
        force=True  # Override any existing configuration
    )

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


class AudioAgent:
    """Main audio agent application."""
    
    def __init__(self):
        """Initialize the audio agent."""
        self.config = get_config()
        self.session_manager: Optional[SessionManager] = None
        self.running = False
    
    async def start(self) -> None:
        """Start the audio agent."""
        logger.info("Starting Audio Agent...")
        
        try:
            # Initialize session manager
            self.session_manager = SessionManager(self.config)
            
            # Start the session
            await self.session_manager.start()
            self.running = True
            
            logger.info("Audio Agent started successfully")
            
            # Keep running until stopped
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"Error starting Audio Agent: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the audio agent."""
        logger.info("Stopping Audio Agent...")
        self.running = False
        
        if self.session_manager:
            await self.session_manager.stop()
        
        logger.info("Audio Agent stopped")
    
    def handle_signal(self, signum, frame):
        """Handle system signals."""
        logger.info(f"Received signal {signum}, shutting down...")
        asyncio.create_task(self.stop())


async def main() -> None:
    """Main entry point."""
    agent = AudioAgent()
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, agent.handle_signal)
    signal.signal(signal.SIGTERM, agent.handle_signal)
    
    try:
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        await agent.stop()


if __name__ == "__main__":
    asyncio.run(main())
