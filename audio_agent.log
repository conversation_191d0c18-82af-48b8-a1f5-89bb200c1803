2025-07-27 17:20:36,429 - __main__ - INFO - Starting Audio Agent...
2025-07-27 17:20:36,429 - audio_agent.core.session_manager - INFO - Session<PERSON>anager initialized with session ID: 6038ffe6-c4ed-4cc2-8f1c-57896df9fb11
2025-07-27 17:20:36,430 - audio_agent.core.session_manager - INFO - Starting SessionManager...
2025-07-27 17:20:37,440 - audio_agent.core.session_manager - INFO - Ray initialized
2025-07-27 17:20:37,531 - audio_agent.core.session_manager - INFO - Intelligent agent actor created
2025-07-27 17:20:37,532 - audio_agent.core.session_manager - INFO - Ray actors created
2025-07-27 17:20:37,532 - audio_agent.events.event_handler - INFO - <PERSON><PERSON><PERSON><PERSON> initialized for session 6038ffe6-c4ed-4cc2-8f1c-57896df9fb11
2025-07-27 17:20:37,532 - audio_agent.tools.registry - INFO - Registered tool: calculator
2025-07-27 17:20:37,532 - audio_agent.events.llm_enhancer - INFO - Registered calculator tool
2025-07-27 17:20:37,533 - audio_agent.tools.registry - INFO - Registered tool: datetime
2025-07-27 17:20:37,533 - audio_agent.events.llm_enhancer - INFO - Registered datetime tool
2025-07-27 17:20:37,533 - audio_agent.events.llm_enhancer - INFO - LLMEnhancer initialized (OpenAI: lazy loading)
2025-07-27 17:20:37,533 - audio_agent.events.event_processor - INFO - EventProcessor initialized for session 6038ffe6-c4ed-4cc2-8f1c-57896df9fb11
2025-07-27 17:20:37,533 - audio_agent.core.session_manager - INFO - Event processor initialized
2025-07-27 17:20:41,107 - audio_agent.events.event_processor - INFO - Starting event processing
2025-07-27 17:20:41,107 - audio_agent.events.event_processor - INFO - Event processing task started
2025-07-27 17:20:41,108 - audio_agent.core.session_manager - INFO - Event processor started
2025-07-27 17:20:41,108 - audio_agent.core.session_manager - INFO - SessionManager started successfully with event system
2025-07-27 17:20:41,108 - __main__ - INFO - Audio Agent started successfully
2025-07-27 17:20:41,108 - audio_agent.core.session_manager - INFO - Starting processing loop
2025-07-27 17:20:41,430 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'enable_v3_loudness_balance': True, 'gta': True, 'model_type': 'v3', 'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":40,"flushed":false,"markdown":{"prev_parsed_text":"你好，我是小凯，有什么可以帮助您的？","...<truncated:532chars>', 'text': '你好，我是小凯，有什么可以帮助您的？', 'tts_task_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_1_0', 'tts_type': 'chat_tts_text', 'v3_loundness_params': ''}}
2025-07-27 17:20:41,430 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:41,431 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:41,431 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 17:20:41,431 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: chat_tts_text, text: '你好，我是小凯，有什么可以帮助您的？', task_id: 6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_1_0
2025-07-27 17:20:41,431 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-27 17:20:41,431 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 17:20:41,431 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 17:20:41,431 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 17:20:41,432 - audio_agent.events.event_handler - INFO - TTS sentence start - type: chat_tts_text, text: '你好，我是小凯，有什么可以帮助您的？'
2025-07-27 17:20:42,087 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 17:20:42,087 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:42,088 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:42,088 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 17:20:42,088 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 17:20:42,090 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-27 17:20:42,105 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 17:20:42,182 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38716bytes>', 'payload_msg': '<binary_data:38716bytes>'}
2025-07-27 17:20:42,182 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:42,182 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:42,183 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38716 bytes
2025-07-27 17:20:42,183 - audio_agent.events.event_handler - INFO - TTS audio response: 38716 bytes
2025-07-27 17:20:42,184 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38716 bytes)
2025-07-27 17:20:42,236 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38172bytes>', 'payload_msg': '<binary_data:38172bytes>'}
2025-07-27 17:20:42,236 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:42,237 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:42,237 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38172 bytes
2025-07-27 17:20:42,237 - audio_agent.events.event_handler - INFO - TTS audio response: 38172 bytes
2025-07-27 17:20:42,238 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38172 bytes)
2025-07-27 17:20:42,341 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38424bytes>', 'payload_msg': '<binary_data:38424bytes>'}
2025-07-27 17:20:42,341 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:42,341 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:42,341 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38424 bytes
2025-07-27 17:20:42,341 - audio_agent.events.event_handler - INFO - TTS audio response: 38424 bytes
2025-07-27 17:20:42,342 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38424 bytes)
2025-07-27 17:20:42,479 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38488bytes>', 'payload_msg': '<binary_data:38488bytes>'}
2025-07-27 17:20:42,479 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:42,479 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:42,480 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38488 bytes
2025-07-27 17:20:42,480 - audio_agent.events.event_handler - INFO - TTS audio response: 38488 bytes
2025-07-27 17:20:42,481 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38488 bytes)
2025-07-27 17:20:42,564 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38184bytes>', 'payload_msg': '<binary_data:38184bytes>'}
2025-07-27 17:20:42,564 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:42,564 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:42,564 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38184 bytes
2025-07-27 17:20:42,564 - audio_agent.events.event_handler - INFO - TTS audio response: 38184 bytes
2025-07-27 17:20:42,567 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38184 bytes)
2025-07-27 17:20:42,692 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38972bytes>', 'payload_msg': '<binary_data:38972bytes>'}
2025-07-27 17:20:42,692 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:42,692 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:42,693 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38972 bytes
2025-07-27 17:20:42,693 - audio_agent.events.event_handler - INFO - TTS audio response: 38972 bytes
2025-07-27 17:20:42,694 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38972 bytes)
2025-07-27 17:20:42,712 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:21596bytes>', 'payload_msg': '<binary_data:21596bytes>'}
2025-07-27 17:20:42,712 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:42,712 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:42,713 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 21596 bytes
2025-07-27 17:20:42,713 - audio_agent.events.event_handler - INFO - TTS audio response: 21596 bytes
2025-07-27 17:20:42,714 - audio_agent.core.session_manager - INFO - Audio response queued for playback (21596 bytes)
2025-07-27 17:20:42,730 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"unknown","emotionTag":"","eosProsody":40,"flushed":false,"markdown":{"prev_parsed_text":"你好，我是小凯，有什么可以帮助您的？","...<truncated:532chars>', 'speech_alignment_result': '', 'text': '你好，我是小凯，有什么可以帮助您的？'}}
2025-07-27 17:20:42,731 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:42,731 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:42,731 - audio_agent.events.event_handler - INFO - TTS sentence ended
2025-07-27 17:20:42,736 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'no_content': False}}
2025-07-27 17:20:42,737 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:42,737 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:42,737 - audio_agent.events.event_handler - INFO - TTS synthesis ended
2025-07-27 17:20:44,796 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 17:20:47,080 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 450, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'asr_task_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_1_0', 'question_id': '13846671795456258', 'round_id': 2}}
2025-07-27 17:20:47,080 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 450 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,081 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,081 - audio_agent.events.event_handler - INFO - ASR first word detected - can interrupt playback
2025-07-27 17:20:47,085 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8563021421432495, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.722002, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你'}], 'end_time': 6.722002, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你'}]}}
2025-07-27 17:20:47,085 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,085 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,085 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,085 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,085 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,087 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你', interim: True
2025-07-27 17:20:47,087 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你'
2025-07-27 17:20:47,092 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8563021421432495, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.812004, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你'}], 'end_time': 6.812004, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你'}]}}
2025-07-27 17:20:47,092 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,093 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,093 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,093 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,093 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,093 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你', interim: True
2025-07-27 17:20:47,093 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你'
2025-07-27 17:20:47,153 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8563021421432495, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.882006, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你'}], 'end_time': 6.882006, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你'}]}}
2025-07-27 17:20:47,154 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,154 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,154 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,155 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,155 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,155 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你', interim: True
2025-07-27 17:20:47,155 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你'
2025-07-27 17:20:47,204 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9178966879844666, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.972008, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好'}], 'end_time': 6.972008, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好'}]}}
2025-07-27 17:20:47,204 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,204 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,204 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,205 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,205 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,205 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好', interim: True
2025-07-27 17:20:47,205 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好'
2025-07-27 17:20:47,251 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9178966879844666, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 7.042009, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好'}], 'end_time': 7.042009, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好'}]}}
2025-07-27 17:20:47,251 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,251 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,251 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,251 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,251 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,251 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好', interim: True
2025-07-27 17:20:47,251 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好'
2025-07-27 17:20:47,328 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9178966879844666, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 7.132011, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好'}], 'end_time': 7.132011, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好'}]}}
2025-07-27 17:20:47,328 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,328 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,328 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,328 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,329 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,329 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好', interim: True
2025-07-27 17:20:47,329 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好'
2025-07-27 17:20:47,405 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9222405552864075, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 7.202013, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小'}], 'end_time': 7.202013, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小'}]}}
2025-07-27 17:20:47,405 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,406 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,406 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,406 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,406 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,406 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小', interim: True
2025-07-27 17:20:47,406 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小'
2025-07-27 17:20:47,452 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 19.998075683593797}, 'results': [{'alternatives': [{'end_time': 7.292015, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 7.292015, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:47,452 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,452 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,452 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,452 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,452 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,452 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:47,453 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:47,530 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'prefetch': True, 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 90.00015469360356}, 'results': [{'alternatives': [{'end_time': 7.362017, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好，小凯。'}], 'end_time': 7.362017, 'index': 0, 'is_interim': True, 'is_soft_finished': True, 'is_vad_timeout': False, 'soft_vad_type': 999, 'start_time': 6.142, 'text': '你好，小凯。'}]}}
2025-07-27 17:20:47,530 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,530 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,530 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,530 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,530 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,531 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好，小凯。', interim: True
2025-07-27 17:20:47,531 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好，小凯。'
2025-07-27 17:20:47,607 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 180.002214630127}, 'results': [{'alternatives': [{'end_time': 7.452019, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 7.452019, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:47,607 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,608 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,608 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,608 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,608 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,608 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:47,608 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:47,611 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 239.9988631286618}, 'results': [{'alternatives': [{'end_time': 7.52202, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 7.52202, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:47,611 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,611 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,611 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,611 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,611 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,611 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:47,612 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:47,685 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 330.00092306518525}, 'results': [{'alternatives': [{'end_time': 7.612022, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 7.612022, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:47,685 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,685 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,685 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,685 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,686 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,686 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:47,686 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:47,746 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 400.003002075195}, 'results': [{'alternatives': [{'end_time': 7.682024, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 7.682024, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:47,746 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,746 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,746 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,747 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,747 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,747 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:47,747 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:47,825 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 480.001062011719}, 'results': [{'alternatives': [{'end_time': 7.772026, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 7.772026, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:47,825 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,825 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,825 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,826 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,826 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,826 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:47,826 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:47,901 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 540.0001410217285}, 'results': [{'alternatives': [{'end_time': 7.842028, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 7.842028, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:47,901 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,901 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,901 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,901 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,901 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,901 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:47,902 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:47,977 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 630.0022009582519}, 'results': [{'alternatives': [{'end_time': 7.93203, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 7.93203, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:47,978 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:47,978 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:47,978 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:47,978 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:47,978 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:47,978 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:47,978 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:48,041 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9485213160514832, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 700.0033262939453}, 'results': [{'alternatives': [{'end_time': 8.002031, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好小凯'}], 'end_time': 8.002031, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好小凯'}]}}
2025-07-27 17:20:48,041 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,041 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,041 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:48,041 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:48,041 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:48,041 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好小凯', interim: True
2025-07-27 17:20:48,042 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '你好小凯'
2025-07-27 17:20:48,194 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.2972377836704254, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '你好小凯', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '你好，小凯。', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 790.0053862304687}, 'results': [{'alternatives': [{'end_time': 8.092033, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 6.142, 'text': '你好，小凯。'}], 'end_time': 8.092033, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 6.142, 'text': '你好，小凯。'}]}}
2025-07-27 17:20:48,194 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,194 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,194 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:48,194 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:48,194 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:48,194 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '你好，小凯。', interim: False
2025-07-27 17:20:48,194 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '你好，小凯。'
2025-07-27 17:20:48,497 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'comfort_wait_time': 0, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 7302}}
2025-07-27 17:20:48,497 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,497 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,497 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-27 17:20:48,497 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-27 17:20:48,497 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '你好，小凯。'
2025-07-27 17:20:48,497 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '你好，小凯。'
2025-07-27 17:20:48,500 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
2025-07-27 17:20:48,769 - audio_agent.events.llm_enhancer - WARNING - OPENAI_API_KEY not found, using rule-based fallback
2025-07-27 17:20:48,769 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.7, 'requires_tools': False, 'tools': []}
2025-07-27 17:20:48,769 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.7, 'requires_tools': False, 'tools': []}
2025-07-27 17:20:48,772 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_2_1', 'tts_type': 'default', 'v3_loundness_params': ''}}
2025-07-27 17:20:48,772 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,772 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,799 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 17:20:48,799 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:48,799 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:48,799 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 17:20:48,802 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-27 17:20:48,815 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 17:20:48,818 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': ''}}
2025-07-27 17:20:48,818 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,819 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,834 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '今天'}}
2025-07-27 17:20:48,834 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,834 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,849 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '过得'}}
2025-07-27 17:20:48,849 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,849 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,868 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '怎么样'}}
2025-07-27 17:20:48,868 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,869 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,887 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '啊'}}
2025-07-27 17:20:48,887 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,887 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,908 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '？'}}
2025-07-27 17:20:48,909 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,909 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:48,925 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38764bytes>', 'payload_msg': '<binary_data:38764bytes>'}
2025-07-27 17:20:48,925 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:48,925 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:48,926 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38764 bytes
2025-07-27 17:20:48,927 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38764 bytes)
2025-07-27 17:20:48,943 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38844bytes>', 'payload_msg': '<binary_data:38844bytes>'}
2025-07-27 17:20:48,943 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:48,943 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:48,943 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38844 bytes
2025-07-27 17:20:48,944 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38844 bytes)
2025-07-27 17:20:48,961 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:11660bytes>', 'payload_msg': '<binary_data:11660bytes>'}
2025-07-27 17:20:48,961 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:48,961 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:48,961 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 11660 bytes
2025-07-27 17:20:48,962 - audio_agent.core.session_manager - INFO - Audio response queued for playback (11660 bytes)
2025-07-27 17:20:48,979 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {}}
2025-07-27 17:20:48,979 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:48,979 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:49,066 - audio_agent.events.event_handler - INFO - LLM result: {'user_input': '你好，小凯。', 'ai_response': "我理解您说的是'你好，小凯。'。我可以为您做些什么吗？"}
2025-07-27 17:20:49,066 - audio_agent.events.event_processor - INFO - LLM response ready for TTS: 我理解您说的是'你好，小凯。'。我可以为您做些什么吗？
2025-07-27 17:20:49,070 - audio_agent.events.event_processor - INFO - ✅ LLM enhanced response sent to server for TTS: '我理解您说的是'你好，小凯。'。我可以为您做些什么吗？...'
2025-07-27 17:20:49,070 - audio_agent.events.event_processor - INFO - LLM enhanced response sent to user successfully
2025-07-27 17:20:49,070 - audio_agent.events.event_processor - INFO - Response generated: '我理解您说的是'你好，小凯。'。我可以为您做些什么吗？'
2025-07-27 17:20:49,071 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 17:20:49,071 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: 6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_2_1
2025-07-27 17:20:49,072 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-27 17:20:49,072 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 17:20:49,072 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 17:20:49,072 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 17:20:49,072 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-27 17:20:49,072 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 17:20:49,072 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:49,073 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:49,073 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:49,073 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:49,073 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:49,073 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:49,073 - audio_agent.events.event_handler - INFO - TTS audio response: 38764 bytes
2025-07-27 17:20:49,073 - audio_agent.events.event_handler - INFO - TTS audio response: 38844 bytes
2025-07-27 17:20:49,074 - audio_agent.events.event_handler - INFO - TTS audio response: 11660 bytes
2025-07-27 17:20:49,074 - audio_agent.events.event_handler - INFO - TTS sentence ended
2025-07-27 17:20:49,095 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'enable_v3_loudness_balance': True, 'gta': True, 'model_type': 'v3', 'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"Chinese","emotionTag":"","eosProsody":40,"flushed":false,"markdown":{"prev_parsed_text":"我理解您说的是\'你好，小凯。\'。我可以为您...<truncated:674chars>', 'text': "我理解您说的是'你好，小凯。'。我可以为您做些什么吗？", 'tts_task_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_3_0', 'tts_type': 'chat_tts_text', 'v3_loundness_params': ''}}
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: chat_tts_text, text: '我理解您说的是'你好，小凯。'。我可以为您做些什么吗？', task_id: 6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_3_0
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 17:20:49,096 - audio_agent.events.event_handler - INFO - TTS sentence start - type: chat_tts_text, text: '我理解您说的是'你好，小凯。'。我可以为您做些什么吗？'
2025-07-27 17:20:49,641 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 17:20:49,642 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:49,642 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:49,642 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 17:20:49,642 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (36000 bytes) - LLM mock response already sent
2025-07-27 17:20:49,642 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 17:20:49,712 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38428bytes>', 'payload_msg': '<binary_data:38428bytes>'}
2025-07-27 17:20:49,712 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:49,712 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:49,712 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38428 bytes
2025-07-27 17:20:49,712 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38428 bytes) - LLM mock response already sent
2025-07-27 17:20:49,712 - audio_agent.events.event_handler - INFO - TTS audio response: 38428 bytes
2025-07-27 17:20:49,851 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38844bytes>', 'payload_msg': '<binary_data:38844bytes>'}
2025-07-27 17:20:49,851 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:49,851 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:49,851 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38844 bytes
2025-07-27 17:20:49,851 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38844 bytes) - LLM mock response already sent
2025-07-27 17:20:49,851 - audio_agent.events.event_handler - INFO - TTS audio response: 38844 bytes
2025-07-27 17:20:49,928 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 17:20:50,011 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38412bytes>', 'payload_msg': '<binary_data:38412bytes>'}
2025-07-27 17:20:50,012 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:50,012 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:50,012 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38412 bytes
2025-07-27 17:20:50,012 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38412 bytes) - LLM mock response already sent
2025-07-27 17:20:50,012 - audio_agent.events.event_handler - INFO - TTS audio response: 38412 bytes
2025-07-27 17:20:50,129 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38796bytes>', 'payload_msg': '<binary_data:38796bytes>'}
2025-07-27 17:20:50,129 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:50,129 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:50,129 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38796 bytes
2025-07-27 17:20:50,130 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38796 bytes) - LLM mock response already sent
2025-07-27 17:20:50,130 - audio_agent.events.event_handler - INFO - TTS audio response: 38796 bytes
2025-07-27 17:20:50,213 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38104bytes>', 'payload_msg': '<binary_data:38104bytes>'}
2025-07-27 17:20:50,214 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:50,214 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:50,214 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38104 bytes
2025-07-27 17:20:50,214 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38104 bytes) - LLM mock response already sent
2025-07-27 17:20:50,214 - audio_agent.events.event_handler - INFO - TTS audio response: 38104 bytes
2025-07-27 17:20:50,348 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38392bytes>', 'payload_msg': '<binary_data:38392bytes>'}
2025-07-27 17:20:50,348 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:50,348 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:50,348 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38392 bytes
2025-07-27 17:20:50,348 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38392 bytes) - LLM mock response already sent
2025-07-27 17:20:50,348 - audio_agent.events.event_handler - INFO - TTS audio response: 38392 bytes
2025-07-27 17:20:50,502 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38300bytes>', 'payload_msg': '<binary_data:38300bytes>'}
2025-07-27 17:20:50,503 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:50,503 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:50,503 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38300 bytes
2025-07-27 17:20:50,503 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38300 bytes) - LLM mock response already sent
2025-07-27 17:20:50,503 - audio_agent.events.event_handler - INFO - TTS audio response: 38300 bytes
2025-07-27 17:20:50,676 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38504bytes>', 'payload_msg': '<binary_data:38504bytes>'}
2025-07-27 17:20:50,676 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:50,676 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:50,676 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38504 bytes
2025-07-27 17:20:50,676 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38504 bytes) - LLM mock response already sent
2025-07-27 17:20:50,676 - audio_agent.events.event_handler - INFO - TTS audio response: 38504 bytes
2025-07-27 17:20:50,783 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38636bytes>', 'payload_msg': '<binary_data:38636bytes>'}
2025-07-27 17:20:50,783 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:50,783 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:50,783 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38636 bytes
2025-07-27 17:20:50,783 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38636 bytes) - LLM mock response already sent
2025-07-27 17:20:50,783 - audio_agent.events.event_handler - INFO - TTS audio response: 38636 bytes
2025-07-27 17:20:50,951 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38508bytes>', 'payload_msg': '<binary_data:38508bytes>'}
2025-07-27 17:20:50,951 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:50,951 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:50,951 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38508 bytes
2025-07-27 17:20:50,951 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38508 bytes) - LLM mock response already sent
2025-07-27 17:20:50,951 - audio_agent.events.event_handler - INFO - TTS audio response: 38508 bytes
2025-07-27 17:20:51,026 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38732bytes>', 'payload_msg': '<binary_data:38732bytes>'}
2025-07-27 17:20:51,026 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:51,027 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:51,027 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38732 bytes
2025-07-27 17:20:51,027 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38732 bytes) - LLM mock response already sent
2025-07-27 17:20:51,027 - audio_agent.events.event_handler - INFO - TTS audio response: 38732 bytes
2025-07-27 17:20:51,165 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38792bytes>', 'payload_msg': '<binary_data:38792bytes>'}
2025-07-27 17:20:51,165 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:51,165 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:51,165 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38792 bytes
2025-07-27 17:20:51,165 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38792 bytes) - LLM mock response already sent
2025-07-27 17:20:51,166 - audio_agent.events.event_handler - INFO - TTS audio response: 38792 bytes
2025-07-27 17:20:51,271 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38140bytes>', 'payload_msg': '<binary_data:38140bytes>'}
2025-07-27 17:20:51,272 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:51,272 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:51,272 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38140 bytes
2025-07-27 17:20:51,272 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38140 bytes) - LLM mock response already sent
2025-07-27 17:20:51,272 - audio_agent.events.event_handler - INFO - TTS audio response: 38140 bytes
2025-07-27 17:20:51,348 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38408bytes>', 'payload_msg': '<binary_data:38408bytes>'}
2025-07-27 17:20:51,349 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:51,349 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:51,349 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38408 bytes
2025-07-27 17:20:51,349 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38408 bytes) - LLM mock response already sent
2025-07-27 17:20:51,349 - audio_agent.events.event_handler - INFO - TTS audio response: 38408 bytes
2025-07-27 17:20:51,353 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"Chinese","emotionTag":"","eosProsody":40,"flushed":false,"markdown":{"prev_parsed_text":"我理解您说的是\'你好，小凯。\'。我可以为您...<truncated:674chars>', 'speech_alignment_result': '', 'text': "我理解您说的是'你好，小凯。'。我可以为您做些什么吗？"}}
2025-07-27 17:20:51,353 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:51,353 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:51,353 - audio_agent.events.event_handler - INFO - TTS sentence ended
2025-07-27 17:20:51,357 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'no_content': False}}
2025-07-27 17:20:51,357 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:51,358 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:51,358 - audio_agent.events.event_handler - INFO - TTS synthesis ended
2025-07-27 17:20:51,358 - audio_agent.events.event_handler - INFO - 🔧 TTS synthesis ended, resetting LLM response sent flag
2025-07-27 17:20:54,039 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 450, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'asr_task_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_2_1', 'question_id': '13846671795458050', 'round_id': 4}}
2025-07-27 17:20:54,039 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 450 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,039 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,039 - audio_agent.events.event_handler - INFO - ASR first word detected - can interrupt playback
2025-07-27 17:20:54,042 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9217652678489685, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 5.762008, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天'}], 'end_time': 5.762008, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天'}]}}
2025-07-27 17:20:54,042 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,043 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,043 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,043 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,043 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,043 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天', interim: True
2025-07-27 17:20:54,043 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天'
2025-07-27 17:20:54,085 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.9217652678489685, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 5.82201, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天'}], 'end_time': 5.82201, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天'}]}}
2025-07-27 17:20:54,085 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,085 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,085 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,085 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,085 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,086 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天', interim: True
2025-07-27 17:20:54,086 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天'
2025-07-27 17:20:54,162 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8893074989318848, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 5.922012, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得'}], 'end_time': 5.922012, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得'}]}}
2025-07-27 17:20:54,163 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,163 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,163 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,163 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,163 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,163 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得', interim: True
2025-07-27 17:20:54,164 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得'
2025-07-27 17:20:54,239 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8893074989318848, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 5.982013, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得'}], 'end_time': 5.982013, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得'}]}}
2025-07-27 17:20:54,239 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,239 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,239 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,239 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,239 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,239 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得', interim: True
2025-07-27 17:20:54,240 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得'
2025-07-27 17:20:54,315 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.850717306137085, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.082016, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还'}], 'end_time': 6.082016, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还'}]}}
2025-07-27 17:20:54,315 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,315 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,315 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,315 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,316 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,316 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还', interim: True
2025-07-27 17:20:54,316 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还'
2025-07-27 17:20:54,391 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3'}, 'results': [{'alternatives': [{'end_time': 6.142017, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.142017, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:54,391 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,391 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,391 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,391 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,391 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,391 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:54,392 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:54,468 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'prefetch': True, 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 90.00017648315418}, 'results': [{'alternatives': [{'end_time': 6.242019, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错。'}], 'end_time': 6.242019, 'index': 0, 'is_interim': True, 'is_soft_finished': True, 'is_vad_timeout': False, 'soft_vad_type': 999, 'start_time': 4.912, 'text': '今天过得还不错。'}]}}
2025-07-27 17:20:54,468 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,468 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,468 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,469 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,469 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,469 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错。', interim: True
2025-07-27 17:20:54,469 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错。'
2025-07-27 17:20:54,575 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 150.002026611328}, 'results': [{'alternatives': [{'end_time': 6.302021, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.302021, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:54,575 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,575 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,575 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,575 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,575 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,575 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:54,575 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:54,621 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 250.00383859252918}, 'results': [{'alternatives': [{'end_time': 6.402023, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.402023, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:54,621 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,621 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,621 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,621 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,621 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,621 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:54,622 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:54,697 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 300.0002118835443}, 'results': [{'alternatives': [{'end_time': 6.462024, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.462024, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:54,697 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,697 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,697 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,697 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,697 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,698 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:54,698 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:54,775 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 400.0029775390619}, 'results': [{'alternatives': [{'end_time': 6.562027, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.562027, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:54,775 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,775 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,775 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,775 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,775 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,776 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:54,776 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:54,851 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 449.99987399291985}, 'results': [{'alternatives': [{'end_time': 6.622028, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.622028, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:54,851 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,851 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,851 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,851 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,851 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,851 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:54,852 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:54,959 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 540.0001628112792}, 'results': [{'alternatives': [{'end_time': 6.72203, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.72203, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:54,959 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:54,959 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:54,959 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:54,960 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:54,960 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:54,960 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:54,960 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:55,006 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 600.002012939453}, 'results': [{'alternatives': [{'end_time': 6.782032, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.782032, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:55,006 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,006 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,006 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:55,006 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:55,007 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:55,007 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:55,007 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:55,114 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 700.0038249206542}, 'results': [{'alternatives': [{'end_time': 6.882034, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.882034, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:55,114 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,114 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,114 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:55,114 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:55,114 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:55,114 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:55,115 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:55,192 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 760.0051982116698}, 'results': [{'alternatives': [{'end_time': 6.942035, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 6.942035, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:55,192 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,192 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,192 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:55,192 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:55,193 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:55,193 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:55,193 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:55,275 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'interrupt_score': 0.8236581087112427, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 860.007010192871}, 'results': [{'alternatives': [{'end_time': 7.042037, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错'}], 'end_time': 7.042037, 'index': 0, 'is_interim': True, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错'}]}}
2025-07-27 17:20:55,276 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,276 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,276 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:55,277 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:55,277 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:55,277 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错', interim: True
2025-07-27 17:20:55,277 - audio_agent.events.event_processor - INFO - 🎤 ASR interim result saved: '今天过得还不错'
2025-07-27 17:20:55,376 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 451, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'extra': {'endpoint': True, 'interrupt_score': 0.0611393004655838, 'is_pvad': False, 'model_version': 'BigASR-BigStreamv1.4.8_chunk_8', 'origin_text': '今天过得还不错', 'req_payload': {'end_smooth_silence_proportion': 0.9, 'eos_silence_timeout': 1500}, 'soft_finish_paralinguistic': {'asr_text': '今天过得还不错。', 'para_resp': {}, 'para_text': ''}, 'source': 'bigasr-acllm-release-streaming-grpc-v3', 'vad_backtrack_silence_time_ms': 920.0088603210448}, 'results': [{'alternatives': [{'end_time': 7.102039, 'oi_decoding_info': {'oi_former_word_num': 0, 'oi_latter_word_num': 0, 'oi_words': None}, 'semantic_related_to_prev': None, 'start_time': 4.912, 'text': '今天过得还不错。'}], 'end_time': 7.102039, 'index': 0, 'is_interim': False, 'is_vad_timeout': False, 'start_time': 4.912, 'text': '今天过得还不错。'}]}}
2025-07-27 17:20:55,376 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 451 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,376 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,376 - audio_agent.events.event_processor - INFO - 🎤 ASR event received: 451
2025-07-27 17:20:55,376 - audio_agent.events.event_processor - INFO - 🎤 ASR results count: 1
2025-07-27 17:20:55,376 - audio_agent.events.event_processor - INFO - 🎤 Payload keys: ['extra', 'results']
2025-07-27 17:20:55,376 - audio_agent.events.event_processor - INFO - 🎤 ASR text: '今天过得还不错。', interim: False
2025-07-27 17:20:55,376 - audio_agent.events.event_processor - INFO - 🎤 ASR final result saved: '今天过得还不错。'
2025-07-27 17:20:55,380 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 459, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'comfort_wait_time': 0, 'last_resp_cost_time': 1018, 'no_content': False, 'task_request_seq_id': 0, 'task_request_timestamp': 0, 'user_duration': 6182}}
2025-07-27 17:20:55,380 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 459 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,380 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,380 - audio_agent.events.event_handler - INFO - ASR ended - user finished speaking
2025-07-27 17:20:55,380 - audio_agent.events.event_processor - INFO - 🏁 ASR ended event received: 459
2025-07-27 17:20:55,380 - audio_agent.events.event_processor - INFO - 🏁 Current user input: '今天过得还不错。'
2025-07-27 17:20:55,380 - audio_agent.events.event_processor - INFO - 🚀 ASR ended, processing user input: '今天过得还不错。'
2025-07-27 17:20:55,382 - audio_agent.events.event_processor - INFO - 🚨 ChatTTSText(start) sent immediately at ASR end - following Volcengine official flow
2025-07-27 17:20:55,383 - audio_agent.events.llm_enhancer - INFO - 🤔 Ambiguous time mention, treating as conversation: '今天过得还不错。'
2025-07-27 17:20:55,383 - audio_agent.events.llm_enhancer - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.8, 'requires_tools': False, 'tools': []}
2025-07-27 17:20:55,383 - audio_agent.events.event_processor - INFO - Intent recognized: {'intent': 'general_conversation', 'confidence': 0.8, 'requires_tools': False, 'tools': []}
2025-07-27 17:20:55,385 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'enable_v3_loudness_balance': True, 'model_type': 'v3', 'tts_task_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_4_1', 'tts_type': 'default', 'v3_loundness_params': ''}}
2025-07-27 17:20:55,385 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,385 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,406 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 17:20:55,406 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:55,407 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:55,407 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 17:20:55,408 - audio_agent.core.session_manager - INFO - Audio response queued for playback (36000 bytes)
2025-07-27 17:20:55,420 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 17:20:55,439 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38396bytes>', 'payload_msg': '<binary_data:38396bytes>'}
2025-07-27 17:20:55,439 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:55,439 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:55,439 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38396 bytes
2025-07-27 17:20:55,440 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38396 bytes)
2025-07-27 17:20:55,472 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38856bytes>', 'payload_msg': '<binary_data:38856bytes>'}
2025-07-27 17:20:55,472 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:55,472 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:55,472 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38856 bytes
2025-07-27 17:20:55,473 - audio_agent.core.session_manager - INFO - Audio response queued for playback (38856 bytes)
2025-07-27 17:20:55,488 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': ''}}
2025-07-27 17:20:55,488 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,488 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,503 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '那'}}
2025-07-27 17:20:55,503 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,504 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,519 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '太好了'}}
2025-07-27 17:20:55,519 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,519 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,534 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '！能跟我说说'}}
2025-07-27 17:20:55,534 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,535 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,550 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '今天'}}
2025-07-27 17:20:55,550 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,551 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,566 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '遇到'}}
2025-07-27 17:20:55,567 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,567 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,581 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '什么'}}
2025-07-27 17:20:55,581 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,582 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,597 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '开心'}}
2025-07-27 17:20:55,598 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,598 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,613 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '事儿'}}
2025-07-27 17:20:55,613 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,613 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,627 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:37912bytes>', 'payload_msg': '<binary_data:37912bytes>'}
2025-07-27 17:20:55,627 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:55,627 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:55,627 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37912 bytes
2025-07-27 17:20:55,628 - audio_agent.core.session_manager - INFO - Audio response queued for playback (37912 bytes)
2025-07-27 17:20:55,645 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '了'}}
2025-07-27 17:20:55,645 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,646 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,662 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '吗'}}
2025-07-27 17:20:55,663 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,663 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,675 - audio_agent.events.event_handler - INFO - LLM result: {'user_input': '今天过得还不错。', 'ai_response': "我理解您说的是'今天过得还不错。'。我可以为您做些什么吗？"}
2025-07-27 17:20:55,675 - audio_agent.events.event_processor - INFO - LLM response ready for TTS: 我理解您说的是'今天过得还不错。'。我可以为您做些什么吗？
2025-07-27 17:20:55,678 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 550, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'content': '？'}}
2025-07-27 17:20:55,679 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 550 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,679 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,681 - audio_agent.events.event_processor - INFO - ✅ LLM enhanced response sent to server for TTS: '我理解您说的是'今天过得还不错。'。我可以为您做些什么吗？...'
2025-07-27 17:20:55,681 - audio_agent.events.event_processor - INFO - LLM enhanced response sent to user successfully
2025-07-27 17:20:55,682 - audio_agent.events.event_processor - INFO - Response generated: '我理解您说的是'今天过得还不错。'。我可以为您做些什么吗？'
2025-07-27 17:20:55,682 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 17:20:55,682 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: default, text: '', task_id: 6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_4_1
2025-07-27 17:20:55,682 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-27 17:20:55,682 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 17:20:55,682 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 17:20:55,682 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 17:20:55,682 - audio_agent.events.event_handler - INFO - TTS sentence start - type: default, text: ''
2025-07-27 17:20:55,682 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - TTS audio response: 38396 bytes
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - TTS audio response: 38856 bytes
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,683 - audio_agent.events.event_handler - INFO - TTS audio response: 37912 bytes
2025-07-27 17:20:55,684 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,684 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,684 - audio_agent.events.event_handler - INFO - Chat response: ''
2025-07-27 17:20:55,686 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38376bytes>', 'payload_msg': '<binary_data:38376bytes>'}
2025-07-27 17:20:55,686 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:55,687 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:55,687 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38376 bytes
2025-07-27 17:20:55,687 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38376 bytes) - LLM mock response already sent
2025-07-27 17:20:55,687 - audio_agent.events.event_handler - INFO - TTS audio response: 38376 bytes
2025-07-27 17:20:55,693 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38684bytes>', 'payload_msg': '<binary_data:38684bytes>'}
2025-07-27 17:20:55,693 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:55,693 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:55,693 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38684 bytes
2025-07-27 17:20:55,693 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38684 bytes) - LLM mock response already sent
2025-07-27 17:20:55,694 - audio_agent.events.event_handler - INFO - TTS audio response: 38684 bytes
2025-07-27 17:20:55,764 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38412bytes>', 'payload_msg': '<binary_data:38412bytes>'}
2025-07-27 17:20:55,764 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:55,764 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:55,764 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38412 bytes
2025-07-27 17:20:55,764 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38412 bytes) - LLM mock response already sent
2025-07-27 17:20:55,765 - audio_agent.events.event_handler - INFO - TTS audio response: 38412 bytes
2025-07-27 17:20:55,876 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38124bytes>', 'payload_msg': '<binary_data:38124bytes>'}
2025-07-27 17:20:55,876 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:55,876 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:55,877 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38124 bytes
2025-07-27 17:20:55,877 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38124 bytes) - LLM mock response already sent
2025-07-27 17:20:55,877 - audio_agent.events.event_handler - INFO - TTS audio response: 38124 bytes
2025-07-27 17:20:55,956 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 350, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'enable_v3_loudness_balance': True, 'gta': True, 'model_type': 'v3', 'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"Chinese","emotionTag":"","eosProsody":40,"flushed":false,"markdown":{"prev_parsed_text":"我理解您说的是\'今天过得还不错。\'。我可以...<truncated:682chars>', 'text': "我理解您说的是'今天过得还不错。'。我可以为您做些什么吗？", 'tts_task_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_5_0', 'tts_type': 'chat_tts_text', 'v3_loundness_params': ''}}
2025-07-27 17:20:55,956 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 350 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:55,956 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:55,956 - audio_agent.events.event_handler - INFO - 🎯 TTS_SENTENCE_START handler called! Event: 350
2025-07-27 17:20:55,956 - audio_agent.events.event_handler - INFO - 🎯 TTS info - type: chat_tts_text, text: '我理解您说的是'今天过得还不错。'。我可以为您做些什么吗？', task_id: 6038ffe6-c4ed-4cc2-8f1c-57896df9fb11_5_0
2025-07-27 17:20:55,956 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - event_processor: True
2025-07-27 17:20:55,957 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - has tool_calling_active: True
2025-07-27 17:20:55,957 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool_calling_active: False
2025-07-27 17:20:55,957 - audio_agent.events.event_handler - INFO - 🔍 TTS Filter Debug - tool calling not active, allowing TTS
2025-07-27 17:20:55,957 - audio_agent.events.event_handler - INFO - TTS sentence start - type: chat_tts_text, text: '我理解您说的是'今天过得还不错。'。我可以为您做些什么吗？'
2025-07-27 17:20:56,452 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:36000bytes>', 'payload_msg': '<binary_data:36000bytes>'}
2025-07-27 17:20:56,453 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:56,453 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:56,453 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36000 bytes
2025-07-27 17:20:56,453 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (36000 bytes) - LLM mock response already sent
2025-07-27 17:20:56,453 - audio_agent.events.event_handler - INFO - TTS audio response: 36000 bytes
2025-07-27 17:20:56,498 - audio_agent.core.session_manager - INFO - AI finished speaking
2025-07-27 17:20:56,502 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38300bytes>', 'payload_msg': '<binary_data:38300bytes>'}
2025-07-27 17:20:56,502 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:56,502 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:56,502 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38300 bytes
2025-07-27 17:20:56,502 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38300 bytes) - LLM mock response already sent
2025-07-27 17:20:56,502 - audio_agent.events.event_handler - INFO - TTS audio response: 38300 bytes
2025-07-27 17:20:56,614 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38504bytes>', 'payload_msg': '<binary_data:38504bytes>'}
2025-07-27 17:20:56,615 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:56,615 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:56,615 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38504 bytes
2025-07-27 17:20:56,615 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38504 bytes) - LLM mock response already sent
2025-07-27 17:20:56,615 - audio_agent.events.event_handler - INFO - TTS audio response: 38504 bytes
2025-07-27 17:20:56,712 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38296bytes>', 'payload_msg': '<binary_data:38296bytes>'}
2025-07-27 17:20:56,712 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:56,712 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:56,713 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38296 bytes
2025-07-27 17:20:56,713 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38296 bytes) - LLM mock response already sent
2025-07-27 17:20:56,713 - audio_agent.events.event_handler - INFO - TTS audio response: 38296 bytes
2025-07-27 17:20:56,877 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38444bytes>', 'payload_msg': '<binary_data:38444bytes>'}
2025-07-27 17:20:56,878 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:56,878 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:56,878 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38444 bytes
2025-07-27 17:20:56,878 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38444 bytes) - LLM mock response already sent
2025-07-27 17:20:56,878 - audio_agent.events.event_handler - INFO - TTS audio response: 38444 bytes
2025-07-27 17:20:56,995 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38156bytes>', 'payload_msg': '<binary_data:38156bytes>'}
2025-07-27 17:20:56,996 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:56,996 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:56,996 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38156 bytes
2025-07-27 17:20:56,996 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38156 bytes) - LLM mock response already sent
2025-07-27 17:20:56,996 - audio_agent.events.event_handler - INFO - TTS audio response: 38156 bytes
2025-07-27 17:20:57,142 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38748bytes>', 'payload_msg': '<binary_data:38748bytes>'}
2025-07-27 17:20:57,142 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:57,142 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:57,142 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38748 bytes
2025-07-27 17:20:57,143 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38748 bytes) - LLM mock response already sent
2025-07-27 17:20:57,143 - audio_agent.events.event_handler - INFO - TTS audio response: 38748 bytes
2025-07-27 17:20:57,230 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38412bytes>', 'payload_msg': '<binary_data:38412bytes>'}
2025-07-27 17:20:57,230 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:57,230 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:57,230 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38412 bytes
2025-07-27 17:20:57,230 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38412 bytes) - LLM mock response already sent
2025-07-27 17:20:57,231 - audio_agent.events.event_handler - INFO - TTS audio response: 38412 bytes
2025-07-27 17:20:57,350 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:37992bytes>', 'payload_msg': '<binary_data:37992bytes>'}
2025-07-27 17:20:57,350 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:57,351 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:57,351 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 37992 bytes
2025-07-27 17:20:57,351 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (37992 bytes) - LLM mock response already sent
2025-07-27 17:20:57,351 - audio_agent.events.event_handler - INFO - TTS audio response: 37992 bytes
2025-07-27 17:20:57,438 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:38444bytes>', 'payload_msg': '<binary_data:38444bytes>'}
2025-07-27 17:20:57,438 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:57,438 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:57,438 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 38444 bytes
2025-07-27 17:20:57,439 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (38444 bytes) - LLM mock response already sent
2025-07-27 17:20:57,439 - audio_agent.events.event_handler - INFO - TTS audio response: 38444 bytes
2025-07-27 17:20:57,683 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_ACK', 'event': 352, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': '<binary_data:36476bytes>', 'payload_msg': '<binary_data:36476bytes>'}
2025-07-27 17:20:57,684 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 352 (message_type: SERVER_ACK)
2025-07-27 17:20:57,684 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload', 'payload_msg']
2025-07-27 17:20:57,684 - audio_agent.core.session_manager - INFO - SERVER_ACK: Received audio data of 36476 bytes
2025-07-27 17:20:57,684 - audio_agent.core.session_manager - WARNING - 🚫 DISCARDING SERVER_ACK audio chunk (36476 bytes) - LLM mock response already sent
2025-07-27 17:20:57,684 - audio_agent.events.event_handler - INFO - TTS audio response: 36476 bytes
2025-07-27 17:20:57,690 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 351, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'silence_context': '{"contextLanguage":"unknown","contextTexts":["{\\"text\\":\\"\\"}"],"defaultNeutralLanguage":"Chinese","emotionTag":"","eosProsody":40,"flushed":false,"markdown":{"prev_parsed_text":"我理解您说的是\'今天过得还不错。\'。我可以...<truncated:682chars>', 'speech_alignment_result': '', 'text': "我理解您说的是'今天过得还不错。'。我可以为您做些什么吗？"}}
2025-07-27 17:20:57,690 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 351 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:57,690 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:57,690 - audio_agent.events.event_handler - INFO - TTS sentence ended
2025-07-27 17:20:57,694 - audio_agent.core.session_manager - INFO - Response: {'message_type': 'SERVER_FULL_RESPONSE', 'event': 359, 'session_id': '6038ffe6-c4ed-4cc2-8f1c-57896df9fb11', 'payload': {'no_content': False}}
2025-07-27 17:20:57,695 - audio_agent.events.event_handler - INFO - 📨 Processing server event: 359 (message_type: SERVER_FULL_RESPONSE)
2025-07-27 17:20:57,695 - audio_agent.events.event_handler - INFO - 📨 Event data keys: ['message_type', 'event', 'session_id', 'payload']
2025-07-27 17:20:57,695 - audio_agent.events.event_handler - INFO - TTS synthesis ended
2025-07-27 17:20:57,695 - audio_agent.events.event_handler - INFO - 🔧 TTS synthesis ended, resetting LLM response sent flag
