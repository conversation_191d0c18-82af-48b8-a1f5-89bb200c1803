{"tests/test_tools.py::TestToolExecutor::test_tool_execution": true, "tests/test_tools.py::TestToolExecutor::test_tool_calls_format": true, "tests/test_websocket_events.py::TestWebSocketEventFlow::test_asr_to_llm_flow": true, "tests/test_websocket_events.py::TestWebSocketEventFlow::test_tts_audio_handling": true, "tests/test_websocket_events.py::TestWebSocketEventFlow::test_chat_response_handling": true, "tests/test_tts_filtering.py": true, "tests/test_tts_filtering.py::test_tts_filtering_default_type_during_tool_calling": true}