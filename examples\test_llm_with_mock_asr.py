#!/usr/bin/env python3
"""
模拟ASR事件测试LLM增强功能

通过模拟ASR事件来测试LLM增强功能是否真正工作，
绕过WebSocket连接问题。
"""

import sys
import asyncio
import logging
from pathlib import Path
from datetime import datetime
import uuid

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.events import EventProcessor, EventType, ServerEvent
from audio_agent.config import get_config


async def test_llm_enhancement_with_mock_asr():
    """通过模拟ASR事件测试LLM增强功能"""
    print("🧪 模拟ASR事件测试LLM增强功能")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 获取配置
    config = get_config()
    session_id = str(uuid.uuid4())
    
    # 创建EventProcessor (测试模式，不需要DialogClientActor)
    event_processor = EventProcessor(session_id, config, dialog_client_actor=None)
    
    print(f"✅ EventProcessor创建成功，会话ID: {session_id}")
    
    # 测试用例
    test_cases = [
        {
            "name": "时间查询",
            "user_input": "现在几点了？",
            "expected_intent": "datetime_query",
            "expected_tools": ["datetime"]
        },
        {
            "name": "数学计算",
            "user_input": "帮我算一下 15 + 27 * 3",
            "expected_intent": "calculator",
            "expected_tools": ["calculator"]
        },
        {
            "name": "普通对话",
            "user_input": "你好，很高兴认识你",
            "expected_intent": "general_conversation",
            "expected_tools": []
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        user_input = test_case["user_input"]
        print(f"用户输入: '{user_input}'")
        
        try:
            # 步骤1: 模拟ASR_RESPONSE事件 (451)
            print("1️⃣ 模拟ASR_RESPONSE事件...")
            
            asr_response_data = {
                'message_type': 'SERVER_FULL_RESPONSE',
                'event': 451,
                'session_id': session_id,
                'payload': {
                    'results': [{
                        'text': user_input,
                        'is_interim': False,
                        'alternatives': [{
                            'text': user_input,
                            'start_time': 0.0,
                            'end_time': 2.0
                        }]
                    }]
                }
            }
            
            asr_response_event = ServerEvent.from_websocket_message(asr_response_data, session_id)
            
            # 直接调用ASR处理方法
            await event_processor._handle_asr_with_llm_enhancement(asr_response_event)
            
            print(f"   ✅ ASR响应处理完成，当前用户输入: '{event_processor.current_user_input}'")
            
            # 步骤2: 模拟ASR_ENDED事件 (459)
            print("2️⃣ 模拟ASR_ENDED事件...")
            
            asr_ended_data = {
                'message_type': 'SERVER_FULL_RESPONSE',
                'event': 459,
                'session_id': session_id,
                'payload': {
                    'comfort_wait_time': 0,
                    'last_resp_cost_time': 100,
                    'no_content': False,
                    'user_duration': 2000
                }
            }
            
            asr_ended_event = ServerEvent.from_websocket_message(asr_ended_data, session_id)
            
            # 直接调用ASR结束处理方法
            await event_processor._handle_asr_ended_with_enhancement(asr_ended_event)
            
            print("   ✅ ASR结束事件处理完成")
            
            # 等待一下让LLM处理完成
            await asyncio.sleep(1)
            
            print(f"   📊 测试结果:")
            print(f"      - 用户输入已保存: {'✅' if event_processor.current_user_input else '❌'}")
            print(f"      - LLM处理状态: {'🔄 处理中' if event_processor.is_processing_llm else '✅ 完成'}")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 模拟ASR测试完成！")
    print()
    print("💡 测试结果说明:")
    print("  - 如果看到🎤和🏁的调试日志，说明事件处理正常")
    print("  - 如果看到🚀的日志，说明LLM增强功能被触发")
    print("  - 如果看到工具执行日志，说明工具调用正常")


async def test_direct_llm_enhancement():
    """直接测试LLM增强功能"""
    print("\n🧠 直接测试LLM增强功能")
    print("=" * 60)
    
    config = get_config()
    session_id = str(uuid.uuid4())
    
    # 创建EventProcessor (测试模式，不需要DialogClientActor)
    event_processor = EventProcessor(session_id, config, dialog_client_actor=None)
    
    # 测试用例
    test_inputs = [
        "现在几点了？",
        "帮我计算 2 + 3 * 4",
        "你好"
    ]
    
    for user_input in test_inputs:
        print(f"\n测试输入: '{user_input}'")
        
        try:
            # 直接调用LLM增强器
            llm_enhancer = event_processor.llm_enhancer
            
            # 1. 意图识别
            intent_result = await llm_enhancer.recognize_intent(user_input, [])
            print(f"  意图: {intent_result['intent']}")
            print(f"  置信度: {intent_result['confidence']}")
            print(f"  需要工具: {intent_result['requires_tools']}")
            
            if intent_result['requires_tools']:
                # 2. 工具执行
                print(f"  工具: {intent_result['tools']}")
                tool_results = await llm_enhancer.execute_tools(
                    user_input, 
                    intent_result['tools'], 
                    []
                )
                
                for result in tool_results:
                    if result['success']:
                        print(f"  ✅ {result['tool']}: {result['result']}")
                    else:
                        print(f"  ❌ {result['tool']}: {result['error']}")
                
                # 3. 生成增强响应
                response = await llm_enhancer.generate_enhanced_response(
                    user_input,
                    tool_results,
                    []
                )
                print(f"  📝 增强响应: {response}")
            else:
                # 生成普通响应
                response = await llm_enhancer.generate_response(
                    user_input,
                    [],
                    intent_result
                )
                print(f"  📝 普通响应: {response}")
                
        except Exception as e:
            print(f"  ❌ 错误: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主测试函数"""
    print("🚀 LLM增强功能完整测试")
    print("=" * 70)
    print("测试LLM增强功能是否真正工作，绕过WebSocket连接问题")
    print()
    
    # 测试1: 模拟ASR事件
    await test_llm_enhancement_with_mock_asr()
    
    # 测试2: 直接测试LLM功能
    await test_direct_llm_enhancement()
    
    print("\n🎉 所有测试完成！")
    print()
    print("💡 如果所有测试都通过，说明LLM增强功能已经完全集成并可以工作！")
    print("   音频播放问题是独立的WebSocket连接问题，不影响LLM功能。")


if __name__ == "__main__":
    asyncio.run(main())
