"""
Professional Voice Activity Detection (VAD) module.

This module provides multiple VAD implementations including:
- Silero VAD (recommended for production)
- WebRTC VAD (lightweight alternative)
- Simple amplitude-based VAD (fallback)
"""

import logging
import numpy as np
import time
from typing import Optional, List, Tuple
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class VADInterface(ABC):
    """Abstract interface for Voice Activity Detection."""
    
    @abstractmethod
    def is_speech(self, audio_chunk: np.ndarray, sample_rate: int) -> float:
        """
        Detect if audio chunk contains speech.
        
        Args:
            audio_chunk: Audio data as numpy array
            sample_rate: Sample rate of the audio
            
        Returns:
            Confidence score (0.0 = no speech, 1.0 = definitely speech)
        """
        pass
    
    @abstractmethod
    def reset(self) -> None:
        """Reset VAD state."""
        pass


class SileroVAD(VADInterface):
    """
    Silero VAD implementation - professional grade VAD.
    
    This is the recommended VAD for production use.
    Requires: pip install silero-vad
    """
    
    def __init__(self):
        """Initialize Silero VAD model."""
        self.model = None
        self.utils = None
        self._initialize_model()
    
    def _initialize_model(self) -> None:
        """Initialize the Silero VAD model."""
        try:
            import torch
            from silero_vad import load_silero_vad, get_speech_timestamps
            
            # Set single thread for better performance in real-time scenarios
            torch.set_num_threads(1)
            
            self.model = load_silero_vad()
            self.get_speech_timestamps = get_speech_timestamps
            
            logger.info("Silero VAD model loaded successfully")
            
        except ImportError:
            logger.error("Silero VAD not available. Install with: pip install silero-vad")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Silero VAD: {e}")
            raise
    
    def is_speech(self, audio_chunk: np.ndarray, sample_rate: int) -> float:
        """
        Detect speech using Silero VAD.
        
        Args:
            audio_chunk: Audio data as numpy array (float32, range -1 to 1)
            sample_rate: Sample rate (8000 or 16000 Hz recommended)
            
        Returns:
            Confidence score (0.0-1.0)
        """
        try:
            import torch
            
            # Convert to torch tensor if needed
            if isinstance(audio_chunk, np.ndarray):
                audio_tensor = torch.from_numpy(audio_chunk.astype(np.float32))
            else:
                audio_tensor = audio_chunk
            
            # Ensure correct shape (1D tensor)
            if audio_tensor.dim() > 1:
                audio_tensor = audio_tensor.squeeze()
            
            # Get speech probability
            speech_prob = self.model(audio_tensor, sample_rate).item()
            
            return speech_prob
            
        except Exception as e:
            logger.error(f"Silero VAD error: {e}")
            return 0.0
    
    def reset(self) -> None:
        """Reset Silero VAD state."""
        # Silero VAD is stateless, no reset needed
        pass


class WebRTCVAD(VADInterface):
    """
    WebRTC VAD implementation - lightweight alternative.
    
    Requires: pip install webrtcvad
    """
    
    def __init__(self, aggressiveness: int = 3):
        """
        Initialize WebRTC VAD.
        
        Args:
            aggressiveness: VAD aggressiveness (0-3, higher = more aggressive)
        """
        self.aggressiveness = aggressiveness
        self.vad = None
        self._initialize_model()
    
    def _initialize_model(self) -> None:
        """Initialize WebRTC VAD."""
        try:
            import webrtcvad
            
            self.vad = webrtcvad.Vad(self.aggressiveness)
            logger.info(f"WebRTC VAD initialized with aggressiveness {self.aggressiveness}")
            
        except ImportError:
            logger.error("WebRTC VAD not available. Install with: pip install webrtcvad")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize WebRTC VAD: {e}")
            raise
    
    def is_speech(self, audio_chunk: np.ndarray, sample_rate: int) -> float:
        """
        Detect speech using WebRTC VAD.
        
        Args:
            audio_chunk: Audio data as numpy array (int16)
            sample_rate: Sample rate (8000, 16000, 32000, or 48000 Hz)
            
        Returns:
            Confidence score (0.0 or 1.0 - WebRTC VAD is binary)
        """
        try:
            # Convert to int16 if needed
            if audio_chunk.dtype != np.int16:
                if audio_chunk.dtype == np.float32:
                    # Convert from float32 [-1, 1] to int16
                    audio_chunk = (audio_chunk * 32767).astype(np.int16)
                else:
                    audio_chunk = audio_chunk.astype(np.int16)
            
            # WebRTC VAD requires specific frame sizes
            # For 16kHz: 160, 320, or 480 samples (10ms, 20ms, 30ms)
            frame_duration_ms = 30  # Use 30ms frames
            frame_size = int(sample_rate * frame_duration_ms / 1000)
            
            # Pad or trim to frame size
            if len(audio_chunk) < frame_size:
                audio_chunk = np.pad(audio_chunk, (0, frame_size - len(audio_chunk)))
            elif len(audio_chunk) > frame_size:
                audio_chunk = audio_chunk[:frame_size]
            
            # Convert to bytes
            audio_bytes = audio_chunk.tobytes()
            
            # Detect speech
            is_speech = self.vad.is_speech(audio_bytes, sample_rate)
            
            return 1.0 if is_speech else 0.0
            
        except Exception as e:
            logger.error(f"WebRTC VAD error: {e}")
            return 0.0
    
    def reset(self) -> None:
        """Reset WebRTC VAD state."""
        # WebRTC VAD is stateless, no reset needed
        pass


class SimpleVAD(VADInterface):
    """
    Simple amplitude-based VAD - fallback implementation.
    
    This is a basic implementation for when professional VAD is not available.
    """
    
    def __init__(self, threshold: float = 0.01, window_size: int = 3):
        """
        Initialize Simple VAD.
        
        Args:
            threshold: Amplitude threshold for speech detection
            window_size: Number of frames to consider for smoothing
        """
        self.threshold = threshold
        self.window_size = window_size
        self.amplitude_history = []
    
    def is_speech(self, audio_chunk: np.ndarray, sample_rate: int) -> float:
        """
        Detect speech using amplitude analysis.
        
        Args:
            audio_chunk: Audio data as numpy array
            sample_rate: Sample rate (not used in this implementation)
            
        Returns:
            Confidence score (0.0-1.0)
        """
        try:
            # Calculate RMS amplitude
            rms = np.sqrt(np.mean(audio_chunk ** 2))
            
            # Add to history
            self.amplitude_history.append(rms)
            if len(self.amplitude_history) > self.window_size:
                self.amplitude_history.pop(0)
            
            # Calculate smoothed amplitude
            avg_amplitude = np.mean(self.amplitude_history)
            
            # Convert to confidence score
            confidence = min(avg_amplitude / self.threshold, 1.0)
            
            return confidence
            
        except Exception as e:
            logger.error(f"Simple VAD error: {e}")
            return 0.0
    
    def reset(self) -> None:
        """Reset Simple VAD state."""
        self.amplitude_history.clear()


class VADManager:
    """
    VAD Manager that handles different VAD implementations and provides
    a unified interface with interrupt detection logic.
    """
    
    def __init__(self, vad_model: str = "silero", threshold: float = 0.5, 
                 window_size_ms: int = 30, min_speech_duration_ms: int = 100):
        """
        Initialize VAD Manager.
        
        Args:
            vad_model: VAD model to use ("silero", "webrtc", "simple")
            threshold: Confidence threshold for speech detection
            window_size_ms: Window size in milliseconds
            min_speech_duration_ms: Minimum speech duration to trigger interrupt
        """
        self.vad_model = vad_model
        self.threshold = threshold
        self.window_size_ms = window_size_ms
        self.min_speech_duration_ms = min_speech_duration_ms
        
        # State tracking
        self.is_speech_active = False
        self.speech_start_time = None
        self.last_speech_time = None
        self.confidence_history = []
        
        # Initialize VAD
        self.vad = self._create_vad()
        
        logger.info(f"VAD Manager initialized with {vad_model} model")
    
    def _create_vad(self) -> VADInterface:
        """Create VAD instance based on model type."""
        if self.vad_model == "silero":
            try:
                return SileroVAD()
            except Exception as e:
                logger.warning(f"Failed to load Silero VAD: {e}. Falling back to WebRTC VAD.")
                return self._create_fallback_vad()
        
        elif self.vad_model == "webrtc":
            try:
                return WebRTCVAD()
            except Exception as e:
                logger.warning(f"Failed to load WebRTC VAD: {e}. Falling back to Simple VAD.")
                return SimpleVAD()
        
        elif self.vad_model == "simple":
            return SimpleVAD()
        
        else:
            logger.warning(f"Unknown VAD model: {self.vad_model}. Using Simple VAD.")
            return SimpleVAD()
    
    def _create_fallback_vad(self) -> VADInterface:
        """Create fallback VAD when preferred model fails."""
        try:
            return WebRTCVAD()
        except Exception:
            logger.warning("WebRTC VAD also failed. Using Simple VAD.")
            return SimpleVAD()
    
    def process_audio(self, audio_chunk: np.ndarray, sample_rate: int) -> Tuple[bool, float]:
        """
        Process audio chunk and detect speech activity.
        
        Args:
            audio_chunk: Audio data as numpy array
            sample_rate: Sample rate of the audio
            
        Returns:
            Tuple of (should_interrupt, confidence_score)
        """
        current_time = time.time()
        
        # Get speech confidence from VAD
        confidence = self.vad.is_speech(audio_chunk, sample_rate)
        
        # Add to confidence history
        self.confidence_history.append(confidence)
        max_history_size = max(1, self.window_size_ms // 10)  # Assume 10ms chunks
        if len(self.confidence_history) > max_history_size:
            self.confidence_history.pop(0)
        
        # Calculate smoothed confidence
        avg_confidence = np.mean(self.confidence_history)
        
        # Determine if speech is active
        speech_detected = avg_confidence > self.threshold
        
        should_interrupt = False
        
        if speech_detected:
            if not self.is_speech_active:
                # Speech started
                self.is_speech_active = True
                self.speech_start_time = current_time
                logger.debug("Speech activity started")
            
            self.last_speech_time = current_time
            
            # Check if speech duration is sufficient for interrupt
            if (current_time - self.speech_start_time) * 1000 >= self.min_speech_duration_ms:
                should_interrupt = True
        
        else:
            if self.is_speech_active:
                # Speech ended
                self.is_speech_active = False
                speech_duration = (current_time - self.speech_start_time) * 1000
                logger.debug(f"Speech activity ended (duration: {speech_duration:.1f}ms)")
        
        return should_interrupt, avg_confidence
    
    def reset(self) -> None:
        """Reset VAD manager state."""
        self.is_speech_active = False
        self.speech_start_time = None
        self.last_speech_time = None
        self.confidence_history.clear()
        self.vad.reset()
        logger.debug("VAD Manager state reset")
    
    def get_speech_status(self) -> dict:
        """Get current speech detection status."""
        current_time = time.time()
        
        return {
            "is_speech_active": self.is_speech_active,
            "speech_duration_ms": (
                (current_time - self.speech_start_time) * 1000 
                if self.speech_start_time else 0
            ),
            "time_since_last_speech_ms": (
                (current_time - self.last_speech_time) * 1000 
                if self.last_speech_time else float('inf')
            ),
            "avg_confidence": np.mean(self.confidence_history) if self.confidence_history else 0.0,
            "vad_model": self.vad_model
        }
