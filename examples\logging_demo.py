#!/usr/bin/env python3
"""
日志配置演示

展示如何使用AudioAgent的日志配置功能来控制日志输出级别。
"""

import sys
import asyncio
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from audio_agent.config import get_config, update_config


def demo_default_logging():
    """演示默认日志配置"""
    print("🔧 1. 默认日志配置演示")
    print("=" * 50)
    
    # 获取默认配置
    config = get_config()
    
    print(f"默认日志级别: {config.logging.level}")
    print(f"音频调试: {config.logging.audio_debug}")
    print(f"WebSocket调试: {config.logging.websocket_debug}")
    print(f"事件调试: {config.logging.event_debug}")
    
    # 测试不同级别的日志
    logger = logging.getLogger("demo")
    logger.debug("这是DEBUG级别日志 - 默认不会显示")
    logger.info("这是INFO级别日志 - 会显示")
    logger.warning("这是WARNING级别日志 - 会显示")
    logger.error("这是ERROR级别日志 - 会显示")
    
    print()


def demo_debug_logging():
    """演示调试日志配置"""
    print("🐛 2. 调试日志配置演示")
    print("=" * 50)
    
    # 更新为调试模式
    update_config(
        logging={
            "level": "DEBUG",
            "audio_debug": True,
            "websocket_debug": True,
            "event_debug": True
        }
    )
    
    config = get_config()
    print(f"调试日志级别: {config.logging.level}")
    print(f"音频调试: {config.logging.audio_debug}")
    print(f"WebSocket调试: {config.logging.websocket_debug}")
    print(f"事件调试: {config.logging.event_debug}")
    
    # 测试调试日志
    logger = logging.getLogger("demo")
    logger.debug("现在DEBUG级别日志会显示了！")
    logger.info("INFO级别日志")
    
    # 模拟音频处理日志
    audio_logger = logging.getLogger("audio_agent.core.actors")
    audio_logger.debug("模拟音频数据处理日志")
    
    # 模拟事件系统日志
    event_logger = logging.getLogger("audio_agent.events")
    event_logger.debug("模拟事件处理日志")
    
    print()


def demo_quiet_logging():
    """演示安静模式日志配置"""
    print("🔇 3. 安静模式日志配置演示")
    print("=" * 50)
    
    # 更新为安静模式
    update_config(
        logging={
            "level": "WARNING",
            "audio_debug": False,
            "websocket_debug": False,
            "event_debug": False
        }
    )
    
    config = get_config()
    print(f"安静模式日志级别: {config.logging.level}")
    
    # 测试安静模式
    logger = logging.getLogger("demo")
    logger.debug("DEBUG日志 - 不会显示")
    logger.info("INFO日志 - 不会显示")
    logger.warning("WARNING日志 - 会显示")
    logger.error("ERROR日志 - 会显示")
    
    print()


def demo_file_logging():
    """演示文件日志配置"""
    print("📁 4. 文件日志配置演示")
    print("=" * 50)
    
    # 启用文件日志
    update_config(
        logging={
            "level": "INFO",
            "enable_file_logging": True,
            "log_file": "demo_audio_agent.log"
        }
    )
    
    config = get_config()
    print(f"文件日志启用: {config.logging.enable_file_logging}")
    print(f"日志文件: {config.logging.log_file}")
    
    # 写入一些日志到文件
    logger = logging.getLogger("demo")
    logger.info("这条日志会同时写入控制台和文件")
    logger.warning("文件日志演示完成")
    
    print(f"✅ 日志已写入文件: {config.logging.log_file}")
    print()


def demo_environment_variables():
    """演示环境变量配置"""
    print("🌍 5. 环境变量配置演示")
    print("=" * 50)
    
    print("可以通过以下环境变量控制日志:")
    print()
    print("# 基本日志配置")
    print("export AUDIO_AGENT_LOGGING_LEVEL=DEBUG")
    print("export AUDIO_AGENT_LOGGING_ENABLE_FILE_LOGGING=true")
    print("export AUDIO_AGENT_LOGGING_LOG_FILE=my_audio_agent.log")
    print()
    print("# 调试开关")
    print("export AUDIO_AGENT_LOGGING_AUDIO_DEBUG=true")
    print("export AUDIO_AGENT_LOGGING_WEBSOCKET_DEBUG=true") 
    print("export AUDIO_AGENT_LOGGING_EVENT_DEBUG=true")
    print()
    print("# 运行应用")
    print("uv run python scripts/run.py")
    print()


def demo_production_logging():
    """演示生产环境日志配置"""
    print("🏭 6. 生产环境日志配置演示")
    print("=" * 50)
    
    # 生产环境配置
    update_config(
        logging={
            "level": "INFO",
            "enable_file_logging": True,
            "log_file": "audio_agent_production.log",
            "audio_debug": False,
            "websocket_debug": False,
            "event_debug": False
        }
    )
    
    config = get_config()
    print("生产环境推荐配置:")
    print(f"  日志级别: {config.logging.level}")
    print(f"  文件日志: {config.logging.enable_file_logging}")
    print(f"  调试模式: 全部关闭")
    
    logger = logging.getLogger("production")
    logger.info("生产环境日志配置完成")
    logger.warning("只有重要信息会被记录")
    
    print("✅ 这样可以减少日志噪音，提高性能")
    print()


def main():
    """主演示函数"""
    print("🎯 AudioAgent 日志配置演示")
    print("=" * 60)
    print("这个演示展示了如何控制AudioAgent的日志输出")
    print()
    
    # 运行各种演示
    demo_default_logging()
    demo_debug_logging()
    demo_quiet_logging()
    demo_file_logging()
    demo_environment_variables()
    demo_production_logging()
    
    print("🎉 日志配置演示完成！")
    print()
    print("💡 使用建议:")
    print("  - 开发时使用DEBUG级别和调试开关")
    print("  - 生产环境使用INFO级别，关闭调试开关")
    print("  - 启用文件日志以便问题排查")
    print("  - 通过环境变量灵活控制日志配置")


if __name__ == "__main__":
    main()
