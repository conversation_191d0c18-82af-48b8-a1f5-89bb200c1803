#!/usr/bin/env python3
"""
Real-world test for audio playback interrupt functionality.

This script tests the interrupt feature with actual audio playback.
"""

import asyncio
import logging
import time
import numpy as np
from audio_agent.config import get_config
from audio_agent.core import SessionManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RealInterruptTest:
    """Test class for real interrupt functionality."""
    
    def __init__(self):
        """Initialize the test."""
        self.config = get_config()
        self.session_manager = None
        
        # Test configuration
        self.config.interrupt.enabled = True
        self.config.interrupt.response_delay_ms = 50
        self.config.input_audio.vad_enabled = True
        self.config.input_audio.vad_threshold = 500  # Higher threshold for real test
        
        logger.info("Real interrupt test initialized")
    
    def generate_test_audio(self, duration_seconds: float, frequency: float = 440.0) -> bytes:
        """Generate test audio data (sine wave)."""
        sample_rate = self.config.output_audio.sample_rate
        samples = int(duration_seconds * sample_rate)
        
        # Generate sine wave
        t = np.linspace(0, duration_seconds, samples, False)
        wave = np.sin(2 * np.pi * frequency * t)
        
        # Convert to int16 format
        audio_int16 = (wave * 32767).astype(np.int16)
        return audio_int16.tobytes()
    
    async def test_interrupt_with_real_audio(self):
        """Test interrupt functionality with real audio playback."""
        logger.info("=== Testing Real Audio Interrupt ===")
        
        try:
            # Start session manager
            self.session_manager = SessionManager(self.config)
            await self.session_manager.start()
            
            # Generate 5 seconds of test audio (440Hz tone)
            test_audio = self.generate_test_audio(5.0, 440.0)
            chunk_size = self.config.output_audio.chunk * 2  # Larger chunks for longer playback
            
            logger.info("🎵 Starting audio playback (5 second tone)")
            logger.info("🎤 You can speak to test interrupt, or wait 3 seconds for automatic interrupt")
            
            # Queue audio chunks for playback
            for i in range(0, len(test_audio), chunk_size):
                chunk = test_audio[i:i + chunk_size]
                if len(chunk) > 0:
                    await self.session_manager.audio_output_actor.queue_audio_data.remote(chunk)
            
            # Mark AI as speaking
            self.session_manager.ai_is_speaking = True
            
            # Wait 3 seconds, then trigger interrupt
            logger.info("⏰ Waiting 3 seconds before triggering interrupt...")
            await asyncio.sleep(3.0)
            
            # Check if still playing
            queue_size = await self.session_manager.audio_output_actor.get_queue_size.remote()
            is_playing = await self.session_manager.audio_output_actor.is_playback_active.remote()
            
            logger.info(f"📊 Before interrupt: queue_size={queue_size}, is_playing={is_playing}")
            
            if is_playing and queue_size > 0:
                logger.info("🛑 Triggering interrupt...")
                interrupted = await self.session_manager.audio_output_actor.interrupt_playback.remote()
                
                if interrupted:
                    logger.info("✅ Interrupt signal sent")
                    
                    # Wait a moment for interrupt to take effect
                    await asyncio.sleep(0.2)
                    
                    # Check status after interrupt
                    queue_size_after = await self.session_manager.audio_output_actor.get_queue_size.remote()
                    is_empty = await self.session_manager.audio_output_actor.is_queue_empty.remote()
                    
                    logger.info(f"📊 After interrupt: queue_size={queue_size_after}, is_empty={is_empty}")
                    
                    if is_empty:
                        logger.info("🎉 SUCCESS: Audio playback successfully interrupted!")
                        logger.info("🔇 You should notice the audio stopped playing")
                        return True
                    else:
                        logger.warning("⚠️ WARNING: Queue not empty after interrupt")
                        return False
                else:
                    logger.error("❌ Failed to send interrupt signal")
                    return False
            else:
                logger.info("ℹ️ Audio already finished playing")
                return True
                
        except Exception as e:
            logger.error(f"Real interrupt test failed: {e}")
            return False
        finally:
            if self.session_manager:
                await self.session_manager.stop()
    
    async def test_manual_interrupt(self):
        """Test manual interrupt by monitoring voice activity."""
        logger.info("=== Testing Manual Voice Interrupt ===")
        
        try:
            # Start session manager
            self.session_manager = SessionManager(self.config)
            await self.session_manager.start()
            
            # Generate longer audio for manual testing
            test_audio = self.generate_test_audio(10.0, 220.0)  # Lower frequency, longer duration
            chunk_size = self.config.output_audio.chunk * 2
            
            logger.info("🎵 Starting 10-second audio playback")
            logger.info("🎤 SPEAK NOW to test voice interrupt!")
            logger.info("📢 The audio should stop when you start speaking")
            
            # Queue audio chunks
            for i in range(0, len(test_audio), chunk_size):
                chunk = test_audio[i:i + chunk_size]
                if len(chunk) > 0:
                    await self.session_manager.audio_output_actor.queue_audio_data.remote(chunk)
            
            # Mark AI as speaking
            self.session_manager.ai_is_speaking = True
            
            # Monitor for voice activity for 10 seconds
            start_time = time.time()
            interrupted = False
            
            while time.time() - start_time < 10.0:
                # Check voice activity
                voice_active = await self.session_manager.audio_input_actor.get_voice_activity.remote()
                
                if voice_active and self.session_manager.ai_is_speaking:
                    logger.info("🎤 Voice detected! Triggering interrupt...")
                    await self.session_manager.audio_output_actor.interrupt_playback.remote()
                    self.session_manager.ai_is_speaking = False
                    interrupted = True
                    break
                
                await asyncio.sleep(0.1)
            
            if interrupted:
                logger.info("🎉 SUCCESS: Voice interrupt worked!")
                return True
            else:
                logger.info("ℹ️ No voice detected, test completed without interrupt")
                return True
                
        except Exception as e:
            logger.error(f"Manual interrupt test failed: {e}")
            return False
        finally:
            if self.session_manager:
                await self.session_manager.stop()
    
    async def run_all_tests(self):
        """Run all real interrupt tests."""
        logger.info("🚀 Starting real interrupt functionality tests")
        
        tests = [
            ("Real Audio Interrupt", self.test_interrupt_with_real_audio),
            ("Manual Voice Interrupt", self.test_manual_interrupt),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            logger.info(f"\n{'='*60}")
            logger.info(f"Running test: {test_name}")
            logger.info(f"{'='*60}")
            
            try:
                result = await test_func()
                results[test_name] = result
                
                if result:
                    logger.info(f"✅ {test_name} PASSED")
                else:
                    logger.error(f"❌ {test_name} FAILED")
                    
            except Exception as e:
                logger.error(f"❌ {test_name} FAILED with exception: {e}")
                results[test_name] = False
            
            # Wait between tests
            await asyncio.sleep(2)
        
        # Print summary
        logger.info(f"\n{'='*60}")
        logger.info("TEST SUMMARY")
        logger.info(f"{'='*60}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 All tests passed!")
        else:
            logger.warning(f"⚠️ {total - passed} test(s) failed")
        
        return passed == total


async def main():
    """Main test function."""
    test = RealInterruptTest()
    success = await test.run_all_tests()
    
    if success:
        logger.info("🎉 All real interrupt tests completed successfully!")
    else:
        logger.error("❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
