# API 文档

本文档详细介绍了Audio Agent项目的API接口，包括配置系统、核心组件和工具函数。

## 目录

- [配置 API](#配置-api)
- [核心组件 API](#核心组件-api)
  - [AudioInputActor](#audioinputactor)
  - [AudioOutputActor](#audiooutputactor)
  - [DialogClientActor](#dialogclientactor)
  - [SessionManager](#sessionmanager)
- [协议 API](#协议-api)
- [工具函数](#工具函数)

## 配置 API

### `get_config()`

获取全局配置实例。

**返回值**:
- `AudioAgentConfig`: 全局配置实例

**示例**:
```python
from audio_agent.config import get_config

config = get_config()
print(f"输入采样率: {config.input_audio.sample_rate}")
```

### `update_config(**kwargs)`

更新全局配置。

**参数**:
- `**kwargs`: 配置更新字典

**示例**:
```python
from audio_agent.config import update_config

update_config(
    websocket={"app_id": "new_app_id"},
    input_audio={"sample_rate": 16000}
)
```

### `AudioAgentConfig`

主配置类，包含所有子配置。

**属性**:
- `websocket`: WebSocket配置
- `input_audio`: 输入音频配置
- `output_audio`: 输出音频配置
- `session`: 会话配置
- `ray`: Ray集群配置

**示例**:
```python
from audio_agent.config import AudioAgentConfig

custom_config = AudioAgentConfig(
    websocket={"app_id": "custom_app_id"},
    input_audio={"sample_rate": 16000},
    output_audio={"sample_rate": 24000}
)
```

### `WebSocketConfig`

WebSocket连接配置。

**属性**:
- `base_url`: WebSocket基础URL
- `app_id`: 应用ID
- `access_key`: 访问密钥
- `resource_id`: 资源ID
- `app_key`: 应用密钥
- `connect_id`: 连接ID

**方法**:
- `headers`: 获取WebSocket请求头

### `InputAudioConfig` / `OutputAudioConfig`

音频配置类。

**属性**:
- `chunk`: 音频块大小
- `format`: 音频格式
- `channels`: 声道数
- `sample_rate`: 采样率
- `bit_size`: 位深度

### `RayConfig`

Ray集群配置类。

**属性**:
- `address`: Ray集群地址
  - `"auto"`: 自动启动本地集群（默认）
  - `"local"`: 强制本地模式
  - `"ray://host:port"`: 连接到远程集群
- `num_cpus`: 集群CPU核心数限制
- `num_gpus`: 集群GPU数量限制
- `object_store_memory`: 对象存储内存大小（字节）
- `dashboard_host`: Ray仪表板主机地址
- `dashboard_port`: Ray仪表板端口

**示例**:
```python
from audio_agent.config import update_config

# 本地开发配置
update_config(
    ray={
        "address": "auto",
        "num_cpus": 4,
        "dashboard_port": 8265
    }
)

# 连接远程集群
update_config(
    ray={
        "address": "ray://*************:10001",
        "dashboard_host": "0.0.0.0"
    }
)

# 生产环境配置
update_config(
    ray={
        "address": "auto",
        "num_cpus": 16,
        "num_gpus": 4,
        "object_store_memory": 8000000000,  # 8GB
        "dashboard_host": "0.0.0.0",
        "dashboard_port": 8265
    }
)
```

## 核心组件 API

### AudioInputActor

音频输入处理Actor。

#### `start_recording()`

开始录制音频。

**返回值**:
- `bool`: 是否成功启动录制

**示例**:
```python
import ray
from audio_agent.core import AudioInputActor
from audio_agent.config import get_config

config = get_config()
input_actor = AudioInputActor.remote(config.input_audio)
success = ray.get(input_actor.start_recording.remote())
```

#### `stop_recording()`

停止录制音频。

**返回值**:
- `bool`: 是否成功停止录制

#### `get_audio_data()`

获取录制的音频数据。

**返回值**:
- `Optional[bytes]`: 音频数据或None（如果没有可用数据）

#### `is_recording_active()`

检查录制是否处于活动状态。

**返回值**:
- `bool`: 录制是否活动

### AudioOutputActor

音频输出处理Actor。

#### `start_playback()`

开始音频播放。

**返回值**:
- `bool`: 是否成功启动播放

**示例**:
```python
import ray
from audio_agent.core import AudioOutputActor
from audio_agent.config import get_config

config = get_config()
output_actor = AudioOutputActor.remote(config.output_audio)
success = ray.get(output_actor.start_playback.remote())
```

#### `stop_playback()`

停止音频播放。

**返回值**:
- `bool`: 是否成功停止播放

#### `queue_audio_data(audio_data: bytes)`

将音频数据加入播放队列。

**参数**:
- `audio_data`: 要播放的音频数据

**返回值**:
- `bool`: 是否成功加入队列

#### `is_playback_active()`

检查播放是否处于活动状态。

**返回值**:
- `bool`: 播放是否活动

### DialogClientActor

对话客户端Actor，处理WebSocket通信。

#### `connect(session_id: str)`

连接到WebSocket服务。

**参数**:
- `session_id`: 会话ID

**返回值**:
- `bool`: 是否成功连接

**示例**:
```python
import ray
import uuid
from audio_agent.core import DialogClientActor
from audio_agent.config import get_config

config = get_config()
dialog_actor = DialogClientActor.remote(config)
session_id = str(uuid.uuid4())
success = ray.get(dialog_actor.connect.remote(session_id))
```

#### `disconnect()`

断开WebSocket连接。

**返回值**:
- `bool`: 是否成功断开连接

#### `send_audio_data(audio_data: bytes)`

发送音频数据到服务器。

**参数**:
- `audio_data`: 要发送的音频数据

**返回值**:
- `bool`: 是否成功发送

#### `receive_message()`

接收服务器消息。

**返回值**:
- `Optional[Dict[str, Any]]`: 接收到的消息或None（如果没有消息）

### SessionManager

会话管理器，协调所有Actor。

#### `start()`

启动会话管理器和所有Actor。

**示例**:
```python
import asyncio
from audio_agent.core import SessionManager
from audio_agent.config import get_config

async def main():
    config = get_config()
    session_manager = SessionManager(config)
    await session_manager.start()
    
    # 保持运行一段时间
    await asyncio.sleep(60)
    
    # 停止会话
    await session_manager.stop()

asyncio.run(main())
```

#### `stop()`

停止会话管理器和所有Actor。

#### `is_running()`

检查会话管理器是否正在运行。

**返回值**:
- `bool`: 是否正在运行

## 协议 API

### `ProtocolHandler`

协议处理器，用于消息编解码。

#### `encode(data: Dict[str, Any], message_type: MessageType = MessageType.CLIENT_FULL_REQUEST)`

编码消息。

**参数**:
- `data`: 要编码的数据
- `message_type`: 消息类型

**返回值**:
- `bytes`: 编码后的消息

**示例**:
```python
from audio_agent.core import ProtocolHandler, MessageType

handler = ProtocolHandler()
message = {
    "type": "audio",
    "data": "audio_data_hex_string"
}
encoded = handler.encode(message, MessageType.CLIENT_AUDIO_ONLY_REQUEST)
```

#### `decode(message_bytes: bytes)`

解码消息。

**参数**:
- `message_bytes`: 要解码的消息字节

**返回值**:
- `Dict[str, Any]`: 解码后的消息数据

### 消息类型常量

```python
class MessageType(IntEnum):
    CLIENT_FULL_REQUEST = 0b0001
    CLIENT_AUDIO_ONLY_REQUEST = 0b0010
    SERVER_FULL_RESPONSE = 0b1001
    SERVER_ACK = 0b1011
    SERVER_ERROR_RESPONSE = 0b1111
```

## 工具函数

### `encode_message(data: Dict[str, Any], message_type: int = MessageType.CLIENT_FULL_REQUEST)`

直接编码消息（不使用ProtocolHandler）。

**参数**:
- `data`: 要编码的数据
- `message_type`: 消息类型

**返回值**:
- `bytes`: 编码后的消息

### `decode_message(message_bytes: bytes)`

直接解码消息（不使用ProtocolHandler）。

**参数**:
- `message_bytes`: 要解码的消息字节

**返回值**:
- `Dict[str, Any]`: 解码后的消息数据

---

**注意**: 所有异步方法应在异步环境中调用，Ray Actor方法应使用Ray的远程调用语法。
