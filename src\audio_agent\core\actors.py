"""
Ray actors for the audio agent.

This module contains all Ray actors used for parallel processing
in the real-time audio dialog system.
"""

import asyncio
import logging
import queue
import threading
from typing import Optional, Dict, Any, List
import pyaudio
import ray

from ..config import AudioAgentConfig, InputAudioConfig, OutputAudioConfig


logger = logging.getLogger(__name__)


@ray.remote
class AudioInputActor:
    """
    Ray actor for handling audio input.
    
    This actor manages audio recording from the microphone and
    sends audio data to other actors for processing.
    """
    
    def __init__(self, config: InputAudioConfig):
        """
        Initialize the audio input actor.

        Args:
            config: Input audio configuration
        """
        self.config = config
        self.audio = None
        self.stream = None
        self.is_recording = False
        self.audio_queue = queue.Queue()
        self.record_thread = None

        # Voice Activity Detection (VAD) attributes
        self.vad_manager = None
        self.is_voice_active = False
        self._initialize_vad()

        logger.info("AudioInputActor initialized")

    def _initialize_vad(self) -> None:
        """Initialize Voice Activity Detection."""
        try:
            from .vad import VADManager

            self.vad_manager = VADManager(
                vad_model=self.config.vad_model,
                threshold=self.config.vad_threshold,
                window_size_ms=self.config.vad_window_size_ms,
                min_speech_duration_ms=self.config.vad_min_speech_duration_ms
            )
            logger.info(f"VAD initialized with {self.config.vad_model} model")

        except Exception as e:
            logger.error(f"Failed to initialize VAD: {e}")
            self.vad_manager = None
    
    def start_recording(self) -> bool:
        """
        Start audio recording.
        
        Returns:
            True if recording started successfully, False otherwise
        """
        try:
            self.audio = pyaudio.PyAudio()
            
            self.stream = self.audio.open(
                format=self.config.bit_size,
                channels=self.config.channels,
                rate=self.config.sample_rate,
                input=True,
                frames_per_buffer=self.config.chunk
            )
            
            self.is_recording = True
            self.record_thread = threading.Thread(target=self._record_audio)
            self.record_thread.start()
            
            logger.info("Audio recording started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start recording: {e}")
            return False
    
    def stop_recording(self) -> bool:
        """
        Stop audio recording.
        
        Returns:
            True if recording stopped successfully, False otherwise
        """
        try:
            self.is_recording = False
            
            if self.record_thread:
                self.record_thread.join()
            
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
            
            if self.audio:
                self.audio.terminate()
            
            logger.info("Audio recording stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop recording: {e}")
            return False
    
    def get_audio_data(self) -> Optional[bytes]:
        """
        Get audio data from the recording queue.

        Returns:
            Audio data bytes or None if no data available
        """
        try:
            data = self.audio_queue.get_nowait()
            logger.info(f"Got audio data: {len(data)} bytes")
            return data
        except queue.Empty:
            return None
    
    def _record_audio(self) -> None:
        """Internal method to record audio in a separate thread."""
        logger.info("Audio recording thread started")
        audio_count = 0
        while self.is_recording:
            try:
                data = self.stream.read(self.config.chunk, exception_on_overflow=False)
                self.audio_queue.put(data)
                audio_count += 1

                # Process audio for VAD and logging
                if len(data) > 0:
                    import numpy as np
                    audio_int16 = np.frombuffer(data, dtype=np.int16)
                    max_amplitude = np.abs(audio_int16).max()

                    # Voice Activity Detection using professional VAD
                    if self.config.vad_enabled and self.vad_manager:
                        # Convert to float32 for VAD processing
                        audio_float32 = audio_int16.astype(np.float32) / 32768.0
                        should_interrupt, confidence = self.vad_manager.process_audio(
                            audio_float32, self.config.sample_rate
                        )

                        # Update voice activity status
                        self.is_voice_active = should_interrupt

                    # Log every 200 audio chunks to show it's working (reduced frequency)
                    if audio_count % 200 == 0:
                        vad_info = ""
                        if self.config.vad_enabled and self.vad_manager:
                            vad_status = self.vad_manager.get_speech_status()
                            vad_info = f", VAD: {self.is_voice_active}, confidence: {vad_status['avg_confidence']:.2f}"
                        logger.debug(f"Audio recording active: chunk {audio_count}, max amplitude: {max_amplitude}{vad_info}")

                    # Log when there's significant audio activity (higher threshold)
                    if max_amplitude > 1000:  # Higher threshold to reduce noise
                        logger.debug(f"Audio detected: {len(data)} bytes, max amplitude: {max_amplitude}")

                    # Log audio stats less frequently
                    if audio_count % 200 == 0:
                        logger.debug(f"Audio amplitude check: max={max_amplitude}, avg={np.abs(audio_int16).mean():.2f}, min={np.abs(audio_int16).min()}")

                    # Log first few chunks to see if we're getting any data at all
                    if audio_count <= 5:
                        logger.info(f"Initial audio chunk {audio_count}: max={max_amplitude}, avg={np.abs(audio_int16).mean():.2f}")
            except Exception as e:
                logger.error(f"Error recording audio: {e}")
                break
        logger.info("Audio recording thread stopped")
    
    def is_recording_active(self) -> bool:
        """Check if recording is currently active."""
        return self.is_recording

    def get_voice_activity(self) -> bool:
        """
        Get current voice activity status.

        Returns:
            True if voice activity is detected, False otherwise
        """
        return self.is_voice_active

    def get_vad_status(self) -> dict:
        """
        Get detailed VAD status information.

        Returns:
            Dictionary with VAD status details
        """
        if self.vad_manager:
            return self.vad_manager.get_speech_status()
        else:
            return {
                "is_speech_active": self.is_voice_active,
                "vad_model": "disabled",
                "avg_confidence": 0.0
            }



    def reset_vad(self) -> None:
        """Reset voice activity detection state."""
        self.is_voice_active = False
        if self.vad_manager:
            self.vad_manager.reset()
        logger.info("VAD state reset")


@ray.remote
class AudioOutputActor:
    """
    Ray actor for handling audio output.
    
    This actor manages audio playback to speakers.
    """
    
    def __init__(self, config: OutputAudioConfig):
        """
        Initialize the audio output actor.

        Args:
            config: Output audio configuration
        """
        self.config = config
        self.audio = None
        self.stream = None
        self.is_playing = False
        self.audio_queue = queue.Queue()
        self.play_thread = None
        self.interrupt_event = threading.Event()  # Event for interrupting playback

        logger.info("AudioOutputActor initialized")
    
    def start_playback(self) -> bool:
        """
        Start audio playback.
        
        Returns:
            True if playback started successfully, False otherwise
        """
        try:
            self.audio = pyaudio.PyAudio()
            
            self.stream = self.audio.open(
                format=self.config.bit_size,
                channels=self.config.channels,
                rate=self.config.sample_rate,
                output=True,
                frames_per_buffer=self.config.chunk
            )
            
            self.is_playing = True
            self.play_thread = threading.Thread(target=self._play_audio)
            self.play_thread.start()
            
            logger.info("Audio playback started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start playback: {e}")
            return False
    
    def stop_playback(self) -> bool:
        """
        Stop audio playback.
        
        Returns:
            True if playback stopped successfully, False otherwise
        """
        try:
            self.is_playing = False
            
            if self.play_thread:
                self.play_thread.join()
            
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
            
            if self.audio:
                self.audio.terminate()
            
            logger.info("Audio playback stopped")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop playback: {e}")
            return False
    
    def queue_audio_data(self, audio_data: bytes) -> bool:
        """
        Queue audio data for playback.
        
        Args:
            audio_data: Audio data to play
            
        Returns:
            True if data was queued successfully, False otherwise
        """
        try:
            self.audio_queue.put(audio_data)
            return True
        except Exception as e:
            logger.error(f"Failed to queue audio data: {e}")
            return False
    
    def _play_audio(self) -> None:
        """Internal method to play audio in a separate thread."""
        while self.is_playing:
            try:
                # Check for interrupt signal before processing audio
                if self.interrupt_event.is_set():
                    logger.info("Audio playback interrupted")
                    self._clear_audio_queue()
                    self._flush_audio_stream()
                    self.interrupt_event.clear()
                    continue

                # Wait for audio data with timeout
                try:
                    data = self.audio_queue.get(timeout=0.1)

                    # Check for interrupt again before writing audio
                    if self.interrupt_event.is_set():
                        logger.info("Audio playback interrupted before write")
                        self._clear_audio_queue()
                        self._flush_audio_stream()
                        self.interrupt_event.clear()
                        continue

                    # Split audio data into smaller chunks for better interrupt responsiveness
                    chunk_size = self.config.chunk // 4  # Smaller chunks for faster interrupt
                    for i in range(0, len(data), chunk_size):
                        # Check for interrupt before each small chunk
                        if self.interrupt_event.is_set():
                            logger.info("Audio playback interrupted during chunk write")
                            self._clear_audio_queue()
                            self._flush_audio_stream()
                            self.interrupt_event.clear()
                            break

                        chunk = data[i:i + chunk_size]
                        if len(chunk) > 0:
                            self.stream.write(chunk)

                except queue.Empty:
                    continue
            except Exception as e:
                logger.error(f"Error playing audio: {e}")
                break
    
    def is_playback_active(self) -> bool:
        """Check if playback is currently active."""
        return self.is_playing

    def interrupt_playback(self) -> bool:
        """
        Interrupt current audio playback.

        Returns:
            True if interrupt signal was sent successfully, False otherwise
        """
        try:
            self.interrupt_event.set()
            logger.info("Audio playback interrupt signal sent")
            return True
        except Exception as e:
            logger.error(f"Failed to interrupt playback: {e}")
            return False

    def clear_audio_queue(self) -> bool:
        """
        Clear the audio playback queue.

        Returns:
            True if queue was cleared successfully, False otherwise
        """
        try:
            self._clear_audio_queue()
            logger.info("Audio playback queue cleared")
            return True
        except Exception as e:
            logger.error(f"Failed to clear audio queue: {e}")
            return False

    def _clear_audio_queue(self) -> None:
        """Internal method to clear the audio queue."""
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except queue.Empty:
                break

    def _flush_audio_stream(self) -> None:
        """Internal method to flush the audio stream buffer."""
        try:
            if self.stream:
                # Try to stop and restart the stream safely
                if hasattr(self.stream, 'is_active') and self.stream.is_active():
                    self.stream.stop_stream()

                # Small delay to ensure stop takes effect
                import time
                time.sleep(0.02)  # 20ms delay

                # Restart the stream
                if hasattr(self.stream, 'is_stopped') and self.stream.is_stopped():
                    self.stream.start_stream()

                logger.info("Audio stream flushed safely")
        except Exception as e:
            logger.warning(f"Error flushing audio stream (non-critical): {e}")

    def force_stop_playback(self) -> bool:
        """
        Force stop all audio playback immediately.

        This method will:
        1. Set interrupt flag
        2. Clear the queue
        3. Flush the audio stream buffer

        Returns:
            True if successfully stopped, False otherwise
        """
        try:
            # Set interrupt flag immediately
            self.interrupt_event.set()

            # Clear the queue aggressively
            self._clear_audio_queue()

            # Flush the audio stream buffer (safer approach)
            self._flush_audio_stream()

            logger.info("Audio playback force stopped")
            return True

        except Exception as e:
            logger.error(f"Failed to force stop playback: {e}")
            return False

    def is_queue_empty(self) -> bool:
        """Check if the audio queue is empty."""
        return self.audio_queue.empty()

    def get_queue_size(self) -> int:
        """Get the current size of the audio queue."""
        return self.audio_queue.qsize()


@ray.remote
class DialogClientActor:
    """
    Ray actor for handling WebSocket communication with event-driven architecture.

    This actor manages the WebSocket connection to the dialog service
    and handles message exchange following Volcengine's official event system.
    """

    def __init__(self, config: AudioAgentConfig):
        """
        Initialize the dialog client actor.

        Args:
            config: Audio agent configuration
        """
        self.config = config
        self.websocket = None
        self.is_connected = False
        self.session_id = None
        self.message_queue = asyncio.Queue()

        # Event system integration
        self.event_processor = None

        logger.info("DialogClientActor initialized with event system support")

    async def connect(self, session_id: str) -> bool:
        """
        Connect to the WebSocket service with event system integration.

        Args:
            session_id: Session ID for the connection

        Returns:
            True if connected successfully, False otherwise
        """
        try:
            import websockets
            from ..events import EventProcessor

            self.session_id = session_id

            logger.info(f"Connecting to {self.config.websocket.base_url} with headers: {self.config.websocket.headers}")

            self.websocket = await websockets.connect(
                self.config.websocket.base_url,
                additional_headers=self.config.websocket.headers,
                ping_interval=None
            )

            # Try to get logid from response headers (version compatibility)
            try:
                logid = self.websocket.response_headers.get("X-Tt-Logid")
            except AttributeError:
                # For newer websockets versions
                logid = getattr(self.websocket, 'response_headers', {}).get("X-Tt-Logid", "unknown")
            logger.info(f"Dialog server response logid: {logid}")

            # Initialize event processor with reference to this actor
            self.event_processor = EventProcessor(session_id, self.config, self)

            # Start connection and session
            await self._start_connection()
            await self._start_session()
            await self._say_hello()

            self.is_connected = True
            logger.info(f"Dialog client connected successfully with session ID: {session_id}")

            return True

        except Exception as e:
            logger.error(f"Failed to connect to WebSocket: {e}")
            return False

    async def disconnect(self) -> bool:
        """
        Disconnect from the WebSocket service.

        Returns:
            True if disconnected successfully, False otherwise
        """
        try:
            self.is_connected = False

            # Stop event processing
            if self.event_processor:
                self.event_processor.stop_processing()

            if self.websocket:
                await self.websocket.close()

            logger.info("Disconnected from WebSocket service")
            return True

        except Exception as e:
            logger.error(f"Failed to disconnect from WebSocket: {e}")
            return False

    async def start_event_processing(self) -> bool:
        """
        Start the event processing loop.

        Returns:
            True if started successfully, False otherwise
        """
        try:
            if not self.event_processor:
                logger.warning("Event processor not initialized")
                return False

            # Start event processing in background
            asyncio.create_task(self.event_processor.start_processing())
            logger.info("Event processing started")
            return True

        except Exception as e:
            logger.error(f"Failed to start event processing: {e}")
            return False

    async def send_audio_data(self, audio_data: bytes) -> bool:
        """
        Send audio data to the service using ByteDance protocol.

        Args:
            audio_data: Audio data to send

        Returns:
            True if sent successfully, False otherwise
        """
        try:
            if not self.is_connected or not self.websocket:
                return False

            # Create audio message using ByteDance protocol
            from .protocol import generate_header
            import gzip

            # Generate header for audio-only request (message type 200)
            # Use CLIENT_AUDIO_ONLY_REQUEST with NO_SERIALIZATION
            header = generate_header(
                message_type=0b0010,  # CLIENT_AUDIO_ONLY_REQUEST
                serial_method=0b0000  # NO_SERIALIZATION
            )

            task_request = bytearray(header)
            task_request.extend(int(200).to_bytes(4, 'big'))  # message type 200
            task_request.extend((len(self.session_id)).to_bytes(4, 'big'))
            task_request.extend(str.encode(self.session_id))

            # Compress audio data
            payload_bytes = gzip.compress(audio_data)
            task_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            task_request.extend(payload_bytes)

            await self.websocket.send(task_request)
            return True

        except Exception as e:
            logger.error(f"Failed to send audio data: {e}")
            return False

    async def receive_message(self) -> Optional[Dict[str, Any]]:
        """
        Receive a message from the service using ByteDance protocol with event processing.

        Returns:
            Received message data or None if no message available
        """
        try:
            if not self.is_connected or not self.websocket:
                return None

            # Check if there's a message available (non-blocking)
            try:
                message = await asyncio.wait_for(self.websocket.recv(), timeout=0.01)
                decoded_message = self._parse_response(message)

                # Process through event system if available
                if self.event_processor and decoded_message:
                    await self.event_processor.process_websocket_message(decoded_message)

                return decoded_message

            except asyncio.TimeoutError:
                return None

        except Exception as e:
            logger.error(f"Failed to receive message: {e}")
            return None

    def _parse_response(self, res: bytes) -> Dict[str, Any]:
        """Parse response using ByteDance protocol format."""
        try:
            import gzip
            import json

            if isinstance(res, str) or len(res) < 4:
                return {}

            # Parse header
            protocol_version = res[0] >> 4
            header_size = res[0] & 0x0f
            message_type = res[1] >> 4
            message_type_specific_flags = res[1] & 0x0f
            serialization_method = res[2] >> 4
            message_compression = res[2] & 0x0f
            reserved = res[3]

            header_extensions = res[4:header_size * 4]
            payload = res[header_size * 4:]

            result = {}
            payload_msg = None
            payload_size = 0
            start = 0

            # SERVER_FULL_RESPONSE = 0b1001, SERVER_ACK = 0b1011
            if message_type == 0b1001 or message_type == 0b1011:
                result['message_type'] = 'SERVER_FULL_RESPONSE'
                if message_type == 0b1011:
                    result['message_type'] = 'SERVER_ACK'

                # Handle sequence and event flags
                if message_type_specific_flags & 0b0010 > 0:  # NEG_SEQUENCE
                    result['seq'] = int.from_bytes(payload[:4], "big", signed=False)
                    start += 4
                if message_type_specific_flags & 0b0100 > 0:  # MSG_WITH_EVENT
                    result['event'] = int.from_bytes(payload[:4], "big", signed=False)
                    start += 4

                payload = payload[start:]

                # Parse session ID
                if len(payload) >= 4:
                    session_id_size = int.from_bytes(payload[:4], "big", signed=True)
                    if len(payload) >= 4 + session_id_size:
                        session_id = payload[4:session_id_size+4]
                        result['session_id'] = session_id.decode('utf-8', errors='ignore')
                        payload = payload[4 + session_id_size:]

                        # Parse payload
                        if len(payload) >= 4:
                            payload_size = int.from_bytes(payload[:4], "big", signed=False)
                            payload_msg = payload[4:4+payload_size]

            elif message_type == 0b1111:  # SERVER_ERROR_RESPONSE
                result['message_type'] = 'SERVER_ERROR_RESPONSE'
                result['error'] = 'Server error response'

            # Decompress and parse payload if available
            if payload_msg:
                try:
                    if message_compression == 0b0001:  # GZIP
                        payload_msg = gzip.decompress(payload_msg)
                    if serialization_method == 0b0001:  # JSON
                        result['payload'] = json.loads(payload_msg.decode('utf-8'))
                    else:
                        # For binary data (like audio), store in both payload and payload_msg
                        result['payload'] = payload_msg
                        result['payload_msg'] = payload_msg
                except Exception as e:
                    logger.warning(f"Failed to parse payload: {e}")
                    result['payload'] = payload_msg
                    result['payload_msg'] = payload_msg

            return result

        except Exception as e:
            logger.error(f"Failed to parse response: {e}")
            return {}

    def _clean_response_for_logging(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean response data for logging by replacing binary data with size info."""
        if not response_data:
            return response_data

        cleaned = {}
        for k, v in response_data.items():
            if isinstance(v, bytes):
                cleaned[k] = f"<binary_data:{len(v)}bytes>"
            elif isinstance(v, dict):
                # Recursively clean nested dicts
                cleaned[k] = self._clean_response_for_logging(v)
            else:
                cleaned[k] = v
        return cleaned

    async def _start_connection(self) -> None:
        """Start the WebSocket connection."""
        from .protocol import generate_header
        import gzip

        # StartConnection request
        start_connection_request = bytearray(generate_header())
        start_connection_request.extend(int(1).to_bytes(4, 'big'))
        payload_bytes = str.encode("{}")
        payload_bytes = gzip.compress(payload_bytes)
        start_connection_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        start_connection_request.extend(payload_bytes)

        await self.websocket.send(start_connection_request)
        response = await self.websocket.recv()

        # Parse and log the response
        response_data = self._parse_response(response)
        # Clean binary data for logging
        log_data = self._clean_response_for_logging(response_data)
        logger.info(f"StartConnection completed: {log_data}")

    async def _start_session(self) -> None:
        """Start the dialog session."""
        from .protocol import generate_header
        import gzip
        import json

        # Prepare session request using ByteDance format
        request_params = {
            "tts": {
                "audio_config": {
                    "channel": self.config.output_audio.channels,
                    "format": self.config.output_audio.format,
                    "sample_rate": self.config.output_audio.sample_rate
                }
            },
            "dialog": {
                "bot_name": self.config.session.dialog.bot_name,
                "system_role": self.config.session.dialog.system_role,
                "speaking_style": self.config.session.dialog.speaking_style,
                "extra": {
                    "strict_audit": self.config.session.dialog.strict_audit,
                    "audit_response": self.config.session.dialog.audit_response
                }
            }
        }

        payload_bytes = str.encode(json.dumps(request_params))
        payload_bytes = gzip.compress(payload_bytes)

        start_session_request = bytearray(generate_header())
        start_session_request.extend(int(100).to_bytes(4, 'big'))
        start_session_request.extend((len(self.session_id)).to_bytes(4, 'big'))
        start_session_request.extend(str.encode(self.session_id))
        start_session_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        start_session_request.extend(payload_bytes)

        await self.websocket.send(start_session_request)
        response = await self.websocket.recv()

        # Parse and log the response
        response_data = self._parse_response(response)
        # Clean binary data for logging
        log_data = self._clean_response_for_logging(response_data)
        logger.info(f"StartSession completed: {log_data}")

    async def _say_hello(self) -> None:
        """Send Hello message to keep connection alive."""
        from .protocol import generate_header
        from ..config.settings import get_config
        import gzip
        import json

        config = get_config()

        # Send Hello message (message type 300)
        payload = {
            "content": config.session.dialog.greeting_message,
        }

        hello_request = bytearray(generate_header())
        hello_request.extend(int(300).to_bytes(4, 'big'))
        payload_bytes = str.encode(json.dumps(payload))
        payload_bytes = gzip.compress(payload_bytes)
        hello_request.extend((len(self.session_id)).to_bytes(4, 'big'))
        hello_request.extend(str.encode(self.session_id))
        hello_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
        hello_request.extend(payload_bytes)

        await self.websocket.send(hello_request)
        logger.info("Hello message sent")

    async def send_chat_tts_text(self, text: str, start: bool = True, end: bool = True) -> bool:
        """
        Send ChatTTSText event (500) to specify text for TTS synthesis.

        This is the correct way according to Volcengine's official practice:
        When client determines that user query doesn't need chat result,
        client can specify text for audio synthesis directly.

        Args:
            text: Text to be synthesized to audio
            start: Whether this is the start of the text
            end: Whether this is the end of the text

        Returns:
            True if sent successfully, False otherwise
        """
        try:
            from .protocol import generate_header
            import gzip
            import json

            # Send ChatTTSText message (event type 500)
            payload = {
                "start": start,
                "content": text,
                "end": end
            }

            chat_tts_request = bytearray(generate_header())
            chat_tts_request.extend(int(500).to_bytes(4, 'big'))  # ChatTTSText event
            payload_bytes = str.encode(json.dumps(payload))
            payload_bytes = gzip.compress(payload_bytes)
            chat_tts_request.extend((len(self.session_id)).to_bytes(4, 'big'))
            chat_tts_request.extend(str.encode(self.session_id))
            chat_tts_request.extend((len(payload_bytes)).to_bytes(4, 'big'))
            chat_tts_request.extend(payload_bytes)

            await self.websocket.send(chat_tts_request)
            logger.info(f"ChatTTSText sent to server: {text[:50]}...")
            return True

        except Exception as e:
            logger.error(f"Failed to send ChatTTSText: {e}")
            return False

    async def send_tool_result(self, user_input: str, tool_results: list, ai_response: str) -> bool:
        """
        Send tool calling result to server using ChatTTSText (500).

        Args:
            user_input: Original user input
            tool_results: List of tool execution results
            ai_response: Generated AI response based on tool results

        Returns:
            True if sent successfully, False otherwise
        """
        # Use ChatTTSText to directly specify the AI response for TTS
        return await self.send_chat_tts_text(ai_response)



    def is_connection_active(self) -> bool:
        """Check if the WebSocket connection is active."""
        return self.is_connected
