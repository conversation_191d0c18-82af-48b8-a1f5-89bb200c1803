"""
Base classes for the knowledge system.

This module defines the fundamental interfaces for knowledge storage
and retrieval systems.
"""

import json
from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
from datetime import datetime


@dataclass
class KnowledgeItem:
    """A single knowledge item."""
    
    id: str
    title: str
    content: str
    category: str = "general"
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        """Initialize default values."""
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "category": self.category,
            "tags": self.tags,
            "metadata": self.metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "KnowledgeItem":
        """Create from dictionary."""
        created_at = None
        updated_at = None
        
        if data.get("created_at"):
            created_at = datetime.fromisoformat(data["created_at"])
        if data.get("updated_at"):
            updated_at = datetime.fromisoformat(data["updated_at"])
        
        return cls(
            id=data["id"],
            title=data["title"],
            content=data["content"],
            category=data.get("category", "general"),
            tags=data.get("tags", []),
            metadata=data.get("metadata", {}),
            created_at=created_at,
            updated_at=updated_at
        )


class BaseKnowledgeBase(ABC):
    """
    Abstract base class for knowledge bases.
    
    This class defines the interface that all knowledge base
    implementations must follow.
    """
    
    def __init__(self, name: str):
        """
        Initialize the knowledge base.
        
        Args:
            name: Name of the knowledge base
        """
        self.name = name
    
    @abstractmethod
    async def add_item(self, item: KnowledgeItem) -> bool:
        """
        Add a knowledge item to the base.
        
        Args:
            item: Knowledge item to add
            
        Returns:
            True if added successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_item(self, item_id: str) -> Optional[KnowledgeItem]:
        """
        Get a knowledge item by ID.
        
        Args:
            item_id: ID of the item to retrieve
            
        Returns:
            Knowledge item or None if not found
        """
        pass
    
    @abstractmethod
    async def search(self, query: str, max_results: int = 5) -> List[KnowledgeItem]:
        """
        Search for knowledge items.
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            List of matching knowledge items
        """
        pass
    
    @abstractmethod
    async def update_item(self, item: KnowledgeItem) -> bool:
        """
        Update a knowledge item.
        
        Args:
            item: Updated knowledge item
            
        Returns:
            True if updated successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def delete_item(self, item_id: str) -> bool:
        """
        Delete a knowledge item.
        
        Args:
            item_id: ID of the item to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        pass
    
    @abstractmethod
    async def list_items(self, category: Optional[str] = None) -> List[KnowledgeItem]:
        """
        List all knowledge items.
        
        Args:
            category: Optional category filter
            
        Returns:
            List of knowledge items
        """
        pass
    
    @abstractmethod
    async def get_categories(self) -> List[str]:
        """
        Get all available categories.
        
        Returns:
            List of category names
        """
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """
        Clear all knowledge items.
        
        Returns:
            True if cleared successfully, False otherwise
        """
        pass
