"""
Logging utilities for AudioAgent.

This module provides utilities to clean up log messages and avoid
displaying binary data or other sensitive information.
"""

from typing import Any, Dict, List, Union
import logging


def clean_data_for_logging(data: Any, max_str_length: int = 200) -> Any:
    """
    Clean data for logging by replacing binary data and truncating long strings.
    
    Args:
        data: The data to clean
        max_str_length: Maximum length for string values
        
    Returns:
        Cleaned data safe for logging
    """
    if data is None:
        return None
    
    if isinstance(data, bytes):
        return f"<binary_data:{len(data)}bytes>"
    
    if isinstance(data, str):
        if len(data) > max_str_length:
            return f"{data[:max_str_length]}...<truncated:{len(data)}chars>"
        return data
    
    if isinstance(data, dict):
        cleaned = {}
        for k, v in data.items():
            cleaned[k] = clean_data_for_logging(v, max_str_length)
        return cleaned
    
    if isinstance(data, (list, tuple)):
        cleaned = []
        for item in data:
            cleaned.append(clean_data_for_logging(item, max_str_length))
        return type(data)(cleaned)
    
    # For other types (int, float, bool, etc.), return as-is
    return data


def log_websocket_message(logger: logging.Logger, message: Dict[str, Any], direction: str = "received"):
    """
    Log a WebSocket message with proper cleaning.
    
    Args:
        logger: Logger instance
        message: WebSocket message to log
        direction: "received" or "sent"
    """
    cleaned_message = clean_data_for_logging(message)
    logger.info(f"WebSocket {direction}: {cleaned_message}")


def log_audio_data(logger: logging.Logger, data: bytes, context: str = ""):
    """
    Log audio data information without the actual binary content.
    
    Args:
        logger: Logger instance
        data: Audio data bytes
        context: Additional context for the log
    """
    if context:
        logger.debug(f"{context}: Audio data {len(data)} bytes")
    else:
        logger.debug(f"Audio data: {len(data)} bytes")


def log_response_summary(logger: logging.Logger, response: Dict[str, Any]):
    """
    Log a summary of a response without binary data.
    
    Args:
        logger: Logger instance
        response: Response dictionary
    """
    # Create a summary with key information
    summary = {
        "message_type": response.get("message_type"),
        "event": response.get("event"),
        "session_id": response.get("session_id"),
    }
    
    # Add payload info without binary data
    if "payload" in response:
        payload = response["payload"]
        if isinstance(payload, bytes):
            summary["payload"] = f"<binary_data:{len(payload)}bytes>"
        elif isinstance(payload, dict):
            summary["payload"] = clean_data_for_logging(payload)
        else:
            summary["payload"] = payload
    
    if "payload_msg" in response:
        payload_msg = response["payload_msg"]
        if isinstance(payload_msg, bytes):
            summary["payload_msg"] = f"<binary_data:{len(payload_msg)}bytes>"
        else:
            summary["payload_msg"] = payload_msg
    
    logger.info(f"Response: {summary}")


def setup_audio_logging(enable_debug: bool = False):
    """
    Setup logging configuration for audio processing.
    
    Args:
        enable_debug: Whether to enable debug logging for audio
    """
    audio_loggers = [
        "audio_agent.core.actors",
        "audio_agent.core.session_manager",
        "audio_agent.events",
    ]
    
    level = logging.DEBUG if enable_debug else logging.INFO
    
    for logger_name in audio_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)


def create_safe_logger(name: str) -> logging.Logger:
    """
    Create a logger that automatically cleans data for logging.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    logger = logging.getLogger(name)
    
    # Add a custom method for safe logging
    def safe_info(message: str, data: Any = None):
        if data is not None:
            cleaned_data = clean_data_for_logging(data)
            logger.info(f"{message}: {cleaned_data}")
        else:
            logger.info(message)
    
    def safe_debug(message: str, data: Any = None):
        if data is not None:
            cleaned_data = clean_data_for_logging(data)
            logger.debug(f"{message}: {cleaned_data}")
        else:
            logger.debug(message)
    
    # Attach safe methods to logger
    logger.safe_info = safe_info
    logger.safe_debug = safe_debug
    
    return logger


# Convenience functions for common logging patterns
def log_audio_chunk(logger: logging.Logger, chunk_data: bytes, chunk_number: int):
    """Log audio chunk information."""
    logger.debug(f"Audio chunk #{chunk_number}: {len(chunk_data)} bytes")


def log_event_processing(logger: logging.Logger, event_type: str, event_data: Any):
    """Log event processing information."""
    cleaned_data = clean_data_for_logging(event_data)
    logger.info(f"Processing {event_type} event: {cleaned_data}")


def log_websocket_connection(logger: logging.Logger, status: str, details: Dict[str, Any] = None):
    """Log WebSocket connection status."""
    if details:
        cleaned_details = clean_data_for_logging(details)
        logger.info(f"WebSocket {status}: {cleaned_details}")
    else:
        logger.info(f"WebSocket {status}")
