["tests/test_event_system.py::TestEventHandler::test_event_handler_initialization", "tests/test_event_system.py::TestEventHandler::test_handle_server_event", "tests/test_event_system.py::TestEventHandler::test_register_handler", "tests/test_event_system.py::TestEventIntegration::test_event_handler_registration", "tests/test_event_system.py::TestEventIntegration::test_full_event_flow", "tests/test_event_system.py::TestEventProcessor::test_event_processor_initialization", "tests/test_event_system.py::TestEventProcessor::test_process_websocket_message", "tests/test_event_system.py::TestEventTypes::test_event_type_values", "tests/test_event_system.py::TestEventTypes::test_llm_event_creation", "tests/test_event_system.py::TestEventTypes::test_server_event_creation", "tests/test_event_system.py::TestLLMEnhancer::test_enhanced_response_generation", "tests/test_event_system.py::TestLLMEnhancer::test_intent_recognition_general", "tests/test_event_system.py::TestLLMEnhancer::test_intent_recognition_search", "tests/test_event_system.py::TestLLMEnhancer::test_intent_recognition_weather", "tests/test_event_system.py::TestLLMEnhancer::test_llm_enhancer_initialization", "tests/test_event_system.py::TestLLMEnhancer::test_response_generation", "tests/test_event_system.py::TestLLMEnhancer::test_tool_execution", "tests/test_tools.py::TestBaseTool::test_function_schema", "tests/test_tools.py::TestBaseTool::test_parameter_validation", "tests/test_tools.py::TestBaseTool::test_tool_creation", "tests/test_tools.py::TestBaseTool::test_tool_execution", "tests/test_tools.py::TestBuiltinTools::test_calculator_tool", "tests/test_tools.py::TestBuiltinTools::test_datetime_tool", "tests/test_tools.py::TestBuiltinTools::test_web_search_tool", "tests/test_tools.py::TestToolExecutor::test_nonexistent_tool", "tests/test_tools.py::TestToolExecutor::test_tool_calls_format", "tests/test_tools.py::TestToolExecutor::test_tool_execution", "tests/test_tools.py::TestToolRegistry::test_duplicate_registration", "tests/test_tools.py::TestToolRegistry::test_function_schemas", "tests/test_tools.py::TestToolRegistry::test_registry_creation", "tests/test_tools.py::TestToolRegistry::test_tool_registration", "tests/test_tools.py::TestToolRegistry::test_tool_unregistration", "tests/test_tools.py::test_integration", "tests/test_tts_filtering.py::test_tts_filtering_default_type_during_tool_calling", "tests/test_websocket_events.py::TestWebSocketEventFlow::test_asr_to_llm_flow", "tests/test_websocket_events.py::TestWebSocketEventFlow::test_chat_response_handling", "tests/test_websocket_events.py::TestWebSocketEventFlow::test_tts_audio_handling", "tests/test_websocket_events.py::TestWebSocketEventParsing::test_asr_ended_event_parsing", "tests/test_websocket_events.py::TestWebSocketEventParsing::test_asr_response_event_parsing", "tests/test_websocket_events.py::TestWebSocketEventParsing::test_chat_response_event_parsing", "tests/test_websocket_events.py::TestWebSocketEventParsing::test_connection_started_event_parsing", "tests/test_websocket_events.py::TestWebSocketEventParsing::test_session_started_event_parsing", "tests/test_websocket_events.py::TestWebSocketEventParsing::test_tts_response_event_parsing", "tests/test_websocket_events.py::TestWebSocketEventParsing::test_unknown_event_fallback"]